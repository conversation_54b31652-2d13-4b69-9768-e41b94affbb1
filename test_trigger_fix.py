#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试触发图像保存修复的脚本
验证每次触发只保存一帧图像
"""

import sys
import time
import threading
from unittest.mock import Mock, MagicMock
import numpy as np

def test_trigger_frame_counting():
    """测试触发帧计数机制"""
    print("=== 测试触发帧计数机制 ===")
    
    # 模拟CameraOperation类的关键部分
    class MockCameraOperation:
        def __init__(self, camera_id):
            self.n_connect_num = camera_id
            self.frame_count = 0
            self.trigger_frame_count = 0
            self.last_trigger_frame_count = 0
            self.trigger_image_captured = False
            self.waiting_for_trigger_frame = False
            self.trigger_buf_lock = threading.Lock()
            
        def trigger_once(self):
            """模拟软件触发"""
            self.trigger_buf_lock.acquire()
            try:
                self.trigger_image_captured = False
                self.waiting_for_trigger_frame = True
                self.last_trigger_frame_count = self.frame_count
                print(f"Camera {self.n_connect_num}: Software trigger executed, waiting for frame...")
                return 0
            finally:
                self.trigger_buf_lock.release()
        
        def simulate_new_frame(self):
            """模拟新帧到达"""
            self.frame_count += 1
            
            # 检查是否是触发后的第一帧
            is_trigger_frame = False
            if self.waiting_for_trigger_frame and self.frame_count > self.last_trigger_frame_count:
                is_trigger_frame = True
            
            if is_trigger_frame and not self.trigger_image_captured:
                self.trigger_buf_lock.acquire()
                try:
                    self.trigger_image_captured = True
                    self.waiting_for_trigger_frame = False
                    self.trigger_frame_count = self.frame_count
                    print(f"Camera {self.n_connect_num}: Trigger frame captured (frame #{self.frame_count})")
                    return True  # 返回True表示这是触发帧
                finally:
                    self.trigger_buf_lock.release()
            
            return False  # 返回False表示这是普通帧
        
        def is_trigger_image_ready(self):
            return self.trigger_image_captured
        
        def clear_trigger_image(self):
            self.trigger_buf_lock.acquire()
            try:
                self.trigger_image_captured = False
                self.waiting_for_trigger_frame = False
                print(f"Camera {self.n_connect_num}: Trigger image cache cleared")
            finally:
                self.trigger_buf_lock.release()
    
    # 测试场景1：软件触发后只捕获第一帧
    print("\n1. 测试软件触发后只捕获第一帧")
    camera = MockCameraOperation(0)
    
    # 模拟一些普通帧
    for i in range(5):
        camera.simulate_new_frame()
        print(f"  普通帧 #{camera.frame_count}")
    
    # 执行软件触发
    camera.trigger_once()
    
    # 模拟触发后的帧
    trigger_frames_captured = 0
    for i in range(5):
        is_trigger = camera.simulate_new_frame()
        if is_trigger:
            trigger_frames_captured += 1
        print(f"  触发后帧 #{camera.frame_count}, 是否为触发帧: {is_trigger}")
    
    print(f"  结果: 捕获了 {trigger_frames_captured} 个触发帧 (期望: 1)")
    assert trigger_frames_captured == 1, f"应该只捕获1个触发帧，但捕获了{trigger_frames_captured}个"
    
    # 测试场景2：多次触发，每次只捕获一帧
    print("\n2. 测试多次触发，每次只捕获一帧")
    camera.clear_trigger_image()
    
    total_trigger_frames = 0
    for trigger_num in range(3):
        print(f"  第 {trigger_num + 1} 次触发:")
        camera.trigger_once()
        
        trigger_frames_this_round = 0
        for i in range(3):
            is_trigger = camera.simulate_new_frame()
            if is_trigger:
                trigger_frames_this_round += 1
                total_trigger_frames += 1
            print(f"    帧 #{camera.frame_count}, 是否为触发帧: {is_trigger}")
        
        print(f"    本轮捕获触发帧: {trigger_frames_this_round}")
        camera.clear_trigger_image()
    
    print(f"  总结果: 3次触发共捕获了 {total_trigger_frames} 个触发帧 (期望: 3)")
    assert total_trigger_frames == 3, f"3次触发应该捕获3个触发帧，但捕获了{total_trigger_frames}个"
    
    print("✓ 触发帧计数机制测试通过")

def test_hardware_trigger_detection():
    """测试硬件触发检测机制"""
    print("\n=== 测试硬件触发检测机制 ===")
    
    class MockHardwareCameraOperation:
        def __init__(self, camera_id):
            self.n_connect_num = camera_id
            self.frame_count = 0
            self.last_trigger_frame_count = 0
            self.trigger_image_captured = False
            self.is_hardware_trigger_mode = False
            self.hardware_trigger_enabled = False
            self.trigger_buf_lock = threading.Lock()
            
        def enable_hardware_trigger_detection(self):
            self.hardware_trigger_enabled = True
            self.is_hardware_trigger_mode = True
            
        def prepare_for_hardware_trigger(self):
            if self.is_hardware_trigger_mode:
                self.trigger_buf_lock.acquire()
                try:
                    self.trigger_image_captured = False
                    self.last_trigger_frame_count = self.frame_count
                finally:
                    self.trigger_buf_lock.release()
        
        def simulate_hardware_trigger_frame(self):
            """模拟硬件触发帧到达"""
            self.frame_count += 1
            
            # 硬件触发模式下的检测逻辑
            if (self.is_hardware_trigger_mode and self.hardware_trigger_enabled and 
                not self.trigger_image_captured and self.frame_count > self.last_trigger_frame_count):
                
                self.trigger_buf_lock.acquire()
                try:
                    self.trigger_image_captured = True
                    print(f"Camera {self.n_connect_num}: Hardware trigger frame captured (frame #{self.frame_count})")
                    return True
                finally:
                    self.trigger_buf_lock.release()
            
            return False
        
        def is_trigger_image_ready(self):
            return self.trigger_image_captured
        
        def clear_trigger_image(self):
            self.trigger_buf_lock.acquire()
            try:
                self.trigger_image_captured = False
                print(f"Camera {self.n_connect_num}: Hardware trigger image cleared")
            finally:
                self.trigger_buf_lock.release()
    
    # 测试硬件触发检测
    print("\n1. 测试硬件触发检测")
    camera = MockHardwareCameraOperation(0)
    
    # 启用硬件触发
    camera.enable_hardware_trigger_detection()
    camera.prepare_for_hardware_trigger()
    
    # 模拟硬件触发帧
    trigger_frames_captured = 0
    for i in range(5):
        is_trigger = camera.simulate_hardware_trigger_frame()
        if is_trigger:
            trigger_frames_captured += 1
        print(f"  帧 #{camera.frame_count}, 是否为硬件触发帧: {is_trigger}")
    
    print(f"  结果: 捕获了 {trigger_frames_captured} 个硬件触发帧 (期望: 1)")
    assert trigger_frames_captured == 1, f"应该只捕获1个硬件触发帧，但捕获了{trigger_frames_captured}个"
    
    print("✓ 硬件触发检测机制测试通过")

def test_multi_camera_synchronization():
    """测试多相机同步"""
    print("\n=== 测试多相机同步 ===")
    
    # 创建多个相机
    cameras = [MockCameraOperation(i) for i in range(4)]
    
    class MockCameraOperation:
        def __init__(self, camera_id):
            self.n_connect_num = camera_id
            self.frame_count = 0
            self.trigger_frame_count = 0
            self.last_trigger_frame_count = 0
            self.trigger_image_captured = False
            self.waiting_for_trigger_frame = False
            self.trigger_buf_lock = threading.Lock()
            
        def trigger_once(self):
            self.trigger_buf_lock.acquire()
            try:
                self.trigger_image_captured = False
                self.waiting_for_trigger_frame = True
                self.last_trigger_frame_count = self.frame_count
                return 0
            finally:
                self.trigger_buf_lock.release()
        
        def simulate_new_frame(self):
            self.frame_count += 1
            is_trigger_frame = False
            if self.waiting_for_trigger_frame and self.frame_count > self.last_trigger_frame_count:
                is_trigger_frame = True
            
            if is_trigger_frame and not self.trigger_image_captured:
                self.trigger_buf_lock.acquire()
                try:
                    self.trigger_image_captured = True
                    self.waiting_for_trigger_frame = False
                    self.trigger_frame_count = self.frame_count
                    return True
                finally:
                    self.trigger_buf_lock.release()
            return False
        
        def is_trigger_image_ready(self):
            return self.trigger_image_captured
    
    cameras = [MockCameraOperation(i) for i in range(4)]
    
    # 同时触发所有相机
    print("\n1. 同时触发所有相机")
    for camera in cameras:
        camera.trigger_once()
    
    # 模拟帧到达
    total_trigger_frames = 0
    for frame_num in range(3):
        print(f"  帧 #{frame_num + 1}:")
        for camera in cameras:
            is_trigger = camera.simulate_new_frame()
            if is_trigger:
                total_trigger_frames += 1
            print(f"    Camera {camera.n_connect_num}: 触发帧={is_trigger}")
    
    print(f"  结果: 4个相机共捕获了 {total_trigger_frames} 个触发帧 (期望: 4)")
    assert total_trigger_frames == 4, f"4个相机应该各捕获1个触发帧，但总共捕获了{total_trigger_frames}个"
    
    print("✓ 多相机同步测试通过")

def main():
    """主测试函数"""
    print("=== 触发图像保存修复验证测试 ===\n")
    
    try:
        # 测试1: 触发帧计数机制
        test_trigger_frame_counting()
        
        # 测试2: 硬件触发检测
        test_hardware_trigger_detection()
        
        # 测试3: 多相机同步
        test_multi_camera_synchronization()
        
        print("\n" + "="*50)
        print("✓ 所有测试通过！")
        print("✓ 触发图像保存修复验证成功")
        print("✓ 确保每次触发只保存一帧图像")
        print("="*50)
        
        return True
        
    except AssertionError as e:
        print(f"\n❌ 测试失败: {e}")
        return False
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
