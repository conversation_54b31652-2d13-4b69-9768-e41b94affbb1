import open3d as o3d
import numpy as np
from scipy.spatial import KDTree
import os
import traceback

def filter_ply_file(input_path, output_path, keep_frame=True):
    print("输入文件路径：", input_path)
    print("输出文件路径：", output_path)

    # 读取点云
    pcd = o3d.io.read_point_cloud(input_path)

    if not pcd.has_points():
        print("错误：无法读取点云或点云为空")
        return

    print("点云点数：", len(pcd.points))

    # 统计滤波去除明显离群点
    cl, ind = pcd.remove_statistical_outlier(nb_neighbors=50, std_ratio=2.0)
    pcd = pcd.select_by_index(ind)
    print("统计滤波后点云点数：", len(pcd.points))

    if keep_frame:
        points = np.asarray(pcd.points)
        kdtree = KDTree(points)
        distances, _ = kdtree.query(points, k=10)
        avg_distances = np.mean(distances, axis=1)
        threshold = np.percentile(avg_distances, 30)
        frame_mask = avg_distances < threshold
        frame_pcd = pcd.select_by_index(np.where(frame_mask)[0])
        inner_pcd = pcd.select_by_index(np.where(~frame_mask)[0])
        print("框点云点数：", len(frame_pcd.points))
        print("内部点云点数：", len(inner_pcd.points))

        with o3d.utility.VerbosityContextManager(o3d.utility.VerbosityLevel.Debug):
            labels = np.array(frame_pcd.cluster_dbscan(eps=30, min_points=10, print_progress=True))
        print("DBSCAN 聚类标签：", labels)

        if len(labels) > 0:
            max_label = labels.max()
            largest_cluster_idx = np.where(labels == np.argmax(np.bincount(labels[labels >= 0])))[0]
            filtered_pcd = frame_pcd.select_by_index(largest_cluster_idx)
        else:
            filtered_pcd = frame_pcd
    else:
        filtered_pcd = pcd

    print("最终滤波点云点数：", len(filtered_pcd.points))

    filtered_pcd = filtered_pcd.voxel_down_sample(voxel_size=5)
    print("降采样后点云点数：", len(filtered_pcd.points))

    if len(filtered_pcd.points) == 0:
        print("错误：最终滤波点云为空，无法保存文件")
        return

    try:
        o3d.io.write_point_cloud(output_path, filtered_pcd)
        print(f"滤波完成，结果已保存到 {output_path}")
    except Exception as e:
        print(f"保存文件时出错：{e}")
        traceback.print_exc()

# 定义一个可调用的滤波函数
def filter_point_cloud(input_ply, output_ply, keep_frame=True):
    """
    对点云文件进行滤波处理。
    :param input_ply: 输入的点云文件路径
    :param output_ply: 输出的滤波后点云文件路径
    :param keep_frame: 是否保留框结构
    """
    filter_ply_file(input_ply, output_ply, keep_frame)

# 使用示例
if __name__ == "__main__":
    input_ply = "/opt/MVS/Samples/aarch64/Python_2hk/MultipleCameras1/plane_fit/surface_raw_1754900831126.ply"
    output_ply = os.path.abspath("filtered.ply")
    filter_point_cloud(input_ply, output_ply, keep_frame=True)