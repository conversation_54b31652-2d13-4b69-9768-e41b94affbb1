# -*- coding: utf-8 -*-
import sys
import time
import os
import threading
from queue import Queue
import numpy as np
import cv2

from PyQt5.QtWidgets import *
from PyQt5.QtGui import QTextCursor, QKeySequence
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from CamOperation_class import CameraOperation
from MvCameraControl_class import *
from MvErrorDefine_const import *
from CameraParams_header import *
from PyUIMultipleCameras import Ui_MainWindow
import ctypes

# 导入YOLO
try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
    print("YOLO库加载成功")
except ImportError as e:
    YOLO_AVAILABLE = False
    print(f"YOLO库未安装: {e}")
    print("请安装: pip install ultralytics")

# 立体视觉处理类 - 基于ceshiliti代码的立体匹配方法
# 标定参数: 2448x2048分辨率, 基线100.88mm, 焦距~2348px
# 立体匹配方法已与ceshiliti代码保持一致
class StereoVisionProcessor(QObject):
    # 定义信号用于更新UI
    depth_result_signal = pyqtSignal(np.ndarray, np.ndarray, np.ndarray)  # disparity, depth, depth_color

    def __init__(self):
        super().__init__()
        self.calibration_params = None
        self.rectification_maps = None
        self.Q_matrix = None
        self.calibration_available = False

        # 立体匹配器
        self.stereo_matcher = self.create_stereo_matcher()

        # 图像队列用于同步
        self.left_image_queue = Queue(maxsize=2)
        self.right_image_queue = Queue(maxsize=2)

        # 处理标志
        self.processing = False
        self.process_thread = None

        # 存储最新的深度图
        self.latest_depth = None
        self.latest_disparity = None
        self.depth_lock = threading.Lock()

        # 图像保存控制参数
        self.save_input_images = True      # 是否保存原始输入图像
        self.save_rectified_images = True  # 是否保存校正后图像
        self.save_comparison_images = True # 是否保存对比图像
        self.image_save_interval = 5       # 图像保存间隔（秒）
        self.last_save_time = 0           # 上次保存时间

        # YOLO模型和参数
        self.yolo_model = None
        self.yolo_enabled = False
        self.yolo_confidence = 0.5
        self.yolo_iou = 0.45
        self.yolo_target_classes = None
        self.yolo_first_mode = False

        # 尝试初始化标定参数
        try:
            self.init_calibration_params()
        except Exception as e:
            print(f"Warning: Calibration initialization failed: {e}")
            print("Using simplified stereo processing without calibration")
            self.calibration_available = False

        # 初始化YOLO处理器
        self.init_yolo_processor()

    def init_yolo_processor(self):
        """初始化YOLO模型"""
        if not YOLO_AVAILABLE:
            print("YOLO库不可用，跳过YOLO初始化")
            self.yolo_enabled = False
            return

        try:
            model_path = "best.pt"
            if not os.path.exists(model_path):
                print(f"YOLO模型文件不存在: {model_path}")
                self.yolo_enabled = False
                return

            print(f"正在加载YOLO模型: {model_path}")
            self.yolo_model = YOLO(model_path)

            # 测试模型
            test_image = np.zeros((640, 640, 3), dtype=np.uint8)
            results = self.yolo_model(test_image, verbose=False)

            self.yolo_enabled = True
            print("YOLO模型加载成功")

            # 打印模型信息
            if hasattr(self.yolo_model, 'names'):
                print(f"模型支持的类别数量: {len(self.yolo_model.names)}")
                print(f"类别列表: {list(self.yolo_model.names.values())}")

                # 保存类别信息
                try:
                    with open("model_classes.txt", "w", encoding="utf-8") as f:
                        f.write("YOLO模型类别信息\n")
                        f.write("=" * 30 + "\n")
                        for class_id, class_name in self.yolo_model.names.items():
                            f.write(f"ID {class_id}: {class_name}\n")
                    print("类别信息已保存到: model_classes.txt")
                except:
                    pass

        except Exception as e:
            print(f"YOLO模型初始化失败: {e}")
            self.yolo_enabled = False

    def detect_objects(self, image):
        """使用YOLO检测物体"""
        if not self.yolo_enabled or self.yolo_model is None:
            return []

        try:
            # YOLO推理
            results = self.yolo_model(
                image,
                conf=self.yolo_confidence,
                iou=self.yolo_iou,
                verbose=False
            )

            detections = []
            if len(results) > 0 and results[0].boxes is not None:
                boxes = results[0].boxes

                for i in range(len(boxes)):
                    # 获取边界框坐标
                    x1, y1, x2, y2 = boxes.xyxy[i].cpu().numpy().astype(int)
                    confidence = float(boxes.conf[i].cpu().numpy())
                    class_id = int(boxes.cls[i].cpu().numpy())

                    # 类别过滤
                    if self.yolo_target_classes is not None and class_id not in self.yolo_target_classes:
                        continue

                    # 获取类别名称
                    class_name = self.yolo_model.names[class_id] if hasattr(self.yolo_model, 'names') else f"class_{class_id}"

                    detection = {
                        'bbox': [x1, y1, x2, y2],
                        'confidence': confidence,
                        'class_id': class_id,
                        'class_name': class_name
                    }
                    detections.append(detection)

            return detections

        except Exception as e:
            print(f"YOLO检测错误: {e}")
            return []

    def create_detection_mask(self, image_shape, detections):
        """根据检测结果创建掩码"""
        mask = np.zeros(image_shape[:2], dtype=np.uint8)

        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            # 确保坐标在图像范围内
            x1 = max(0, min(x1, image_shape[1] - 1))
            y1 = max(0, min(y1, image_shape[0] - 1))
            x2 = max(0, min(x2, image_shape[1] - 1))
            y2 = max(0, min(y2, image_shape[0] - 1))

            # 在检测区域设置掩码
            mask[y1:y2, x1:x2] = 255

        return mask

    def filter_depth_by_detections(self, depth_image, detections):
        """根据检测结果过滤深度图"""
        if len(detections) == 0:
            return np.zeros_like(depth_image)

        # 创建检测掩码
        mask = self.create_detection_mask(depth_image.shape, detections)

        # 应用掩码到深度图
        filtered_depth = depth_image.copy()
        filtered_depth[mask == 0] = 0  # 非检测区域设为0

        return filtered_depth

    def draw_detections(self, image, detections):
        """在图像上绘制检测结果"""
        result_image = image.copy()

        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            confidence = detection['confidence']
            class_name = detection['class_name']

            # 绘制边界框
            cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)

            # 绘制标签
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            cv2.rectangle(result_image, (x1, y1 - label_size[1] - 10),
                         (x1 + label_size[0], y1), (0, 255, 0), -1)
            cv2.putText(result_image, label, (x1, y1 - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)

        return result_image

    def filter_pointcloud_by_detections(self, points_3d, color_image, detections):
        """根据检测结果过滤点云"""
        if len(detections) == 0:
            return np.array([]), np.array([])

        # 创建检测掩码
        mask = self.create_detection_mask(points_3d.shape, detections)

        # 过滤有效的3D点
        valid_depth_mask = (points_3d[:, :, 2] >= 1500) & (points_3d[:, :, 2] <= 8000)
        combined_mask = (mask == 255) & valid_depth_mask

        if not np.any(combined_mask):
            return np.array([]), np.array([])

        # 提取过滤后的点云
        filtered_points = points_3d[combined_mask]

        # 提取对应的颜色
        if color_image is not None and color_image.shape[:2] == points_3d.shape[:2]:
            if len(color_image.shape) == 3:
                color_rgb = cv2.cvtColor(color_image, cv2.COLOR_BGR2RGB)
                filtered_colors = color_rgb[combined_mask]
            else:
                gray_rgb = np.stack([color_image[combined_mask]] * 3, axis=1)
                filtered_colors = gray_rgb
        else:
            filtered_colors = np.full((len(filtered_points), 3), 255, dtype=np.uint8)

        return filtered_points, filtered_colors

    def set_yolo_params(self, confidence=0.5, iou=0.45, target_classes=None):
        """设置YOLO检测参数"""
        self.yolo_confidence = confidence
        self.yolo_iou = iou
        self.yolo_target_classes = target_classes
        print(f"YOLO参数已更新: 置信度={confidence}, IoU={iou}, 目标类别={target_classes}")

    def create_stereo_matcher(self):
        """创建立体匹配器 - 基于ceshiliti代码的方法"""
        # 使用与ceshiliti代码相同的参数设置
        stereo = cv2.StereoSGBM_create(
            minDisparity=0,
            numDisparities=96,  # 增加视差范围
            blockSize=7,  # 增加块大小
            P1=8 * 3 * 7 ** 2,
            P2=32 * 3 * 7 ** 2,
            disp12MaxDiff=1,
            uniquenessRatio=10,
            speckleWindowSize=100,
            speckleRange=32,
            preFilterCap=63,
            mode=cv2.STEREO_SGBM_MODE_SGBM_3WAY
        )
        return stereo

    def init_calibration_params(self):
        """初始化相机标定参数"""
        try:
            print("Initializing stereo calibration parameters...")

            # 尝试从JSON文件加载真实标定参数
            try:
                import json
                with open('stereo_calibration.json', 'r') as f:
                    calib_data = json.load(f)

                print("Loading calibration parameters from stereo_calibration.json")

                # 加载真实的标定参数
                self.camera_matrix_left = np.array(calib_data['camera_matrix_left'], dtype=np.float64)
                self.camera_matrix_right = np.array(calib_data['camera_matrix_right'], dtype=np.float64)

                # 畸变系数
                self.dist_coeffs_left = np.array(calib_data['dist_coeffs_left'], dtype=np.float64)
                self.dist_coeffs_right = np.array(calib_data['dist_coeffs_right'], dtype=np.float64)

                # 立体标定参数
                self.R = np.array(calib_data['R'], dtype=np.float64)
                self.T = np.array(calib_data['T'], dtype=np.float64)

                # 图像尺寸
                self.image_size = tuple(calib_data['image_size'])  # (2448, 2048)
                self.baseline_mm = calib_data['baseline_mm']

                print(f"Loaded calibration: Image size {self.image_size}, Baseline {self.baseline_mm:.2f}mm")

            except (FileNotFoundError, KeyError, json.JSONDecodeError) as e:
                print(f"Failed to load calibration file: {e}")
                print("Using provided calibration parameters...")

                # 使用您提供的真实标定参数
                self.camera_matrix_left = np.array([
                    [2349.599303017811, 0.0, 1221.0886985822297],
                    [0.0, 2347.04087849075, 1021.2297950652342],
                    [0.0, 0.0, 1.0]
                ], dtype=np.float64)

                self.camera_matrix_right = np.array([
                    [2347.7080632127045, 0.0, 1219.3168735048296],
                    [0.0, 2347.528871737054, 1010.4282529230558],
                    [0.0, 0.0, 1.0]
                ], dtype=np.float64)

                # 畸变系数 - 转换为正确的形状
                self.dist_coeffs_left = np.array([
                    [-0.0677072434895743, 0.16840134514589222, -0.00013311325437381048,
                     -0.0010946605867930416, -0.19743756744235746]
                ], dtype=np.float64).reshape(5, 1)

                self.dist_coeffs_right = np.array([
                    [-0.07691265481784593, 0.22703604995053306, 0.00015959041360151294,
                     -0.0011580170802655745, -0.3538743014783903]
                ], dtype=np.float64).reshape(5, 1)

                # 立体标定参数
                self.R = np.array([
                    [0.9998155478060632, -0.0008436404567199599, -0.01918746038540903],
                    [0.0008172502236645439, 0.9999987094440285, -0.0013831891951225166],
                    [0.019188602537281753, 0.0013672531065493177, 0.9998149469534894]
                ], dtype=np.float64)

                self.T = np.array([
                    [-100.87040766250446],
                    [0.06079718879422688],
                    [-1.3284405860235702]
                ], dtype=np.float64)

                # 图像尺寸和基线距离
                self.image_size = (2448, 2048)  # 使用您提供的真实图像尺寸
                self.baseline_mm = 100.87917323555243  # 使用您提供的真实基线距离

                print(f"Using provided calibration: Image size {self.image_size}, Baseline {self.baseline_mm:.2f}mm")

            # 尝试计算校正映射，如果失败则跳过
            try:
                self.compute_rectification_maps()
                self.calibration_available = True
                print("Calibration parameters initialized successfully")
            except Exception as calib_error:
                print(f"Calibration mapping failed: {calib_error}")
                print("Will use simplified stereo processing")
                self.calibration_available = False
                # 设置基于真实标定参数的简化Q矩阵
                # 使用真实的主点坐标和焦距
                cx = 1220.0  # 主点x坐标的平均值
                cy = 1015.0  # 主点y坐标的平均值
                fx = 2348.0  # 焦距的平均值
                baseline = 100.88  # 真实基线距离(mm)

                self.Q_matrix = np.array([
                    [1.0, 0.0, 0.0, -cx],
                    [0.0, 1.0, 0.0, -cy],
                    [0.0, 0.0, 0.0, fx],
                    [0.0, 0.0, -1.0/baseline, 0.0]
                ], dtype=np.float64)

        except Exception as e:
            print(f"Error initializing calibration parameters: {e}")
            # 设置默认值避免崩溃
            self.camera_matrix_left = None
            self.camera_matrix_right = None
            self.dist_coeffs_left = None
            self.dist_coeffs_right = None
            self.R = None
            self.T = None
            self.Q_matrix = None
            self.calibration_available = False

    def compute_rectification_maps(self):
        """计算校正映射"""
        try:
            # 检查参数是否有效
            if (self.camera_matrix_left is None or self.camera_matrix_right is None or
                self.dist_coeffs_left is None or self.dist_coeffs_right is None or
                self.R is None or self.T is None):
                print("Calibration parameters not properly initialized")
                return

            # 使用真实的图像尺寸
            image_size = getattr(self, 'image_size', (640, 480))

            # 确保所有参数都是正确的数据类型
            R1, R2, P1, P2, self.Q_matrix, _, _ = cv2.stereoRectify(
                self.camera_matrix_left.astype(np.float64),
                self.dist_coeffs_left.astype(np.float64),
                self.camera_matrix_right.astype(np.float64),
                self.dist_coeffs_right.astype(np.float64),
                image_size,
                self.R.astype(np.float64),
                self.T.astype(np.float64)
            )

            self.map1_left, self.map2_left = cv2.initUndistortRectifyMap(
                self.camera_matrix_left.astype(np.float64),
                self.dist_coeffs_left.astype(np.float64),
                R1, P1, image_size, cv2.CV_16SC2
            )

            self.map1_right, self.map2_right = cv2.initUndistortRectifyMap(
                self.camera_matrix_right.astype(np.float64),
                self.dist_coeffs_right.astype(np.float64),
                R2, P2, image_size, cv2.CV_16SC2
            )

        except Exception as e:
            print(f"Error computing rectification maps: {e}")
            # 设置为None避免后续错误
            self.map1_left = None
            self.map2_left = None
            self.map1_right = None
            self.map2_right = None
            self.Q_matrix = None

    def rectify_images(self, left_img, right_img):
        """校正图像"""
        try:
            # 如果没有标定参数，直接返回原图像
            if not self.calibration_available:
                return left_img, right_img

            # 检查映射是否有效
            if (self.map1_left is None or self.map2_left is None or
                self.map1_right is None or self.map2_right is None):
                print("Rectification maps not available, returning original images")
                return left_img, right_img

            left_rectified = cv2.remap(left_img, self.map1_left, self.map2_left, cv2.INTER_LINEAR)
            right_rectified = cv2.remap(right_img, self.map1_right, self.map2_right, cv2.INTER_LINEAR)
            return left_rectified, right_rectified
        except Exception as e:
            print(f"Error rectifying images: {e}")
            return left_img, right_img

    def compute_disparity(self, left_img, right_img):
        """计算视差图 - 基于ceshiliti代码的方法"""
        # 转换为灰度图
        if len(left_img.shape) == 3:
            left_gray = cv2.cvtColor(left_img, cv2.COLOR_BGR2GRAY)
            right_gray = cv2.cvtColor(right_img, cv2.COLOR_BGR2GRAY)
        else:
            left_gray = left_img
            right_gray = right_img

        # 校正图像
        left_rect, right_rect = self.rectify_images(left_gray, right_gray)

        # 计算视差
        disparity = self.stereo_matcher.compute(left_rect, right_rect)

        # 转换为浮点数并归一化
        disparity = disparity.astype(np.float32) / 16.0

        return disparity, left_rect, right_rect

    def disparity_to_depth(self, disparity):
        """将视差转换为深度 - 基于ceshiliti代码的方法"""
        if self.Q_matrix is None:
            return None

        # 使用Q矩阵重投影到3D
        points_3d = cv2.reprojectImageTo3D(disparity, self.Q_matrix)

        # 提取深度信息（Z坐标）
        depth = points_3d[:, :, 2]

        # 过滤无效深度值
        depth[depth <= 0] = 0
        depth[depth > 5000] = 0  # 限制最大深度为5米

        return depth

    def create_depth_colormap(self, depth):
        """创建深度图的彩色可视化 - 基于ceshiliti代码的方法"""
        # 归一化深度值到0-255
        depth_norm = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX)
        depth_norm = depth_norm.astype(np.uint8)

        # 应用彩色映射
        depth_color = cv2.applyColorMap(depth_norm, cv2.COLORMAP_JET)

        # 将无效区域设为黑色
        mask = depth <= 0
        depth_color[mask] = [0, 0, 0]

        return depth_color

    def compute_depth(self, disparity):
        """从视差计算深度 - 根据真实标定参数优化"""
        try:
            if self.Q_matrix is None:
                print("Q matrix not available for depth computation")
                return None, None

            # 重投影到3D
            points_3d = cv2.reprojectImageTo3D(disparity, self.Q_matrix)
            depth = points_3d[:, :, 2]

            # 根据真实标定参数过滤深度值
            # 基线: 100.88mm, 焦距: ~2348像素
            # 理论最小深度 = (基线 * 焦距) / 最大视差
            # 理论最大深度 = (基线 * 焦距) / 最小视差
            depth[depth <= 0] = 0
            depth[depth < 1500] = 0    # 最小深度1.5米 (考虑实际应用场景)
            depth[depth > 8000] = 0    # 最大深度8米 (根据视差精度限制)

            # 过滤异常值 (深度变化过大的点)
            depth_median = np.median(depth[depth > 0])
            if depth_median > 0:
                depth_std = np.std(depth[depth > 0])
                depth[np.abs(depth - depth_median) > 3 * depth_std] = 0

            return depth, points_3d
        except Exception as e:
            print(f"Error computing depth: {e}")
            return None, None

    def add_image_pair(self, left_img, right_img):
        """添加图像对到处理队列"""
        try:
            if not self.left_image_queue.full():
                self.left_image_queue.put(left_img, block=False)
            if not self.right_image_queue.full():
                self.right_image_queue.put(right_img, block=False)
        except:
            pass

    def start_processing(self):
        """启动处理线程"""
        if not self.processing:
            self.processing = True
            self.process_thread = threading.Thread(target=self.processing_loop)
            self.process_thread.daemon = True
            self.process_thread.start()

            # 启动YOLO处理
            if self.yolo_enabled and self.yolo_processor:
                self.yolo_processor.start_processing()

    def stop_processing(self):
        """停止处理"""
        self.processing = False
        if self.process_thread:
            self.process_thread.join()

        # 停止YOLO处理
        if self.yolo_enabled and self.yolo_processor:
            self.yolo_processor.stop_processing()

    def processing_loop(self):
        """处理循环"""
        while self.processing:
            try:
                if not self.left_image_queue.empty() and not self.right_image_queue.empty():
                    left_img = self.left_image_queue.get()
                    right_img = self.right_image_queue.get()
                    self.process_stereo_pair(left_img, right_img)
                else:
                    time.sleep(0.01)
            except Exception as e:
                print(f"Stereo processing error: {e}")
                time.sleep(0.1)

    def process_stereo_pair(self, left_img, right_img):
        """处理立体图像对 - 基于ceshiliti代码的方法"""
        try:
            current_time = time.time()

            # 控制图像保存频率，避免过于频繁保存
            should_save_images = (current_time - self.last_save_time) >= self.image_save_interval

            if should_save_images:
                # 保存原始立体图像对（立体匹配前）
                if self.save_input_images:
                    self.save_stereo_input_images(left_img, right_img)

                self.last_save_time = current_time

            print("开始处理立体图像对...")

            # 计算视差（包含图像校正）
            disparity, left_rect, right_rect = self.compute_disparity(left_img, right_img)
            print(f"视差图计算完成，尺寸: {disparity.shape}")

            if should_save_images:
                # 保存校正后的立体图像对
                if self.save_rectified_images:
                    self.save_rectified_images(left_rect, right_rect)

            # 计算深度
            depth = self.disparity_to_depth(disparity)

            if depth is not None:
                print(f"深度图计算完成，深度范围: {np.min(depth):.2f} - {np.max(depth):.2f}")

                # 创建深度彩色图
                depth_color = self.create_depth_colormap(depth)

                # 更新最新结果
                with self.depth_lock:
                    self.latest_disparity = disparity.copy()
                    self.latest_depth = depth.copy()

                # 保存深度图和视差图到文件
                self.save_depth_image(depth_color)

                # 生成并保存点云（仅PLY格式）
                # 重新计算3D点用于点云生成
                points_3d = cv2.reprojectImageTo3D(disparity, self.Q_matrix) if self.Q_matrix is not None else None
                if points_3d is not None:
                    self.save_point_cloud(points_3d, left_rect, format='ply')

                # 发射信号更新UI
                self.depth_result_signal.emit(disparity, depth, depth_color)

                # YOLO处理（如果启用）
                if self.yolo_enabled and self.yolo_processor:
                    # 将图像和深度数据发送给YOLO处理器
                    self.yolo_processor.add_image_depth_pair(left_rect, depth, points_3d)

                # 返回处理结果
                return {
                    'disparity': disparity,
                    'depth': depth,
                    'depth_color': depth_color,
                    'points_3d': points_3d,
                    'left_rectified': left_rect,
                    'right_rectified': right_rect
                }

        except Exception as e:
            print(f"立体处理错误: {e}")



    def set_image_save_settings(self, save_input=True, save_rectified=True, save_comparison=True, interval=5):
        """设置图像保存参数
        Args:
            save_input: 是否保存原始输入图像
            save_rectified: 是否保存校正后图像
            save_comparison: 是否保存对比图像
            interval: 保存间隔（秒）
        """
        self.save_input_images = save_input
        self.save_rectified_images = save_rectified
        self.save_comparison_images = save_comparison
        self.image_save_interval = interval
        print(f"图像保存设置已更新: 输入图像={save_input}, 校正图像={save_rectified}, 对比图像={save_comparison}, 间隔={interval}秒")

    def force_save_current_pair(self, left_img, right_img):
        """强制保存当前图像对（忽略时间间隔限制）"""
        try:
            print("强制保存当前立体图像对...")

            # 保存原始图像
            if self.save_input_images:
                self.save_stereo_input_images(left_img, right_img)

            # 校正并保存校正图像
            left_rectified, right_rectified = self.rectify_images(left_img, right_img)
            if self.save_rectified_images:
                self.save_rectified_images(left_rectified, right_rectified)

            print("强制保存完成")

        except Exception as e:
            print(f"强制保存图像对错误: {e}")

    def save_depth_image(self, depth_color):
        """保存深度图像"""
        try:
            save_dir = "depth_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            filename = f"depth_{timestamp}.png"
            filepath = os.path.join(save_dir, filename)
            cv2.imwrite(filepath, depth_color)
        except Exception as e:
            print(f"Error saving depth image: {e}")

    def save_point_cloud(self, points_3d, color_image=None, format='ply'):
        """保存点云数据
        Args:
            points_3d: 3D点云数据 (H, W, 3)
            color_image: 彩色图像用于纹理 (H, W, 3)
            format: 保存格式 (仅支持 'ply')
        """
        try:
            save_dir = "point_clouds"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 提取有效的3D点
            height, width = points_3d.shape[:2]
            total_pixels = height * width

            # 过滤有效点（深度值在1.5m-8m范围内，根据真实标定参数调整）
            valid_mask = (points_3d[:, :, 2] >= 1500) & (points_3d[:, :, 2] <= 8000)
            valid_count = np.sum(valid_mask)

            # 计算点云覆盖率
            coverage_percentage = (valid_count / total_pixels) * 100

            if not np.any(valid_mask):
                print("No valid 3D points found for point cloud generation")
                return

            # 提取有效的XYZ坐标
            valid_points = points_3d[valid_mask]

            # 统计深度范围
            depth_values = valid_points[:, 2]
            min_depth = np.min(depth_values)
            max_depth = np.max(depth_values)
            mean_depth = np.mean(depth_values)

            # 提取对应的颜色信息
            if color_image is not None and color_image.shape[:2] == (height, width):
                if len(color_image.shape) == 3:
                    # BGR转RGB
                    color_rgb = cv2.cvtColor(color_image, cv2.COLOR_BGR2RGB)
                    valid_colors = color_rgb[valid_mask]
                else:
                    # 灰度图转RGB
                    gray_rgb = np.stack([color_image[valid_mask]] * 3, axis=1)
                    valid_colors = gray_rgb
            else:
                # 默认白色
                valid_colors = np.full((len(valid_points), 3), 255, dtype=np.uint8)

            # 输出详细统计信息
            print(f"=== 点云生成统计 ===")
            print(f"图像分辨率: {width} x {height} ({total_pixels:,} 像素)")
            print(f"有效点云数量: {valid_count:,} 点")
            print(f"点云覆盖率: {coverage_percentage:.2f}%")
            print(f"深度范围: {min_depth:.1f}mm - {max_depth:.1f}mm (有效范围: 1500-8000mm)")
            print(f"平均深度: {mean_depth:.1f}mm")
            print(f"基线距离: {getattr(self, 'baseline_mm', 'Unknown')}mm")
            print(f"相机内参 - 左相机焦距: fx={getattr(self, 'camera_matrix_left', [[0]])[0][0]:.1f}px")
            print(f"相机内参 - 右相机焦距: fx={getattr(self, 'camera_matrix_right', [[0]])[0][0]:.1f}px")

            # 深度范围检查提示
            if min_depth < 1500 or max_depth > 8000:
                print(f"警告: 检测到超出预期范围的深度值，建议检查标定参数或场景设置")

            # 保存为PLY格式
            self._save_ply(valid_points, valid_colors, save_dir, timestamp)

        except Exception as e:
            print(f"Error saving point cloud: {e}")

    def save_stereo_input_images(self, left_img, right_img):
        """保存立体匹配前的原始图像对"""
        try:
            save_dir = "stereo_input_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 保存左图像
            left_filename = f"stereo_left_input_{timestamp}.png"
            left_filepath = os.path.join(save_dir, left_filename)
            cv2.imwrite(left_filepath, left_img)

            # 保存右图像
            right_filename = f"stereo_right_input_{timestamp}.png"
            right_filepath = os.path.join(save_dir, right_filename)
            cv2.imwrite(right_filepath, right_img)

            print(f"立体输入图像已保存: {left_filename}, {right_filename}")

            # 创建并保存拼接图像用于对比显示
            self.save_stereo_comparison_image(left_img, right_img, save_dir, timestamp, "input")

        except Exception as e:
            print(f"保存立体输入图像错误: {e}")

    def save_rectified_images(self, left_rectified, right_rectified):
        """保存校正后的立体图像对"""
        try:
            save_dir = "stereo_rectified_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 保存左校正图像
            left_filename = f"stereo_left_rectified_{timestamp}.png"
            left_filepath = os.path.join(save_dir, left_filename)
            cv2.imwrite(left_filepath, left_rectified)

            # 保存右校正图像
            right_filename = f"stereo_right_rectified_{timestamp}.png"
            right_filepath = os.path.join(save_dir, right_filename)
            cv2.imwrite(right_filepath, right_rectified)

            print(f"立体校正图像已保存: {left_filename}, {right_filename}")

            # 创建并保存拼接图像用于对比显示
            self.save_stereo_comparison_image(left_rectified, right_rectified, save_dir, timestamp, "rectified")

        except Exception as e:
            print(f"保存立体校正图像错误: {e}")

    def save_stereo_comparison_image(self, left_img, right_img, save_dir, timestamp, image_type):
        """创建并保存立体图像对比图（左右拼接+水平线）"""
        try:
            # 确保两个图像尺寸一致
            if left_img.shape != right_img.shape:
                print(f"警告: 左右图像尺寸不一致 - 左: {left_img.shape}, 右: {right_img.shape}")
                # 调整到相同尺寸
                min_height = min(left_img.shape[0], right_img.shape[0])
                min_width = min(left_img.shape[1], right_img.shape[1])
                left_img = left_img[:min_height, :min_width]
                right_img = right_img[:min_height, :min_width]

            # 水平拼接左右图像
            stereo_pair = np.hstack((left_img, right_img))

            # 添加水平参考线（用于检查校正效果）
            height = stereo_pair.shape[0]
            width = stereo_pair.shape[1]

            # 在图像上绘制水平参考线
            line_color = (0, 255, 0) if len(stereo_pair.shape) == 3 else 255  # 绿色或白色
            line_thickness = 2

            # 绘制多条水平线
            for y in range(height // 10, height, height // 10):
                cv2.line(stereo_pair, (0, y), (width, y), line_color, line_thickness)

            # 在中间添加分割线
            middle_x = width // 2
            cv2.line(stereo_pair, (middle_x, 0), (middle_x, height), (0, 0, 255), 3)  # 红色分割线

            # 添加文字标注
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 2.0
            font_thickness = 3
            text_color = (255, 255, 255) if len(stereo_pair.shape) == 3 else 255

            # 左图标注
            cv2.putText(stereo_pair, "LEFT", (50, 80), font, font_scale, text_color, font_thickness)
            # 右图标注
            cv2.putText(stereo_pair, "RIGHT", (middle_x + 50, 80), font, font_scale, text_color, font_thickness)

            # 保存对比图像
            comparison_filename = f"stereo_comparison_{image_type}_{timestamp}.png"
            comparison_filepath = os.path.join(save_dir, comparison_filename)
            cv2.imwrite(comparison_filepath, stereo_pair)

            print(f"立体对比图像已保存: {comparison_filename}")

        except Exception as e:
            print(f"保存立体对比图像错误: {e}")

    def _save_ply(self, points, colors, save_dir, timestamp):
        """保存PLY格式点云"""
        filename = f"pointcloud_{timestamp}.ply"
        filepath = os.path.join(save_dir, filename)

        with open(filepath, 'w') as f:
            # PLY文件头
            f.write("ply\n")
            f.write("format ascii 1.0\n")
            f.write(f"element vertex {len(points)}\n")
            f.write("property float x\n")
            f.write("property float y\n")
            f.write("property float z\n")
            f.write("property uchar red\n")
            f.write("property uchar green\n")
            f.write("property uchar blue\n")
            f.write("end_header\n")

            # 写入点云数据
            for i in range(len(points)):
                x, y, z = points[i]
                r, g, b = colors[i]
                f.write(f"{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n")

        print(f"Point cloud saved as PLY: {filepath}")

# 将返回的错误码转换为十六进制显示
def ToHexStr(num):
    chaDic = {10: 'a', 11: 'b', 12: 'c', 13: 'd', 14: 'e', 15: 'f'}
    hexStr = ""
    if num < 0:
        num = num + 2 ** 32
    while num >= 16:
        digit = num % 16
        hexStr = chaDic.get(digit, str(digit)) + hexStr
        num //= 16
    hexStr = chaDic.get(num, str(num)) + hexStr
    return hexStr

# Decoding Characters
def decoding_char(c_ubyte_value):
    c_char_p_value = ctypes.cast(c_ubyte_value, ctypes.c_char_p)
    try:
        decode_str = c_char_p_value.value.decode('gbk')  # Chinese characters
    except UnicodeDecodeError:
        decode_str = str(c_char_p_value.value)
    return decode_str


if __name__ == "__main__":

    global deviceList
    deviceList = MV_CC_DEVICE_INFO_LIST()

    global cam_checked_list
    cam_checked_list = []

    global obj_cam_operation
    obj_cam_operation = []

    global win_display_handles
    win_display_handles = []

    global valid_number
    valid_number = 0

    global b_is_open
    b_is_open = False

    global b_is_grab
    b_is_grab = False

    global b_is_trigger
    b_is_trigger = False

    global b_is_software_trigger
    b_is_software_trigger = False

    global b_is_hardware_trigger
    b_is_hardware_trigger = False

    global hardware_trigger_line
    hardware_trigger_line = "Line0"  # 默认硬件触发线

    global hardware_trigger_activation
    hardware_trigger_activation = "RisingEdge"  # 默认上升沿触发

    # 立体视觉相关变量
    global stereo_processor
    stereo_processor = None

    global stereo_enabled
    stereo_enabled = False

    global stereo_thread
    stereo_thread = None

    global stereo_running
    stereo_running = False

    # 触发后立体匹配相关变量
    global trigger_stereo_match
    trigger_stereo_match = False

    global last_trigger_images
    last_trigger_images = {"left": None, "right": None}

    # 自动保存相关变量
    global auto_save_enabled
    auto_save_enabled = True

    global auto_save_interval
    auto_save_interval = 30  # 30秒自动保存一次

    global auto_save_timer
    auto_save_timer = None

    # ch:初始化SDK | en: initialize SDK
    MvCamera.MV_CC_Initialize()

    # print info in ui
    def print_text(str_info):
        ui.textEdit.append(str_info)  # 使用 append 代替手动操作光标

    # 初始化立体视觉
    def init_stereo_vision():
        global stereo_processor
        try:
            print_text("正在初始化立体视觉系统...")
            stereo_processor = StereoVisionProcessor()

            if not stereo_processor:
                print_text("立体视觉初始化失败: 立体视觉处理器未正确初始化")
                return Fals

            # 检查立体视觉处理器是否正确初始化
            if (stereo_processor.camera_matrix_left is None or
                stereo_processor.camera_matrix_right is None):
                print_text("立体视觉初始化失败: 标定参数无效")
                stereo_processor = None
                return False

            # 连接信号
            stereo_processor.depth_result_signal.connect(update_depth_display)

            # 设置默认的图像保存参数
            stereo_processor.set_image_save_settings(
                save_input=True,      # 保存原始输入图像
                save_rectified=True,  # 保存校正后图像
                save_comparison=True, # 保存对比图像
                interval=10           # 每10秒保存一次
            )

            print_text("立体视觉系统初始化完成")
            print_text("图像保存设置: 输入图像=开启, 校正图像=开启, 保存间隔=10秒")
            return True
        except Exception as e:
            print_text(f"立体视觉初始化失败: {e}")
            print_text("将使用简化的立体视觉模式")
            stereo_processor = None
            return False

    def update_depth_display(disparity, depth, depth_color):
        """更新深度显示"""
        try:
            print_text(f"深度图更新: 尺寸 {depth.shape}, 深度范围 {depth.min():.1f}-{depth.max():.1f}mm")
        except Exception as e:
            print_text(f"深度显示更新错误: {e}")

    def process_yolo_first_stereo(left_image, right_image):
        """YOLO优先的立体视觉处理"""
        global stereo_processor

        if not stereo_processor or not stereo_processor.yolo_enabled:
            return None

        try:
            print_text("执行YOLO优先的立体视觉处理...")

            # 1. 先在左图像上进行YOLO检测
            detections = stereo_processor.detect_objects(left_image)

            if len(detections) == 0:
                print_text("未检测到目标物体，跳过立体匹配")
                return None

            print_text(f"检测到 {len(detections)} 个物体，开始区域立体匹配")

            # 2. 对检测区域进行立体匹配
            region_depth, region_points_3d = compute_stereo_for_regions(
                left_image, right_image, detections
            )

            if region_depth is not None:
                # 3. 创建结果
                detection_image = stereo_processor.draw_detections(left_image, detections)
                depth_color = create_filtered_depth_colormap(region_depth)

                # 4. 生成点云
                if region_points_3d is not None:
                    filtered_points, filtered_colors = stereo_processor.filter_pointcloud_by_detections(
                        region_points_3d, left_image, detections
                    )
                else:
                    filtered_points, filtered_colors = np.array([]), np.array([])

                return {
                    'detections': detections,
                    'detection_image': detection_image,
                    'filtered_depth': region_depth,
                    'filtered_depth_color': depth_color,
                    'filtered_points_3d': filtered_points,
                    'filtered_colors': filtered_colors,
                    'processing_mode': 'yolo_first'
                }
            else:
                print_text("区域立体匹配失败")
                return None

        except Exception as e:
            print_text(f"YOLO优先立体处理错误: {e}")
            return None

    def compute_stereo_for_regions(left_image, right_image, detections):
        """为检测区域计算立体匹配"""
        try:
            # 创建输出深度图（初始化为0）
            height, width = left_image.shape[:2]
            region_depth = np.zeros((height, width), dtype=np.float32)
            region_points_3d = np.zeros((height, width, 3), dtype=np.float32)

            # 为每个检测区域单独计算深度
            for i, detection in enumerate(detections):
                x1, y1, x2, y2 = detection['bbox']

                # 确保坐标在图像范围内
                x1 = max(0, min(x1, width - 1))
                y1 = max(0, min(y1, height - 1))
                x2 = max(0, min(x2, width - 1))
                y2 = max(0, min(y2, height - 1))

                if x2 <= x1 or y2 <= y1:
                    continue

                print_text(f"处理检测区域 {i+1}: {detection['class_name']} at ({x1},{y1})-({x2},{y2})")

                # 提取区域图像
                left_roi = left_image[y1:y2, x1:x2]
                right_roi = right_image[y1:y2, x1:x2]

                # 对区域进行立体匹配
                roi_depth = compute_roi_depth(left_roi, right_roi)

                if roi_depth is not None:
                    # 将区域深度放回完整图像
                    region_depth[y1:y2, x1:x2] = roi_depth

                    # 计算3D点（简化版本）
                    if stereo_processor.Q_matrix is not None:
                        for y in range(y1, y2):
                            for x in range(x1, x2):
                                if region_depth[y, x] > 0:
                                    # 简化的3D重投影
                                    region_points_3d[y, x, 0] = (x - width/2) * region_depth[y, x] / 2348.0
                                    region_points_3d[y, x, 1] = (y - height/2) * region_depth[y, x] / 2348.0
                                    region_points_3d[y, x, 2] = region_depth[y, x]

            return region_depth, region_points_3d

        except Exception as e:
            print_text(f"区域立体匹配计算错误: {e}")
            return None, None

    def compute_roi_depth(left_roi, right_roi):
        """计算ROI区域的深度"""
        try:
            # 转换为灰度图
            if len(left_roi.shape) == 3:
                left_gray = cv2.cvtColor(left_roi, cv2.COLOR_BGR2GRAY)
                right_gray = cv2.cvtColor(right_roi, cv2.COLOR_BGR2GRAY)
            else:
                left_gray = left_roi
                right_gray = right_roi

            # 创建立体匹配器（针对小区域优化）
            stereo = cv2.StereoSGBM_create(
                minDisparity=0,
                numDisparities=64,  # 减少视差范围以提高速度
                blockSize=5,        # 减小块大小适应小区域
                P1=8 * 3 * 5 ** 2,
                P2=32 * 3 * 5 ** 2,
                disp12MaxDiff=1,
                uniquenessRatio=10,
                speckleWindowSize=50,
                speckleRange=16,
                preFilterCap=63,
                mode=cv2.STEREO_SGBM_MODE_SGBM_3WAY
            )

            # 计算视差
            disparity = stereo.compute(left_gray, right_gray)
            disparity = disparity.astype(np.float32) / 16.0

            # 转换为深度（简化版本）
            depth = np.zeros_like(disparity)
            valid_mask = disparity > 0
            depth[valid_mask] = (2348.0 * 100.88) / disparity[valid_mask]  # 焦距*基线/视差

            # 过滤异常值
            depth[depth <= 0] = 0
            depth[depth < 1000] = 0    # 最小深度1米
            depth[depth > 10000] = 0   # 最大深度10米

            return depth

        except Exception as e:
            print_text(f"ROI深度计算错误: {e}")
            return None

    def create_filtered_depth_colormap(depth):
        """创建过滤后深度图的彩色可视化"""
        # 归一化深度值
        depth_norm = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX)
        depth_norm = depth_norm.astype(np.uint8)

        # 应用彩色映射
        depth_color = cv2.applyColorMap(depth_norm, cv2.COLORMAP_JET)

        # 将无效区域设为黑色
        mask = depth <= 0
        depth_color[mask] = [0, 0, 0]

        return depth_color

    def filter_pointcloud_by_detections(points_3d, color_image, detections):
        """根据检测结果过滤点云（全局函数版本）"""
        global stereo_processor
        if stereo_processor:
            return stereo_processor.filter_pointcloud_by_detections(points_3d, color_image, detections)
        else:
            return np.array([]), np.array([])

    def display_yolo_detection_result(image_with_detections, detections):
        """在UI界面显示YOLO检测结果"""
        try:
            # 保存检测结果图像到临时文件
            temp_dir = "temp_display"
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            temp_file = os.path.join(temp_dir, "yolo_detection_display.png")
            cv2.imwrite(temp_file, image_with_detections)

            # 在控制台显示检测信息
            print_text("=== YOLO检测结果 ===")
            for i, detection in enumerate(detections):
                class_name = detection['class_name']
                confidence = detection['confidence']
                bbox = detection['bbox']
                print_text(f"{i+1}. {class_name}: {confidence:.3f} at {bbox}")

            print_text(f"检测结果图像已保存到: {temp_file}")
            print_text("您可以打开该文件查看带有检测框的图像")

            return temp_file

        except Exception as e:
            print_text(f"显示YOLO检测结果错误: {e}")
            return None

    def create_yolo_detection_overlay(image, detections):
        """创建带有YOLO检测框的图像覆盖层"""
        try:
            overlay_image = image.copy()

            for detection in detections:
                x1, y1, x2, y2 = detection['bbox']
                confidence = detection['confidence']
                class_name = detection['class_name']

                # 绘制检测框（绿色）
                cv2.rectangle(overlay_image, (x1, y1), (x2, y2), (0, 255, 0), 3)

                # 绘制标签背景
                label = f"{class_name}: {confidence:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                cv2.rectangle(overlay_image, (x1, y1 - label_size[1] - 15),
                             (x1 + label_size[0] + 10, y1), (0, 255, 0), -1)

                # 绘制标签文字（黑色）
                cv2.putText(overlay_image, label, (x1 + 5, y1 - 8),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)

                # 添加置信度条
                confidence_width = int(confidence * 100)
                cv2.rectangle(overlay_image, (x1, y2 + 5), (x1 + confidence_width, y2 + 15),
                             (0, 255, 0), -1)
                cv2.rectangle(overlay_image, (x1, y2 + 5), (x1 + 100, y2 + 15),
                             (0, 255, 0), 2)

            return overlay_image

        except Exception as e:
            print_text(f"创建YOLO检测覆盖层错误: {e}")
            return image

    def save_yolo_detection_images(left_image, detections, save_dir, timestamp):
        """保存YOLO检测相关的图像"""
        try:
            if len(detections) == 0:
                return

            # 创建带检测框的图像
            detection_image = create_yolo_detection_overlay(left_image, detections)

            # 保存原始图像
            original_file = os.path.join(save_dir, f"original_left_{timestamp}.png")
            cv2.imwrite(original_file, left_image)

            # 保存检测结果图像
            detection_info = "_".join([det['class_name'] for det in detections[:3]])
            detection_file = os.path.join(save_dir, f"yolo_detection_{detection_info}_{timestamp}.png")
            cv2.imwrite(detection_file, detection_image)

            # 显示检测结果
            display_yolo_detection_result(detection_image, detections)

            print_text(f"YOLO检测图像已保存:")
            print_text(f"  原始图像: {original_file}")
            print_text(f"  检测结果: {detection_file}")

        except Exception as e:
            print_text(f"保存YOLO检测图像错误: {e}")

def enable_yolo_detection(confidence=0.5, iou=0.45, target_classes=None):
    """启用YOLO检测功能"""
    global stereo_processor
    if stereo_processor and stereo_processor.yolo_enabled:
        stereo_processor.set_yolo_params(confidence, iou, target_classes)
        print_text(f"YOLO检测已启用: 置信度={confidence}, IoU={iou}")
        if target_classes:
            print_text(f"目标类别: {target_classes}")
        else:
            print_text("检测所有类别")
    else:
        print_text("YOLO模型未初始化或不可用")

def set_yolo_target_classes(class_names):
    """设置YOLO检测的目标类别
    Args:
        class_names: 类别名称列表，如 ['person', 'car', 'bicycle']
    """
    global stereo_processor
    if stereo_processor and stereo_processor.yolo_enabled:
        # 将类别名称转换为类别ID
        if hasattr(stereo_processor.yolo_model, 'names'):
            names_dict = stereo_processor.yolo_model.names
            class_ids = []
            for name in class_names:
                for id, model_name in names_dict.items():
                    if model_name.lower() == name.lower():
                        class_ids.append(id)
                        break

            if class_ids:
                stereo_processor.set_yolo_params(target_classes=class_ids)
                print_text(f"YOLO目标类别已设置: {class_names} -> {class_ids}")
            else:
                print_text(f"未找到匹配的类别: {class_names}")
        else:
            print_text("无法获取模型类别信息")
    else:
        print_text("YOLO模型未初始化")

    def set_stereo_image_save_settings(save_input=True, save_rectified=True, interval=5):
        """设置立体图像保存参数"""
        global stereo_processor
        if stereo_processor is not None:
            stereo_processor.set_image_save_settings(save_input, save_rectified, True, interval)
            print_text(f"立体图像保存设置已更新: 输入图像={save_input}, 校正图像={save_rectified}, 间隔={interval}秒")
        else:
            print_text("立体视觉处理器未初始化")

    def force_save_stereo_images():
        """强制保存当前立体图像对"""
        global stereo_processor, obj_cam_operation

        if stereo_processor is None:
            print_text("立体视觉处理器未初始化")
            return

        try:
            # 获取当前左右相机图像
            left_image = get_camera_image(0)
            right_image = get_camera_image(1)

            if left_image is not None and right_image is not None:
                stereo_processor.force_save_current_pair(left_image, right_image)
                print_text("已强制保存当前立体图像对")
            else:
                print_text("无法获取有效的立体图像对")

        except Exception as e:
            print_text(f"强制保存立体图像错误: {e}")

    def display_latest_stereo_images():
        """显示最新保存的立体图像（用于调试和验证）"""
        try:
            import glob

            # 查找最新的输入图像
            input_dir = "stereo_input_images"
            if os.path.exists(input_dir):
                input_files = glob.glob(os.path.join(input_dir, "stereo_comparison_input_*.png"))
                if input_files:
                    latest_input = max(input_files, key=os.path.getctime)
                    print_text(f"最新输入图像对比图: {os.path.basename(latest_input)}")

            # 查找最新的校正图像
            rectified_dir = "stereo_rectified_images"
            if os.path.exists(rectified_dir):
                rectified_files = glob.glob(os.path.join(rectified_dir, "stereo_comparison_rectified_*.png"))
                if rectified_files:
                    latest_rectified = max(rectified_files, key=os.path.getctime)
                    print_text(f"最新校正图像对比图: {os.path.basename(latest_rectified)}")

        except Exception as e:
            print_text(f"显示最新立体图像错误: {e}")

    def get_camera_image(camera_index):
        """获取指定相机的图像"""
        global obj_cam_operation
        try:
            if (camera_index < len(obj_cam_operation) and
                obj_cam_operation[camera_index] != 0 and
                obj_cam_operation[camera_index].buf_save_image is not None):

                frame_info = obj_cam_operation[camera_index].st_frame_info
                if frame_info.nHeight > 0 and frame_info.nWidth > 0:
                    # 转换图像数据为numpy数组
                    image_data = np.frombuffer(
                        obj_cam_operation[camera_index].buf_save_image,
                        dtype=np.uint8
                    )

                    # 根据像素格式重塑图像
                    if len(image_data) >= frame_info.nHeight * frame_info.nWidth:
                        image = image_data[:frame_info.nHeight * frame_info.nWidth].reshape(
                            (frame_info.nHeight, frame_info.nWidth)
                        )
                        return cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                    else:
                        print_text(f"相机{camera_index}图像数据长度不足")
                else:
                    print_text(f"相机{camera_index}未打开或无图像数据")
                return None
                except Exception as e:
                print_text(f"获取相机{camera_index}图像失败: {e}")
                return None


    def start_stereo_vision():
        """启动立体视觉"""
        global stereo_enabled, stereo_processor, stereo_thread, stereo_running

        if not b_is_grab:
            print_text("请先启动相机采集")
            return

        # 检查是否至少选择了两个相机
        selected_count = sum(1 for i in range(4) if i < len(obj_cam_operation) and obj_cam_operation[i] != 0)
        if selected_count < 2:
            print_text("立体视觉需要至少两个相机")
            return

        if stereo_processor is None:
            if not init_stereo_vision():
                return

        # 启动立体处理
        stereo_processor.start_processing()

        # 启动图像获取线程
        if not stereo_running:
            stereo_running = True
            stereo_thread = threading.Thread(target=stereo_image_acquisition_loop)
            stereo_thread.daemon = True
            stereo_thread.start()

        stereo_enabled = True
        print_text("立体视觉已启动")
        print_text("立体图像将自动保存到以下目录:")
        print_text("  - 原始图像: stereo_input_images/")
        print_text("  - 校正图像: stereo_rectified_images/")
        print_text("  - 深度图像: depth_images/")
        print_text("  - 点云数据: point_clouds/ (PLY格式)")
        print_text("可调用 force_save_stereo_images() 强制保存当前图像对")

    def stop_stereo_vision():
        """停止立体视觉"""
        global stereo_enabled, stereo_processor, stereo_running

        stereo_enabled = False
        stereo_running = False

        if stereo_processor:
            stereo_processor.stop_processing()

        print_text("立体视觉已停止")

    def stereo_image_acquisition_loop():
        """立体图像获取循环"""
        global stereo_running, stereo_processor, obj_cam_operation

        while stereo_running:
            try:
                # 获取左右相机图像（假设相机0是左相机，相机1是右相机）
                left_image = get_camera_image(0)
                right_image = get_camera_image(1)

                if left_image is not None and right_image is not None and stereo_processor is not None:
                    # 添加到立体处理队列
                    stereo_processor.add_image_pair(left_image, right_image)

                time.sleep(0.033)  # 约30FPS

            except Exception as e:
                print_text(f"立体图像获取错误: {e}")
                time.sleep(0.1)

    # ch:枚举相机 | en:enum devices
    def enum_devices():
        global deviceList
        global valid_number
        deviceList = MV_CC_DEVICE_INFO_LIST()
        n_layer_type = (MV_GIGE_DEVICE | MV_USB_DEVICE
                        | MV_GENTL_GIGE_DEVICE | MV_GENTL_CAMERALINK_DEVICE
                        | MV_GENTL_CXP_DEVICE | MV_GENTL_XOF_DEVICE)
        ret = MvCamera.MV_CC_EnumDevicesEx2(n_layer_type, deviceList, '', SortMethod_SerialNumber)
        if ret != 0:
            str_error = "Enum devices fail! ret = :" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", str_error, QMessageBox.Ok)
            return ret

        if deviceList.nDeviceNum == 0:
            QMessageBox.warning(mainWindow, "Info", "Find no device", QMessageBox.Ok)
            return ret
        print_text("Find %d devices!" % deviceList.nDeviceNum)

        valid_number = 0
        for i in range(0, 4):
            if (i < deviceList.nDeviceNum) is True:
                serial_number = ""
                model_name = ""
                mvcc_dev_info = cast(deviceList.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
                if mvcc_dev_info.nTLayerType == MV_GIGE_DEVICE or mvcc_dev_info.nTLayerType == MV_GENTL_GIGE_DEVICE:
                    print("\ngige device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    nip1 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0xff000000) >> 24)
                    nip2 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x00ff0000) >> 16)
                    nip3 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x0000ff00) >> 8)
                    nip4 = (mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x000000ff)
                    print("current ip: %d.%d.%d.%d " % (nip1, nip2, nip3, nip4))

                    for per in mvcc_dev_info.SpecialInfo.stGigEInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)

                elif mvcc_dev_info.nTLayerType == MV_USB_DEVICE:
                    print("\nu3v device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_CAMERALINK_DEVICE:
                    print("\nCML device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stCMLInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stCMLInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stCMLInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_CXP_DEVICE:
                    print("\nCXP device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stCXPInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stCXPInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stCXPInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_XOF_DEVICE:
                    print("\nXoF device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stXoFInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stXoFInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stXoFInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)

                button_by_id = cam_button_group.button(i)
                button_by_id.setText("(" + serial_number + ")" + model_name)
                button_by_id.setEnabled(True)
                valid_number = valid_number + 1
            else:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(False)

    def cam_check_box_clicked():
        global cam_checked_list
        cam_checked_list = []
        for i in range(0, 4):
            button = cam_button_group.button(i)
            if button.isChecked() is True:
                cam_checked_list.append(True)
            else:
                cam_checked_list.append(False)

    def enable_ui_controls():
        global b_is_open
        global b_is_grab
        global b_is_trigger
        global b_is_software_trigger
        global b_is_hardware_trigger
        ui.pushButton_enum.setEnabled(not b_is_open)
        ui.pushButton_open.setEnabled(not b_is_open)
        ui.pushButton_close.setEnabled(b_is_open)
        result1 = False if b_is_grab else b_is_open
        result2 = b_is_open if b_is_grab else False
        ui.pushButton_startGrab.setEnabled(result1)
        ui.pushButton_stopGrab.setEnabled(result2)
        ui.pushButton_saveImg.setEnabled(result2)
        ui.radioButton_continuous.setEnabled(b_is_open)
        ui.radioButton_trigger.setEnabled(b_is_open)
        ui.pushButton_setParams.setEnabled(b_is_open)
        ui.lineEdit_gain.setEnabled(b_is_open)
        ui.lineEdit_frameRate.setEnabled(b_is_open)
        ui.lineEdit_exposureTime.setEnabled(b_is_open)
        result3 = b_is_open if b_is_trigger else False
        ui.pushButton_triggerOnce.setEnabled(b_is_software_trigger and result3)
        ui.checkBox_software_trigger.setEnabled(b_is_trigger)
        ui.checkBox_hardware_trigger.setEnabled(b_is_trigger)

    def open_devices():
        global deviceList
        global obj_cam_operation
        global b_is_open
        global valid_number
        global cam_checked_list
        b_checked = 0
        if b_is_open is True:
            return

        if len(cam_checked_list) <= 0:
            print_text("please select a camera !")
            return
        obj_cam_operation = []
        for i in range(0, 4):
            if cam_checked_list[i] is True:
                b_checked = True
                camObj = MvCamera()
                obj_cam_operation.append(CameraOperation(camObj, deviceList, i))
                ret = obj_cam_operation[i].open_device()
                if 0 != ret:
                    obj_cam_operation.pop()
                    print_text("open cam %d fail ret[0x%x]" % (i, ret))
                    continue
                else:
                    b_is_open = True
            else:
                obj_cam_operation.append(0)
        if b_checked is False:
            print_text("please select a camera !")
            return
        if b_is_open is False:
            print_text("no camera opened successfully !")
            return
        else:
            ui.radioButton_continuous.setChecked(True)
            enable_ui_controls()

        for i in range(0, 4):
            if(i < valid_number) is True:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(not b_is_open)

    def software_trigger_check_box_clicked():
        global obj_cam_operation
        global b_is_software_trigger
        global b_is_hardware_trigger
        global hardware_trigger_line
        global hardware_trigger_activation

        if (ui.checkBox_software_trigger.isChecked()) is True:
            b_is_software_trigger = True
            b_is_hardware_trigger = False
            ui.checkBox_hardware_trigger.setChecked(False)

            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_trigger_source("software")
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger source: software  fail! ret = ' + ToHexStr(ret))
                    else:
                        print_text('camera' + str(i) + ' set to software trigger mode')
        else:
            b_is_software_trigger = False

        enable_ui_controls()

    def hardware_trigger_check_box_clicked():
        global obj_cam_operation
        global b_is_hardware_trigger
        global b_is_software_trigger
        global hardware_trigger_line
        global hardware_trigger_activation

        if (ui.checkBox_hardware_trigger.isChecked()) is True:
            b_is_hardware_trigger = True
            b_is_software_trigger = False
            ui.checkBox_software_trigger.setChecked(False)

            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    # 设置硬件触发源
                    ret = obj_cam_operation[i].set_trigger_source("hardware", hardware_trigger_line)
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger source: hardware fail! ret = ' + ToHexStr(ret))
                        continue

                    # 设置触发极性
                    ret = obj_cam_operation[i].set_trigger_activation(hardware_trigger_activation)
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger activation fail! ret = ' + ToHexStr(ret))
                        continue

                    print_text('camera' + str(i) + f' set to hardware trigger mode ({hardware_trigger_line}, {hardware_trigger_activation})')
        else:
            b_is_hardware_trigger = False

        enable_ui_controls()

    def radio_button_clicked(button):
        global obj_cam_operation
        global b_is_trigger
        button_id = raio_button_group.id(button)
        if (button_id == 0) is True:
            b_is_trigger = False
            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_trigger_mode("continuous")
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger mode: continuous fail! ret = ' + ToHexStr(ret))
            enable_ui_controls()

        else:
            b_is_trigger = True
            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_trigger_mode("triggermode")
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger on fail! ret = ' + ToHexStr(ret))
            enable_ui_controls()

    def close_devices():
        global b_is_open
        global obj_cam_operation
        global valid_number

        if b_is_open is False:
            return
        if b_is_grab is True:
            stop_grabbing()
        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].close_device()
                if 0 != ret:
                    print_text('camera' + str(i) + ' close device fail! ret = ' + ToHexStr(ret))

            if i < valid_number:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(True)
        b_is_open = False
        enable_ui_controls()

    def start_grabbing():
        global obj_cam_operation
        global win_display_handles
        global b_is_open
        global b_is_grab

        if (not b_is_open) or (b_is_grab is True):
            return

        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].start_grabbing(i, win_display_handles[i])
                if 0 != ret:
                    print_text('camera' + str(i) + ' start grabbing fail! ret = ' + ToHexStr(ret))
                b_is_grab = True
        enable_ui_controls()

        # 自动启动立体视觉（如果有两个或以上相机）
        selected_count = sum(1 for i in range(4) if i < len(obj_cam_operation) and obj_cam_operation[i] != 0)
        if selected_count >= 2:
            print_text("检测到多个相机，可以启动立体视觉")
            start_stereo_vision()

        # 启动自动保存
        if auto_save_enabled:
            start_auto_save()

    def stop_grabbing():
        global b_is_grab
        global obj_cam_operation
        global b_is_open

        if (not b_is_open) or (b_is_grab is False):
            return

        # 停止立体视觉
        stop_stereo_vision()

        # 停止自动保存
        stop_auto_save()

        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].stop_grabbing()
                if 0 != ret:
                    print_text('camera' + str(i) + ' stop grabbing fail!ret = ' + ToHexStr(ret))
                b_is_grab = False
        enable_ui_controls()

    # ch:存图 | en:save image - 增强版本支持触发图像保存
    def save_bmp():
        global b_is_grab
        global obj_cam_operation
        global last_trigger_images

        # 优先保存触发图像，如果没有则保存当前图像
        if (last_trigger_images["left"] is not None or
            last_trigger_images["right"] is not None):
            print_text("保存触发后的图像...")
            save_trigger_images_manual()
            return

        if b_is_grab is False:
            print_text("无法保存图像：相机未在采集状态")
            return

        try:
            # 创建保存目录
            save_dir = "saved_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            saved_count = 0

            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    # 检查图像缓冲区是否有有效的图像数据
                    if (obj_cam_operation[i].buf_save_image is None or
                        obj_cam_operation[i].st_frame_info is None):
                        print_text(f'camera{i} has no image data to save.')
                        continue

                    frame_info = obj_cam_operation[i].st_frame_info

                    # 检查图像尺寸是否有效
                    if frame_info.nHeight <= 0 or frame_info.nWidth <= 0:
                        print_text(f'camera{i} frame info has invalid dimensions')
                        continue

                    try:
                        # 使用改进的保存方法，传递保存目录
                        ret = obj_cam_operation[i].save_bmp(save_dir)
                        if 0 == ret:
                            saved_count += 1
                            print_text(f'camera{i} image saved successfully to {save_dir}')
                        elif ret != -1:  # -1 表示没有图像数据，不是真正的错误
                            print_text('camera' + str(i) + ' save bmp fail!ret = ' + ToHexStr(ret))

                    except Exception as e:
                        print_text(f'camera{i} save error: {e}')
                        continue

            if saved_count > 0:
                print_text(f"成功保存 {saved_count} 张图像")
            else:
                print_text("未能保存任何图像")

        except Exception as e:
            print_text(f"保存图像错误: {e}")

    def save_trigger_images_manual():
        """手动保存触发后的图像"""
        global last_trigger_images

        try:
            save_dir = "trigger_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            saved_count = 0

            if last_trigger_images["left"] is not None:
                filename = f"trigger_left_{timestamp}.png"
                filepath = os.path.join(save_dir, filename)
                cv2.imwrite(filepath, last_trigger_images["left"])
                saved_count += 1
                print_text(f'Left trigger image saved: {filename}')

            if last_trigger_images["right"] is not None:
                filename = f"trigger_right_{timestamp}.png"
                filepath = os.path.join(save_dir, filename)
                cv2.imwrite(filepath, last_trigger_images["right"])
                saved_count += 1
                print_text(f'Right trigger image saved: {filename}')

            if saved_count > 0:
                print_text(f"成功保存 {saved_count} 张触发图像到 {save_dir}")
                # 清空触发图像缓存
                last_trigger_images = {"left": None, "right": None}
            else:
                print_text("没有触发图像可保存")

        except Exception as e:
            print_text(f"保存触发图像错误: {e}")

    def is_float(str_value):
        try:
            float(str_value)
            return True
        except ValueError:
            return False

    def set_parameters():
        global obj_cam_operation
        global b_is_open
        if b_is_open is False:
            return

        frame_rate = ui.lineEdit_frameRate.text()
        exposure_time = ui.lineEdit_exposureTime.text()
        gain = ui.lineEdit_gain.text()

        if is_float(frame_rate) is False or is_float(exposure_time) is False or is_float(gain) is False:
            print_text("parameters is valid, please check")
            return

        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].set_exposure_time(exposure_time)
                if ret != 0:
                    print_text('camera' + str(i) + ' Set exposure time failed ret:' + ToHexStr(ret))
                ret = obj_cam_operation[i].set_gain(gain)
                if ret != 0:
                    print_text('camera' + str(i) + ' Set gain failed ret:' + ToHexStr(ret))
                ret = obj_cam_operation[i].set_frame_rate(frame_rate)
                if ret != 0:
                    print_text('camera' + str(i) + ' set acquisition frame rate failed ret:' + ToHexStr(ret))

    def software_trigger_once():
        global last_trigger_images

        # 清空上次触发的图像
        last_trigger_images = {"left": None, "right": None}

        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].trigger_once()
                if ret != 0:
                    print_text('camera' + str(i) + 'TriggerSoftware failed ret:' + ToHexStr(ret))
                else:
                    print_text('camera' + str(i) + ' software trigger executed')

        # 等待一段时间让图像采集完成，然后自动保存图像
        QTimer.singleShot(500, capture_and_save_trigger_images)  # 500ms后获取并保存图像

    def capture_and_save_trigger_images():
        """捕获触发后的图像并自动保存"""
        global last_trigger_images, obj_cam_operation

        try:
            print_text("正在捕获触发后的图像...")

            # 创建保存目录
            save_dir = "trigger_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            saved_count = 0

            # 获取并保存所有相机的图像
            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    # 检查图像缓冲区是否有有效的图像数据
                    if (obj_cam_operation[i].buf_save_image is None or
                        obj_cam_operation[i].st_frame_info is None):
                        print_text(f'camera{i} has no image data to save.')
                        continue

                    frame_info = obj_cam_operation[i].st_frame_info

                    # 检查图像尺寸是否有效
                    if frame_info.nHeight <= 0 or frame_info.nWidth <= 0:
                        print_text(f'camera{i} frame info has invalid dimensions')
                        continue

                    try:
                        # 获取图像数据
                        image_data = np.frombuffer(
                            obj_cam_operation[i].buf_save_image,
                            dtype=np.uint8
                        )

                        # 根据像素格式重塑图像
                        if len(image_data) >= frame_info.nHeight * frame_info.nWidth:
                            # 简单处理为灰度图像
                            image = image_data[:frame_info.nHeight * frame_info.nWidth].reshape(
                                (frame_info.nHeight, frame_info.nWidth)
                            )

                            # 保存图像
                            filename = f"trigger_cam{i}_{timestamp}.png"
                            filepath = os.path.join(save_dir, filename)
                            cv2.imwrite(filepath, image)

                            saved_count += 1
                            print_text(f'camera{i} image saved: {filename}')

                            # 如果是前两个相机，保存到立体匹配用的变量中
                            if i == 0:
                                last_trigger_images["left"] = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                            elif i == 1:
                                last_trigger_images["right"] = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)

                    except Exception as e:
                        print_text(f'camera{i} save error: {e}')
                        continue

            if saved_count > 0:
                print_text(f"触发图像保存完成！共保存 {saved_count} 张图像到 {save_dir}")

                # 检查是否成功获取了双目图像，如果是则可以进行立体匹配
                if last_trigger_images["left"] is not None and last_trigger_images["right"] is not None:
                    print_text("双目图像捕获成功，可以进行立体匹配")
                    # 如果启用了自动立体匹配，则自动进行立体匹配
                    if trigger_stereo_match:
                        perform_trigger_stereo_match()
            else:
                print_text("未能保存任何触发图像")

        except Exception as e:
            print_text(f"触发图像捕获错误: {e}")

    def capture_trigger_images():
        """捕获触发后的图像"""
        global last_trigger_images, obj_cam_operation

        try:
            # 获取左相机图像（相机0）
            left_image = get_camera_image(0)
            if left_image is not None:
                last_trigger_images["left"] = left_image.copy()
                print_text("Left camera image captured")

            # 获取右相机图像（相机1）
            right_image = get_camera_image(1)
            if right_image is not None:
                last_trigger_images["right"] = right_image.copy()
                print_text("Right camera image captured")

            # 检查是否成功获取了双目图像
            if last_trigger_images["left"] is not None and last_trigger_images["right"] is not None:
                print_text("Stereo image pair captured successfully")
                # 如果启用了自动立体匹配，则自动进行立体匹配
                if trigger_stereo_match:
                    perform_trigger_stereo_match()
            else:
                print_text("Failed to capture stereo image pair")

        except Exception as e:
            print_text(f"Error capturing trigger images: {e}")

    def perform_trigger_stereo_match():
        """对触发后的图像进行立体匹配（包含YOLO处理）"""
        global last_trigger_images, stereo_processor

        if (last_trigger_images["left"] is None or
            last_trigger_images["right"] is None):
            print_text("No captured images available for stereo matching")
            return

        try:
            if stereo_processor is None:
                if not init_stereo_vision():
                    return

            print_text("Processing stereo matching with YOLO detection...")

            # 处理立体图像对
            result = stereo_processor.process_stereo_pair(
                last_trigger_images["left"],
                last_trigger_images["right"]
            )
            # 如果启用了YOLO，选择处理方式
            if stereo_processor.yolo_enabled:
                if stereo_processor.yolo_first_mode:
                    print_text("Using YOLO-first stereo processing mode...")
                    # YOLO优先模式：先检测，再对区域进行立体匹配
                    yolo_result = process_yolo_first_stereo(
                        last_trigger_images["left"],
                        last_trigger_images["right"]
                    )
                    if yolo_result:
                        print_text(f"YOLO-first processing completed: {len(yolo_result['detections'])} objects")
                        # 将YOLO优先的结果合并到主结果中
                        result.update(yolo_result)
                    else:
                        print_text("YOLO-first processing: No objects detected")
                else:
                    print_text("Using traditional stereo-first processing mode...")

                    # 使用左图像进行YOLO检测
                    detections = stereo_processor.detect_objects(last_trigger_images["left"])

                    if len(detections) > 0:
                        print_text(f"YOLO detected {len(detections)} objects in triggered image")

                        # 显示YOLO检测结果
                        save_yolo_detection_images(last_trigger_images["left"], detections,
                                                   "trigger_stereo_results", int(time.time() * 1000))
                        # 过滤深度图
                        filtered_depth = stereo_processor.filter_depth_by_detections(
                            result['depth'], detections
                        )

                        # 过滤点云
                        if 'points_3d' in result and result['points_3d'] is not None:
                            filtered_points, filtered_colors = filter_pointcloud_by_detections(
                                result['points_3d'], last_trigger_images["left"], detections
                            )
                            result['filtered_points_3d'] = filtered_points
                            result['filtered_colors'] = filtered_colors
                        result['yolo_detections'] = detections
                        result['filtered_depth'] = filtered_depth
                        result['detection_image'] = stereo_processor.draw_detections(
                            last_trigger_images["left"], detections
                        )
                        result['processing_mode'] = 'stereo_first'
                save_stereo_results(last_trigger_images["left"], last_trigger_images["right"], result)
                print_text("Trigger stereo matching completed")

        except Exception as e:
            print_text(f"Error in trigger stereo matching: {e}")

    def save_stereo_results(left_img, right_img, result):
        """保存立体匹配结果（包含YOLO过滤结果）"""
        try:
            save_dir = "trigger_stereo_results"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 保存原始图像
            cv2.imwrite(os.path.join(save_dir, f"left_{timestamp}.png"), left_img)
            cv2.imwrite(os.path.join(save_dir, f"right_{timestamp}.png"), right_img)

            if result:
                # 保存原始深度图和点云
                if 'depth' in result and result['depth'] is not None:
                    depth_norm = cv2.normalize(result['depth'], None, 0, 255, cv2.NORM_MINMAX)
                    depth_color = cv2.applyColorMap(depth_norm.astype(np.uint8), cv2.COLORMAP_JET)
                    cv2.imwrite(os.path.join(save_dir, f"depth_original_{timestamp}.png"), depth_color)

                    # 保存原始点云数据
                    if 'points_3d' in result and result['points_3d'] is not None:
                        print_text("Saving original point cloud data...")
                        save_trigger_point_cloud(result['points_3d'], left_img, save_dir, timestamp, "original")

                # 保存YOLO检测结果
                if 'yolo_detections' in result and len(result['yolo_detections']) > 0:
                    detections = result['yolo_detections']
                    detection_info = "_".join([det['class_name'] for det in detections[:3]])

                    print_text(f"Saving YOLO detection results for: {detection_info}")

                    # 保存检测结果图像
                    if 'detection_image' in result:
                        cv2.imwrite(os.path.join(save_dir, f"yolo_detection_{detection_info}_{timestamp}.png"),
                                   result['detection_image'])

                    # 保存过滤后的深度图
                    if 'filtered_depth' in result and result['filtered_depth'] is not None:
                        filtered_depth_norm = cv2.normalize(result['filtered_depth'], None, 0, 255, cv2.NORM_MINMAX)
                        filtered_depth_color = cv2.applyColorMap(filtered_depth_norm.astype(np.uint8), cv2.COLORMAP_JET)
                        # 将无效区域设为黑色
                        mask = result['filtered_depth'] <= 0
                        filtered_depth_color[mask] = [0, 0, 0]
                        cv2.imwrite(os.path.join(save_dir, f"yolo_filtered_depth_{detection_info}_{timestamp}.png"),
                                   filtered_depth_color)
                        print_text("YOLO filtered depth image saved")

                    # 保存过滤后的点云
                    if ('filtered_points_3d' in result and 'filtered_colors' in result and
                        result['filtered_points_3d'] is not None and len(result['filtered_points_3d']) > 0):
                        print_text(f"Saving YOLO filtered point cloud ({len(result['filtered_points_3d'])} points)...")
                        save_yolo_filtered_point_cloud(
                            result['filtered_points_3d'],
                            result['filtered_colors'],
                            save_dir,
                            timestamp,
                            detection_info
                        )
                        print_text("YOLO filtered point cloud saved")
                    else:
                        print_text("No filtered point cloud data to save")
                        if 'filtered_points_3d' in result:
                            if result['filtered_points_3d'] is None:
                                print_text("  - filtered_points_3d is None")
                            elif len(result['filtered_points_3d']) == 0:
                                print_text("  - filtered_points_3d is empty")
                        else:
                            print_text("  - filtered_points_3d not in result")

                    # 创建对比图
                    create_trigger_comparison_image(save_dir, timestamp, result, detection_info)

                else:
                    print_text("No YOLO detections found, saving only original stereo results")

            print_text(f"Trigger stereo results saved to {save_dir}")

        except Exception as e:
            print_text(f"Error saving trigger stereo results: {e}")

    def save_yolo_filtered_point_cloud(points, colors, save_dir, timestamp, detection_info):
        """保存YOLO过滤后的点云"""
        try:
            filename = f"yolo_filtered_pointcloud_{detection_info}_{timestamp}.ply"
            filepath = os.path.join(save_dir, filename)

            with open(filepath, 'w') as f:
                f.write("ply\n")
                f.write("format ascii 1.0\n")
                f.write(f"element vertex {len(points)}\n")
                f.write("property float x\n")
                f.write("property float y\n")
                f.write("property float z\n")
                f.write("property uchar red\n")
                f.write("property uchar green\n")
                f.write("property uchar blue\n")
                f.write("end_header\n")

                for i in range(len(points)):
                    x, y, z = points[i]
                    r, g, b = colors[i]
                    f.write(f"{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n")

            print_text(f"YOLO filtered point cloud saved: {filename}")

        except Exception as e:
            print_text(f"Error saving YOLO filtered point cloud: {e}")

    def create_trigger_comparison_image(save_dir, timestamp, result, detection_info):
        """创建触发结果对比图"""
        try:
            if ('depth' not in result or 'filtered_depth' not in result or
                result['depth'] is None or result['filtered_depth'] is None):
                return

            # 创建原始深度图的可视化
            depth_norm = cv2.normalize(result['depth'], None, 0, 255, cv2.NORM_MINMAX)
            depth_color_original = cv2.applyColorMap(depth_norm.astype(np.uint8), cv2.COLORMAP_JET)

            # 创建过滤深度图的可视化
            filtered_depth_norm = cv2.normalize(result['filtered_depth'], None, 0, 255, cv2.NORM_MINMAX)
            filtered_depth_color = cv2.applyColorMap(filtered_depth_norm.astype(np.uint8), cv2.COLORMAP_JET)
            mask = result['filtered_depth'] <= 0
            filtered_depth_color[mask] = [0, 0, 0]

            # 确保图像尺寸一致
            h, w = depth_color_original.shape[:2]
            filtered_depth_color = cv2.resize(filtered_depth_color, (w, h))

            # 水平拼接
            comparison = np.hstack([depth_color_original, filtered_depth_color])

            # 添加标签
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 1.0
            color = (255, 255, 255)
            thickness = 2

            cv2.putText(comparison, "Original Depth", (10, 30), font, font_scale, color, thickness)
            cv2.putText(comparison, f"YOLO Filtered ({detection_info})", (w + 10, 30), font, font_scale, color, thickness)

            # 保存对比图
            filename = f"comparison_{detection_info}_{timestamp}.png"
            filepath = os.path.join(save_dir, filename)
            cv2.imwrite(filepath, comparison)

            print_text(f"Comparison image saved: {filename}")

        except Exception as e:
            print_text(f"Error creating comparison image: {e}")

    def save_trigger_point_cloud(points_3d, color_image, save_dir, timestamp, suffix=""):
        """保存触发模式下的点云数据"""
        try:
            # 提取有效的3D点
            height, width = points_3d.shape[:2]
            total_pixels = height * width

            # 过滤有效点（深度值在2m-6m范围内）
            valid_mask = (points_3d[:, :, 2] >= 2000) & (points_3d[:, :, 2] <= 6000)
            valid_count = np.sum(valid_mask)

            # 计算点云覆盖率
            coverage_percentage = (valid_count / total_pixels) * 100

            if not np.any(valid_mask):
                print_text("No valid 3D points found for point cloud generation")
                return

            # 提取有效的XYZ坐标
            valid_points = points_3d[valid_mask]

            # 统计深度范围
            depth_values = valid_points[:, 2]
            min_depth = np.min(depth_values)
            max_depth = np.max(depth_values)
            mean_depth = np.mean(depth_values)

            # 提取对应的颜色信息
            if color_image is not None and color_image.shape[:2] == (height, width):
                if len(color_image.shape) == 3:
                    # BGR转RGB
                    color_rgb = cv2.cvtColor(color_image, cv2.COLOR_BGR2RGB)
                    valid_colors = color_rgb[valid_mask]
                else:
                    # 灰度图转RGB
                    gray_rgb = np.stack([color_image[valid_mask]] * 3, axis=1)
                    valid_colors = gray_rgb
            else:
                # 默认白色
                valid_colors = np.full((len(valid_points), 3), 255, dtype=np.uint8)

            # 输出详细统计信息
            print_text("=== 触发模式点云生成统计 ===")
            print_text(f"图像分辨率: {width} x {height} ({total_pixels:,} 像素)")
            print_text(f"有效点云数量: {valid_count:,} 点")
            print_text(f"点云覆盖率: {coverage_percentage:.2f}%")
            print_text(f"深度范围: {min_depth:.1f}mm - {max_depth:.1f}mm (有效范围: 2000-6000mm)")
            print_text(f"平均深度: {mean_depth:.1f}mm")
            print_text(f"基线距离: 100.88mm")

            # 深度范围检查提示
            if min_depth < 2000 or max_depth > 6000:
                print_text("警告: 检测到超出预期范围的深度值，建议检查标定参数或场景设置")

            # 保存多种格式的点云
            save_point_cloud_file(valid_points, valid_colors, save_dir, timestamp, 'ply', suffix)
            save_point_cloud_file(valid_points, valid_colors, save_dir, timestamp, 'pcd', suffix)
            save_point_cloud_file(valid_points, valid_colors, save_dir, timestamp, 'txt', suffix)

        except Exception as e:
            print_text(f"Error saving trigger point cloud: {e}")

    def save_point_cloud_file(points, colors, save_dir, timestamp, format, suffix=""):
        """保存点云文件的通用函数"""
        try:
            suffix_str = f"_{suffix}" if suffix else ""
            if format.lower() == 'ply':
                filename = f"pointcloud{suffix_str}_{timestamp}.ply"
                filepath = os.path.join(save_dir, filename)

                with open(filepath, 'w') as f:
                    # PLY文件头
                    f.write("ply\n")
                    f.write("format ascii 1.0\n")
                    f.write(f"element vertex {len(points)}\n")
                    f.write("property float x\n")
                    f.write("property float y\n")
                    f.write("property float z\n")
                    f.write("property uchar red\n")
                    f.write("property uchar green\n")
                    f.write("property uchar blue\n")
                    f.write("end_header\n")

                    # 写入点云数据
                    for i in range(len(points)):
                        x, y, z = points[i]
                        r, g, b = colors[i]
                        f.write(f"{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n")

                print_text(f"Point cloud saved as PLY: {filename}")

            elif format.lower() == 'pcd':
                filename = f"pointcloud{suffix_str}_{timestamp}.pcd"
                filepath = os.path.join(save_dir, filename)

                with open(filepath, 'w') as f:
                    # PCD文件头
                    f.write("# .PCD v0.7 - Point Cloud Data file format\n")
                    f.write("VERSION 0.7\n")
                    f.write("FIELDS x y z rgb\n")
                    f.write("SIZE 4 4 4 4\n")
                    f.write("TYPE F F F U\n")
                    f.write("COUNT 1 1 1 1\n")
                    f.write(f"WIDTH {len(points)}\n")
                    f.write("HEIGHT 1\n")
                    f.write("VIEWPOINT 0 0 0 1 0 0 0\n")
                    f.write(f"POINTS {len(points)}\n")
                    f.write("DATA ascii\n")

                    # 写入点云数据
                    for i in range(len(points)):
                        x, y, z = points[i]
                        r, g, b = colors[i]
                        # 将RGB打包为单个32位整数
                        rgb_packed = (int(r) << 16) | (int(g) << 8) | int(b)
                        f.write(f"{x:.6f} {y:.6f} {z:.6f} {rgb_packed}\n")

                print_text(f"Point cloud saved as PCD: {filename}")

            elif format.lower() == 'txt':
                filename = f"pointcloud{suffix_str}_{timestamp}.txt"
                filepath = os.path.join(save_dir, filename)

                with open(filepath, 'w') as f:
                    # 写入文件头注释
                    f.write("# Point Cloud Data\n")
                    f.write("# Format: X Y Z R G B\n")
                    f.write(f"# Points: {len(points)}\n")

                    # 写入点云数据
                    for i in range(len(points)):
                        x, y, z = points[i]
                        r, g, b = colors[i]
                        f.write(f"{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n")

                print_text(f"Point cloud saved as TXT: {filename}")

        except Exception as e:
            print_text(f"Error saving {format} point cloud: {e}")

    def manual_stereo_match():
        """手动触发立体匹配"""
        global trigger_stereo_match

        if (last_trigger_images["left"] is None or
            last_trigger_images["right"] is None):
            print_text("No captured images available. Please trigger cameras first.")
            return

        print_text("Manual stereo matching triggered...")
        perform_trigger_stereo_match()

    def enable_auto_stereo_match():
        """启用自动立体匹配"""
        global trigger_stereo_match
        trigger_stereo_match = True
        print_text("自动立体匹配已启用")

    def disable_auto_stereo_match():
        """禁用自动立体匹配"""
        global trigger_stereo_match
        trigger_stereo_match = False
        print_text("自动立体匹配已禁用")

    def enable_trigger_yolo_detection(confidence=0.5, iou=0.45, target_classes=None):
        """启用触发模式下的YOLO检测功能"""
        global stereo_processor
        if stereo_processor and stereo_processor.yolo_enabled:
            stereo_processor.set_yolo_params(confidence, iou, target_classes)
            print_text(f"触发模式YOLO检测已启用: 置信度={confidence}, IoU={iou}")
            if target_classes:
                print_text(f"目标类别: {target_classes}")
            else:
                print_text("检测所有类别")
            print_text("现在软件/硬件触发后将自动进行YOLO检测和过滤")
        else:
            print_text("YOLO模型未初始化或不可用")

    def set_trigger_yolo_target_classes(class_names):
        """设置触发模式下YOLO检测的目标类别（支持自定义模型）"""
        global stereo_processor
        if stereo_processor and stereo_processor.yolo_enabled:
            if hasattr(stereo_processor.yolo_model, 'names'):
                names_dict = stereo_processor.yolo_model.names
                class_ids = []
                found_names = []

                print_text(f"自定义模型可用类别: {list(names_dict.values())}")

                for name in class_names:
                    for id, model_name in names_dict.items():
                        if model_name.lower() == name.lower():
                            class_ids.append(id)
                            found_names.append(model_name)
                            break

                if class_ids:
                    stereo_processor.set_yolo_params(target_classes=class_ids)
                    print_text(f"触发模式YOLO目标类别已设置: {found_names} -> {class_ids}")
                else:
                    print_text(f"未找到匹配的类别: {class_names}")
                    print_text(f"请从以下类别中选择: {list(names_dict.values())}")
            else:
                print_text("无法获取自定义模型类别信息")
        else:
            print_text("YOLO模型未初始化")

    def trigger_yolo_demo():
        """触发模式YOLO演示"""
        print_text("=== 触发模式YOLO演示 ===")
        print_text("1. 启用自动立体匹配: enable_auto_stereo_match()")
        print_text("2. 启用YOLO检测: enable_trigger_yolo_detection()")
        print_text("3. 设置检测类别: set_trigger_yolo_target_classes(['person', 'car'])")
        print_text("4. 执行软件触发: software_trigger_once()")
        print_text("5. 查看结果: trigger_stereo_results/ 目录")
        print_text("")
        print_text("示例命令序列:")
        print_text("enable_auto_stereo_match()")
        print_text("enable_trigger_yolo_detection(confidence=0.5)")
        print_text("set_trigger_yolo_target_classes(['person', 'car', 'bicycle'])")
        print_text("software_trigger_once()")

    def get_yolo_status():
        """获取YOLO状态信息（包含自定义模型信息）"""
        global stereo_processor
        if stereo_processor:
            if stereo_processor.yolo_enabled:
                print_text("✓ YOLO模型已启用")
                if hasattr(stereo_processor.yolo_model, 'names'):
                    model_names = stereo_processor.yolo_model.names
                    print_text(f"✓ 自定义模型支持 {len(model_names)} 个类别")
                    print_text(f"✓ 类别列表: {list(model_names.values())}")
                print_text(f"✓ 置信度阈值: {stereo_processor.yolo_confidence}")
                print_text(f"✓ IoU阈值: {stereo_processor.yolo_iou}")

                if stereo_processor.yolo_target_classes:
                    target_names = []
                    if hasattr(stereo_processor.yolo_model, 'names'):
                        for class_id in stereo_processor.yolo_target_classes:
                            if class_id in stereo_processor.yolo_model.names:
                                target_names.append(stereo_processor.yolo_model.names[class_id])
                    print_text(f"✓ 目标类别: {target_names} (ID: {stereo_processor.yolo_target_classes})")
                else:
                    print_text("✓ 目标类别: 所有类别")

                # 显示处理模式
                mode = "YOLO优先" if stereo_processor.yolo_first_mode else "立体匹配优先"
                print_text(f"✓ 处理模式: {mode}")
            else:
                print_text("✗ YOLO模型未启用")
        else:
            print_text("✗ 立体视觉处理器未初始化")

    def list_custom_model_classes():
        """列出自定义模型的所有类别"""
        global stereo_processor
        if stereo_processor and stereo_processor.yolo_enabled:
            if hasattr(stereo_processor.yolo_model, 'names'):
                model_names = stereo_processor.yolo_model.names
                print_text("=== 自定义模型类别列表 ===")
                for class_id, class_name in model_names.items():
                    print_text(f"ID {class_id}: {class_name}")
                print_text(f"总计: {len(model_names)} 个类别")
            else:
                print_text("无法获取自定义模型类别信息")
        else:
            print_text("YOLO模型未初始化")

    def set_custom_model_class_by_id(class_ids):
        """通过类别ID设置检测目标（适用于自定义模型）"""
        global stereo_processor
        if stereo_processor and stereo_processor.yolo_enabled:
            if hasattr(stereo_processor.yolo_model, 'names'):
                model_names = stereo_processor.yolo_model.names
                valid_ids = []
                class_names = []

                for class_id in class_ids:
                    if class_id in model_names:
                        valid_ids.append(class_id)
                        class_names.append(model_names[class_id])

                if valid_ids:
                    stereo_processor.set_yolo_params(target_classes=valid_ids)
                    print_text(f"已设置检测类别: {class_names} (ID: {valid_ids})")
                else:
                    print_text(f"无效的类别ID: {class_ids}")
                    print_text(f"有效ID范围: {list(model_names.keys())}")
            else:
                print_text("无法获取自定义模型类别信息")
        else:
            print_text("YOLO模型未初始化")

    def enable_yolo_first_mode():
        """启用YOLO优先模式（先检测，再立体匹配）"""
        global stereo_processor
        if stereo_processor:
            stereo_processor.yolo_first_mode = True
            print_text("✓ YOLO优先模式已启用")
            print_text("处理流程: YOLO检测 → 区域立体匹配 → 深度图生成")
            print_text("优势: 只对检测到的物体区域进行立体匹配，提高效率")
        else:
            print_text("立体视觉处理器未初始化")

    def enable_stereo_first_mode():
        """启用立体匹配优先模式（传统方式）"""
        global stereo_processor
        if stereo_processor:
            stereo_processor.yolo_first_mode = False
            print_text("✓ 立体匹配优先模式已启用")
            print_text("处理流程: 立体匹配 → 深度图生成 → YOLO检测 → 深度过滤")
            print_text("优势: 获得完整深度图，适合需要全局深度信息的应用")
        else:
            print_text("立体视觉处理器未初始化")

    def get_processing_mode():
        """获取当前处理模式"""
        global stereo_processor
        if stereo_processor:
            yolo_first = getattr(stereo_processor, 'yolo_first_mode', False)
            if yolo_first:
                print_text("当前模式: YOLO优先模式")
                print_text("  - 先进行YOLO检测")
                print_text("  - 只对检测区域进行立体匹配")
                print_text("  - 提高处理效率")
            else:
                print_text("当前模式: 立体匹配优先模式")
                print_text("  - 先进行完整立体匹配")
                print_text("  - 再用YOLO结果过滤深度图")
                print_text("  - 获得完整深度信息")
        else:
            print_text("立体视觉处理器未初始化")

    def compare_processing_modes():
        """比较两种处理模式"""
        print_text("=== 处理模式对比 ===")
        print_text("")
        print_text("【YOLO优先模式】")
        print_text("流程: YOLO检测 → 区域立体匹配")
        print_text("优势:")
        print_text("  ✓ 处理速度快（只计算感兴趣区域）")
        print_text("  ✓ 计算资源消耗少")
        print_text("  ✓ 适合实时应用")
        print_text("  ✓ 减少无关区域的噪声")
        print_text("劣势:")
        print_text("  ✗ 只能获得检测物体的深度信息")
        print_text("  ✗ 如果检测失败，无法获得深度图")
        print_text("")
        print_text("【立体匹配优先模式】")
        print_text("流程: 立体匹配 → YOLO检测 → 深度过滤")
        print_text("优势:")
        print_text("  ✓ 获得完整的深度图")
        print_text("  ✓ 即使YOLO检测失败也有深度信息")
        print_text("  ✓ 可以分析整个场景")
        print_text("  ✓ 适合需要全局深度的应用")
        print_text("劣势:")
        print_text("  ✗ 计算量大")
        print_text("  ✗ 处理时间长")
        print_text("  ✗ 包含大量无关深度信息")
        print_text("")
        print_text("切换命令:")
        print_text("  enable_yolo_first_mode()    # 启用YOLO优先")
        print_text("  enable_stereo_first_mode()  # 启用立体优先")

    # 自动保存功能
    def start_auto_save():
        """启动自动保存"""
        global auto_save_timer, auto_save_enabled, auto_save_interval

        if auto_save_enabled and b_is_grab:
            if not auto_save_timer:
                auto_save_timer = QTimer()
                auto_save_timer.timeout.connect(auto_save_images)
            auto_save_timer.start(auto_save_interval * 1000)  # 转换为毫秒
            print_text(f"自动保存已启动，间隔 {auto_save_interval} 秒")

    def stop_auto_save():
        """停止自动保存"""
        global auto_save_timer

        if auto_save_timer:
            auto_save_timer.stop()
            auto_save_timer = None
            print_text("自动保存已停止")

    def auto_save_images():
        """自动保存图像"""
        global b_is_grab, obj_cam_operation

        if not b_is_grab:
            stop_auto_save()
            return

        try:
            # 创建自动保存目录
            save_dir = "auto_saved_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            saved_count = 0

            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    # 检查图像缓冲区是否有有效的图像数据
                    if (obj_cam_operation[i].buf_save_image is None or
                        obj_cam_operation[i].st_frame_info is None):
                        continue

                    frame_info = obj_cam_operation[i].st_frame_info

                    # 检查图像尺寸是否有效
                    if frame_info.nHeight <= 0 or frame_info.nWidth <= 0:
                        continue

                    try:
                        # 获取图像数据
                        image_data = np.frombuffer(
                            obj_cam_operation[i].buf_save_image,
                            dtype=np.uint8
                        )

                        # 根据像素格式重塑图像
                        if len(image_data) >= frame_info.nHeight * frame_info.nWidth:
                            # 简单处理为灰度图像
                            image = image_data[:frame_info.nHeight * frame_info.nWidth].reshape(
                                (frame_info.nHeight, frame_info.nWidth)
                            )

                            # 保存图像
                            filename = f"auto_cam{i}_{timestamp}.png"
                            filepath = os.path.join(save_dir, filename)
                            cv2.imwrite(filepath, image)
                            saved_count += 1

                    except Exception as e:
                        print_text(f'自动保存相机{i}图像错误: {e}')
                        continue

            if saved_count > 0:
                print_text(f"自动保存完成：{saved_count} 张图像")

        except Exception as e:
            print_text(f"自动保存错误: {e}")

    def toggle_auto_save():
        """切换自动保存状态"""
        global auto_save_enabled

        auto_save_enabled = not auto_save_enabled
        if auto_save_enabled:
            print_text("自动保存已启用")
            if b_is_grab:
                start_auto_save()
        else:
            print_text("自动保存已禁用")
            stop_auto_save()

    # 设置硬件触发线
    def set_hardware_trigger_line(line_name):
        global hardware_trigger_line
        global obj_cam_operation
        global b_is_open
        global b_is_software_trigger

        if not b_is_open or b_is_software_trigger:
            hardware_trigger_line = line_name
            print_text(f'Hardware trigger line set to: {line_name}')
            return

        hardware_trigger_line = line_name
        # 如果当前是硬件触发模式，立即应用设置
        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].set_trigger_source("hardware", hardware_trigger_line)
                if 0 != ret:
                    print_text('camera' + str(i) + f' set trigger line to {line_name} fail! ret = ' + ToHexStr(ret))
                else:
                    print_text('camera' + str(i) + f' trigger line set to: {line_name}')

    # 设置硬件触发极性
    def set_hardware_trigger_activation(activation):
        global hardware_trigger_activation
        global obj_cam_operation
        global b_is_open
        global b_is_software_trigger

        if not b_is_open or b_is_software_trigger:
            hardware_trigger_activation = activation
            print_text(f'Hardware trigger activation set to: {activation}')
            return

        hardware_trigger_activation = activation
        # 如果当前是硬件触发模式，立即应用设置
        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].set_trigger_activation(hardware_trigger_activation)
                if 0 != ret:
                    print_text('camera' + str(i) + f' set trigger activation to {activation} fail! ret = ' + ToHexStr(ret))
                else:
                    print_text('camera' + str(i) + f' trigger activation set to: {activation}')

    # ch: 初始化app, 绑定控件与函数 | en: Init app, bind ui and api
    app = QApplication(sys.argv)
    mainWindow = QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(mainWindow)
    ui.pushButton_enum.clicked.connect(enum_devices)
    ui.pushButton_open.clicked.connect(open_devices)
    ui.pushButton_close.clicked.connect(close_devices)
    ui.pushButton_startGrab.clicked.connect(start_grabbing)
    ui.pushButton_stopGrab.clicked.connect(stop_grabbing)
    ui.pushButton_saveImg.clicked.connect(save_bmp)
    ui.pushButton_setParams.clicked.connect(set_parameters)
    ui.checkBox_software_trigger.clicked.connect(software_trigger_check_box_clicked)
    ui.checkBox_hardware_trigger.clicked.connect(hardware_trigger_check_box_clicked)
    ui.pushButton_triggerOnce.clicked.connect(software_trigger_once)

    # 绑定立体匹配按钮（如果UI中有的话）
    if hasattr(ui, 'pushButton_stereo_match'):
        ui.pushButton_stereo_match.clicked.connect(manual_stereo_match)
    cam_button_group = QButtonGroup(mainWindow)
    cam_button_group.addButton(ui.checkBox_1, 0)
    cam_button_group.addButton(ui.checkBox_2, 1)
    cam_button_group.addButton(ui.checkBox_3, 2)
    cam_button_group.addButton(ui.checkBox_4, 3) 

    cam_button_group.setExclusive(False)
    cam_button_group.buttonClicked.connect(cam_check_box_clicked)

    raio_button_group = QButtonGroup(mainWindow)
    raio_button_group.addButton(ui.radioButton_continuous, 0)
    raio_button_group.addButton(ui.radioButton_trigger, 1)
    raio_button_group.buttonClicked.connect(radio_button_clicked)

    win_display_handles.append(ui.widget_display1.winId())
    win_display_handles.append(ui.widget_display2.winId())
    win_display_handles.append(ui.widget_display3.winId())
    win_display_handles.append(ui.widget_display4.winId())

    mainWindow.show()
    enum_devices()
    enable_ui_controls()

    # 初始化立体视觉
    init_stereo_vision()

    app.exec_()

    close_devices()

    # ch:反初始化SDK | en: finalize SDK
    MvCamera.MV_CC_Finalize()

    sys.exit()
