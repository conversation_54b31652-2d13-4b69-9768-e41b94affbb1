# -*- coding: utf-8 -*-
"""
硬件触发立体匹配功能测试脚本
Hardware Trigger Stereo Matching Test Script
"""

import numpy as np
import cv2
import os
import time

def test_stereo_calibration_params():
    """测试立体标定参数"""
    print("=== 测试立体标定参数 ===")
    
    # 示例标定参数（需要替换为实际参数）
    mtx_left = np.array([[2349.599303017811, 0, 1221.0886985822297],
                         [0, 2347.04087849075, 1021.2297950652342],
                         [0, 0, 1]], dtype=np.float32)

    mtx_right = np.array([[2347.7080632127045, 0, 1219.3168735048296],
                          [0, 2347.528871737054, 1010.4282529230558],
                          [0, 0, 1]], dtype=np.float32)

    dist_left = np.array([-0.0677072434895743, 0.16840134514589222, 
                          -0.00013311325437381048, -0.0010946605867930416, 
                          -0.19743756744235746], dtype=np.float32)
    
    dist_right = np.array([-0.07691265481784593, 0.22703604995053306, 
                           0.00015959041360151294, -0.0011580170802655745, 
                           -0.3538743014783903], dtype=np.float32)

    R = np.array([[0.9998155478060632, -0.0008436404567199599, -0.01918746038540903],
                  [0.0008172502236645439, 0.9999987094440285, -0.0013831891951225166],
                  [0.019188602537281753, 0.0013672531065493177, 0.9998149469534894]], dtype=np.float32)

    T = np.array([-100.87040766250446, 0.06079718879422688, -1.3284405860235702], dtype=np.float32)

    image_size = (2448, 2048)
    
    print(f"左相机内参矩阵形状: {mtx_left.shape}")
    print(f"右相机内参矩阵形状: {mtx_right.shape}")
    print(f"左相机畸变参数形状: {dist_left.shape}")
    print(f"右相机畸变参数形状: {dist_right.shape}")
    print(f"旋转矩阵形状: {R.shape}")
    print(f"平移向量形状: {T.shape}")
    print(f"图像尺寸: {image_size}")
    
    # 测试立体校正
    try:
        R1, R2, P1, P2, Q, validPixROI1, validPixROI2 = cv2.stereoRectify(
            mtx_left, dist_left, mtx_right, dist_right,
            image_size, R, T, alpha=0
        )
        print("✓ 立体校正参数计算成功")
        print(f"Q矩阵形状: {Q.shape}")
        
    except Exception as e:
        print(f"✗ 立体校正参数计算失败: {e}")
        return False
    
    return True

def test_stereo_matching():
    """测试立体匹配算法"""
    print("\n=== 测试立体匹配算法 ===")
    
    # 创建测试图像
    height, width = 480, 640
    left_img = np.random.randint(0, 255, (height, width), dtype=np.uint8)
    right_img = np.random.randint(0, 255, (height, width), dtype=np.uint8)
    
    # 添加一些结构化特征
    cv2.rectangle(left_img, (100, 100), (200, 200), 255, -1)
    cv2.rectangle(right_img, (90, 100), (190, 200), 255, -1)  # 模拟视差
    
    print(f"测试图像尺寸: {left_img.shape}")
    
    # 创建立体匹配器
    try:
        stereo = cv2.StereoSGBM_create(
            minDisparity=0,
            numDisparities=96,
            blockSize=5,
            P1=8 * 3 * 5 ** 2,
            P2=32 * 3 * 5 ** 2,
            disp12MaxDiff=1,
            uniquenessRatio=10,
            speckleWindowSize=100,
            speckleRange=32,
            preFilterCap=63,
            mode=cv2.STEREO_SGBM_MODE_SGBM_3WAY
        )
        print("✓ 立体匹配器创建成功")
        
    except Exception as e:
        print(f"✗ 立体匹配器创建失败: {e}")
        return False
    
    # 计算视差
    try:
        disparity = stereo.compute(left_img, right_img).astype(np.float32) / 16.0
        print(f"✓ 视差计算成功，视差图形状: {disparity.shape}")
        print(f"视差范围: {np.min(disparity)} - {np.max(disparity)}")
        
    except Exception as e:
        print(f"✗ 视差计算失败: {e}")
        return False
    
    return True

def test_hardware_trigger_simulation():
    """模拟硬件触发测试"""
    print("\n=== 模拟硬件触发测试 ===")
    
    # 模拟触发配置
    trigger_configs = [
        {"line": "Line0", "activation": "RisingEdge"},
        {"line": "Line1", "activation": "FallingEdge"},
        {"line": "Line2", "activation": "LevelHigh"},
        {"line": "Line3", "activation": "LevelLow"},
    ]
    
    for config in trigger_configs:
        print(f"测试配置: {config['line']} - {config['activation']}")
        
        # 模拟配置应用
        time.sleep(0.1)
        print(f"  ✓ 触发线设置: {config['line']}")
        print(f"  ✓ 触发极性设置: {config['activation']}")
    
    print("✓ 硬件触发配置测试完成")
    return True

def test_file_operations():
    """测试文件操作"""
    print("\n=== 测试文件操作 ===")
    
    # 测试目录创建
    test_dirs = ["stereo_results", "saved_images", "saved_depth_images"]
    
    for dir_name in test_dirs:
        try:
            if not os.path.exists(dir_name):
                os.makedirs(dir_name)
            print(f"✓ 目录创建/检查成功: {dir_name}")
        except Exception as e:
            print(f"✗ 目录创建失败: {dir_name}, 错误: {e}")
            return False
    
    # 测试图像保存
    try:
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        test_filename = os.path.join("stereo_results", "test_image.png")
        cv2.imwrite(test_filename, test_image)
        
        if os.path.exists(test_filename):
            print("✓ 图像保存测试成功")
            os.remove(test_filename)  # 清理测试文件
        else:
            print("✗ 图像保存测试失败")
            return False
            
    except Exception as e:
        print(f"✗ 图像保存测试失败: {e}")
        return False
    
    return True

def test_depth_calculation():
    """测试深度计算"""
    print("\n=== 测试深度计算 ===")
    
    # 创建测试Q矩阵
    Q = np.array([[1, 0, 0, -320],
                  [0, 1, 0, -240],
                  [0, 0, 0, 1000],
                  [0, 0, -1/100, 0]], dtype=np.float32)
    
    # 创建测试视差图
    disparity = np.ones((480, 640), dtype=np.float32) * 10  # 固定视差值
    
    try:
        # 重投影到3D
        points_3d = cv2.reprojectImageTo3D(disparity, Q)
        depth = points_3d[:, :, 2]
        
        print(f"✓ 深度计算成功")
        print(f"深度图形状: {depth.shape}")
        print(f"深度范围: {np.min(depth)} - {np.max(depth)}")
        
        # 测试深度图可视化
        depth_norm = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX)
        depth_color = cv2.applyColorMap(depth_norm.astype(np.uint8), cv2.COLORMAP_JET)
        print(f"✓ 深度图可视化成功，彩色深度图形状: {depth_color.shape}")
        
    except Exception as e:
        print(f"✗ 深度计算失败: {e}")
        return False
    
    return True

def run_all_tests():
    """运行所有测试"""
    print("开始硬件触发立体匹配功能测试...")
    print("=" * 50)
    
    tests = [
        ("立体标定参数", test_stereo_calibration_params),
        ("立体匹配算法", test_stereo_matching),
        ("硬件触发模拟", test_hardware_trigger_simulation),
        ("文件操作", test_file_operations),
        ("深度计算", test_depth_calculation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
    else:
        print("⚠️  部分测试失败，请检查相关配置。")
    
    return passed == total

if __name__ == "__main__":
    run_all_tests()
