# -*- coding: utf-8 -*-
import sys
import time
import os
import threading
from queue import Queue
import numpy as np
import cv2

from PyQt5.QtWidgets import *
from PyQt5.QtGui import QTextCursor, QKeySequence
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from CamOperation_class import CameraOperation
from MvCameraControl_class import *
from MvErrorDefine_const import *
from CameraParams_header import *
from PyUIMultipleCameras import Ui_MainWindow
import ctypes

# 立体视觉处理类 - 基于ceshiliti代码的立体匹配方法
# 标定参数: 2448x2048分辨率, 基线100.88mm, 焦距~2348px
# 立体匹配方法已与ceshiliti代码保持一致
class StereoVisionProcessor(QObject):
    # 定义信号用于更新UI
    depth_result_signal = pyqtSignal(np.ndarray, np.ndarray, np.ndarray)  # disparity, depth, depth_color

    def __init__(self):
        super().__init__()
        self.calibration_params = None
        self.rectification_maps = None
        self.Q_matrix = None
        self.calibration_available = False

        # 立体匹配器
        try:
            self.stereo_matcher = self.create_stereo_matcher()
            if self.stereo_matcher is None:
                print("警告: 立体匹配器创建失败，立体视觉功能将不可用")
                self.stereo_available = False
            else:
                print("立体匹配器初始化成功")
                self.stereo_available = True
        except Exception as e:
            print(f"立体匹配器初始化错误: {e}")
            self.stereo_matcher = None
            self.stereo_available = False

        # 图像队列用于同步
        self.left_image_queue = Queue(maxsize=2)
        self.right_image_queue = Queue(maxsize=2)

        # 处理标志
        self.processing = False
        self.process_thread = None

        # 存储最新的深度图
        self.latest_depth = None
        self.latest_disparity = None
        self.depth_lock = threading.Lock()

        # 图像保存控制参数
        self.save_input_images = True      # 是否保存原始输入图像
        self.save_rectified_images = True  # 是否保存校正后图像
        self.save_comparison_images = True # 是否保存对比图像
        self.image_save_interval = 5       # 图像保存间隔（秒）
        self.last_save_time = 0           # 上次保存时间

        # 深度图处理参数
        self.enable_depth_smoothing = True    # 是否启用深度平滑
        self.smoothing_method = "bilateral"   # 平滑方法: "bilateral", "gaussian", "median", "guided", "edge_preserving"
        self.bilateral_d = 9                  # 双边滤波邻域直径
        self.bilateral_sigma_color = 75       # 双边滤波颜色空间标准差
        self.bilateral_sigma_space = 75       # 双边滤波坐标空间标准差
        self.gaussian_kernel_size = 5         # 高斯滤波核大小
        self.gaussian_sigma = 1.0             # 高斯滤波标准差
        self.median_kernel_size = 5           # 中值滤波核大小
        self.edge_preserving_flags = 1        # 边缘保持滤波标志
        self.edge_preserving_sigma_s = 50     # 边缘保持滤波空间窗口大小
        self.edge_preserving_sigma_r = 0.4    # 边缘保持滤波相似性阈值

        # 深度图高级处理参数
        self.enable_depth_interpolation = False    # 是否启用深度插值
        self.interpolation_method = "linear"       # 插值方法: "linear", "cubic", "nearest", "inpaint"
        self.enable_depth_enhancement = False      # 是否启用深度图增强
        self.enhancement_method = "histogram_eq"   # 增强方法: "histogram_eq", "clahe", "gamma_correction"
        self.enable_hole_filling = True           # 是否启用孔填充
        self.hole_filling_method = "morphology"   # 孔填充方法: "morphology", "inpaint", "interpolation"
        self.hole_filling_kernel_size = 5         # 孔填充核大小
        self.inpaint_radius = 3                   # 修复半径

        # 点云处理参数
        self.enable_pointcloud_processing = True   # 是否启用点云处理
        self.pointcloud_denoising = True          # 是否启用点云降噪
        self.denoising_method = "statistical"     # 降噪方法: "statistical", "radius", "bilateral"
        self.statistical_neighbors = 20           # 统计滤波邻居数
        self.statistical_std_ratio = 2.0          # 统计滤波标准差比率
        self.radius_filter_radius = 0.05          # 半径滤波半径
        self.radius_filter_min_neighbors = 16     # 半径滤波最小邻居数

        self.enable_surface_reconstruction = False # 是否启用表面重建
        self.reconstruction_method = "poisson"     # 重建方法: "poisson", "delaunay", "alpha_shape"
        self.poisson_depth = 9                    # 泊松重建深度

        self.enable_normal_estimation = False      # 是否启用法向量估计
        self.normal_radius = 0.1                  # 法向量估计半径
        self.normal_max_neighbors = 30            # 法向量估计最大邻居数

        self.enable_plane_fitting = False         # 是否启用平面拟合
        self.plane_distance_threshold = 0.01      # 平面拟合距离阈值
        self.plane_ransac_n = 3                   # RANSAC最小点数
        self.plane_num_iterations = 1000          # RANSAC迭代次数

        # 处理流程控制
        self.processing_pipeline = []             # 处理流程列表

        # 尝试初始化标定参数
        try:
            self.init_calibration_params()
        except Exception as e:
            print(f"Warning: Calibration initialization failed: {e}")
            print("Using simplified stereo processing without calibration")
            self.calibration_available = False

    def create_stereo_matcher(self):
        """创建立体匹配器 - 基于ceshiliti代码的方法"""
        try:
            # 使用与ceshiliti代码相同的参数设置
            stereo = cv2.StereoSGBM_create(
                minDisparity=0,
                numDisparities=96,  # 增加视差范围
                blockSize=7,  # 增加块大小
                P1=8 * 3 * 7 ** 2,
                P2=32 * 3 * 7 ** 2,
                disp12MaxDiff=1,
                uniquenessRatio=10,
                speckleWindowSize=100,
                speckleRange=32,
                preFilterCap=63,
                mode=cv2.STEREO_SGBM_MODE_SGBM_3WAY
            )

            # 验证立体匹配器是否创建成功
            if stereo is None:
                print("错误: StereoSGBM_create 返回 None")
                raise RuntimeError("Failed to create StereoSGBM matcher")

            # 验证是否具有compute方法
            if not hasattr(stereo, 'compute') or not callable(getattr(stereo, 'compute')):
                print("错误: 创建的立体匹配器没有可调用的compute方法")
                raise RuntimeError("StereoSGBM matcher does not have callable compute method")

            print("立体匹配器创建成功")
            return stereo

        except Exception as e:
            print(f"创建立体匹配器失败: {e}")
            print("尝试使用备用方法创建立体匹配器...")

            # 备用方法：使用更简单的参数
            try:
                stereo = cv2.StereoSGBM_create()
                if stereo is not None and hasattr(stereo, 'compute') and callable(getattr(stereo, 'compute')):
                    # 设置基本参数
                    stereo.setMinDisparity(0)
                    stereo.setNumDisparities(96)
                    stereo.setBlockSize(7)
                    stereo.setP1(8 * 3 * 7 ** 2)
                    stereo.setP2(32 * 3 * 7 ** 2)
                    stereo.setDisp12MaxDiff(1)
                    stereo.setUniquenessRatio(10)
                    stereo.setSpeckleWindowSize(100)
                    stereo.setSpeckleRange(32)
                    stereo.setPreFilterCap(63)
                    stereo.setMode(cv2.STEREO_SGBM_MODE_SGBM_3WAY)

                    print("使用备用方法成功创建立体匹配器")
                    return stereo
                else:
                    raise RuntimeError("Backup method also failed")

            except Exception as backup_error:
                print(f"备用方法也失败: {backup_error}")
                print("返回None，将在后续处理中处理此错误")
                return None

    def check_stereo_matcher(self):
        """检查立体匹配器状态"""
        if self.stereo_matcher is None:
            return False, "立体匹配器为None"

        if not hasattr(self.stereo_matcher, 'compute'):
            return False, "立体匹配器没有compute方法"

        if not callable(getattr(self.stereo_matcher, 'compute')):
            return False, "立体匹配器的compute方法不可调用"

        return True, "立体匹配器状态正常"

    def reinitialize_stereo_matcher(self):
        """重新初始化立体匹配器"""
        try:
            print("尝试重新初始化立体匹配器...")
            self.stereo_matcher = self.create_stereo_matcher()

            if self.stereo_matcher is not None:
                self.stereo_available = True
                print("立体匹配器重新初始化成功")
                return True
            else:
                self.stereo_available = False
                print("立体匹配器重新初始化失败")
                return False

        except Exception as e:
            print(f"重新初始化立体匹配器时出错: {e}")
            self.stereo_matcher = None
            self.stereo_available = False
            return False

    def init_calibration_params(self):
        """初始化相机标定参数"""
        try:
            print("Initializing stereo calibration parameters...")

            # 尝试从JSON文件加载真实标定参数
            try:
                import json
                with open('stereo_calibration.json', 'r') as f:
                    calib_data = json.load(f)

                print("Loading calibration parameters from stereo_calibration.json")

                # 加载真实的标定参数
                self.camera_matrix_left = np.array(calib_data['camera_matrix_left'], dtype=np.float64)
                self.camera_matrix_right = np.array(calib_data['camera_matrix_right'], dtype=np.float64)

                # 畸变系数
                self.dist_coeffs_left = np.array(calib_data['dist_coeffs_left'], dtype=np.float64)
                self.dist_coeffs_right = np.array(calib_data['dist_coeffs_right'], dtype=np.float64)

                # 立体标定参数
                self.R = np.array(calib_data['R'], dtype=np.float64)
                self.T = np.array(calib_data['T'], dtype=np.float64)

                # 图像尺寸
                self.image_size = tuple(calib_data['image_size'])  # (2448, 2048)
                self.baseline_mm = calib_data['baseline_mm']

                print(f"Loaded calibration: Image size {self.image_size}, Baseline {self.baseline_mm:.2f}mm")

            except (FileNotFoundError, KeyError, json.JSONDecodeError) as e:
                print(f"Failed to load calibration file: {e}")
                print("Using provided calibration parameters...")

                # 使用您提供的真实标定参数
                self.camera_matrix_left = np.array([
                    [2349.599303017811, 0.0, 1221.0886985822297],
                    [0.0, 2347.04087849075, 1021.2297950652342],
                    [0.0, 0.0, 1.0]
                ], dtype=np.float64)

                self.camera_matrix_right = np.array([
                    [2347.7080632127045, 0.0, 1219.3168735048296],
                    [0.0, 2347.528871737054, 1010.4282529230558],
                    [0.0, 0.0, 1.0]
                ], dtype=np.float64)

                # 畸变系数 - 转换为正确的形状
                self.dist_coeffs_left = np.array([
                    [-0.0677072434895743, 0.16840134514589222, -0.00013311325437381048,
                     -0.0010946605867930416, -0.19743756744235746]
                ], dtype=np.float64).reshape(5, 1)

                self.dist_coeffs_right = np.array([
                    [-0.07691265481784593, 0.22703604995053306, 0.00015959041360151294,
                     -0.0011580170802655745, -0.3538743014783903]
                ], dtype=np.float64).reshape(5, 1)

                # 立体标定参数
                self.R = np.array([
                    [0.9998155478060632, -0.0008436404567199599, -0.01918746038540903],
                    [0.0008172502236645439, 0.9999987094440285, -0.0013831891951225166],
                    [0.019188602537281753, 0.0013672531065493177, 0.9998149469534894]
                ], dtype=np.float64)

                self.T = np.array([
                    [-100.87040766250446],
                    [0.06079718879422688],
                    [-1.3284405860235702]
                ], dtype=np.float64)

                # 图像尺寸和基线距离
                self.image_size = (2448, 2048)  # 使用您提供的真实图像尺寸
                self.baseline_mm = 100.87917323555243  # 使用您提供的真实基线距离

                print(f"Using provided calibration: Image size {self.image_size}, Baseline {self.baseline_mm:.2f}mm")

            # 尝试计算校正映射，如果失败则跳过
            try:
                self.compute_rectification_maps()
                self.calibration_available = True
                print("Calibration parameters initialized successfully")
            except Exception as calib_error:
                print(f"Calibration mapping failed: {calib_error}")
                print("Will use simplified stereo processing")
                self.calibration_available = False
                # 设置基于真实标定参数的简化Q矩阵
                # 使用真实的主点坐标和焦距
                cx = 1220.0  # 主点x坐标的平均值
                cy = 1015.0  # 主点y坐标的平均值
                fx = 2348.0  # 焦距的平均值
                baseline = 100.88  # 真实基线距离(mm)

                self.Q_matrix = np.array([
                    [1.0, 0.0, 0.0, -cx],
                    [0.0, 1.0, 0.0, -cy],
                    [0.0, 0.0, 0.0, fx],
                    [0.0, 0.0, -1.0/baseline, 0.0]
                ], dtype=np.float64)

        except Exception as e:
            print(f"Error initializing calibration parameters: {e}")
            # 设置默认值避免崩溃
            self.camera_matrix_left = None
            self.camera_matrix_right = None
            self.dist_coeffs_left = None
            self.dist_coeffs_right = None
            self.R = None
            self.T = None
            self.Q_matrix = None
            self.calibration_available = False

    def compute_rectification_maps(self):
        """计算校正映射"""
        try:
            # 检查参数是否有效
            if (self.camera_matrix_left is None or self.camera_matrix_right is None or
                self.dist_coeffs_left is None or self.dist_coeffs_right is None or
                self.R is None or self.T is None):
                print("Calibration parameters not properly initialized")
                return

            # 使用真实的图像尺寸
            image_size = getattr(self, 'image_size', (640, 480))

            # 确保所有参数都是正确的数据类型
            R1, R2, P1, P2, self.Q_matrix, _, _ = cv2.stereoRectify(
                self.camera_matrix_left.astype(np.float64),
                self.dist_coeffs_left.astype(np.float64),
                self.camera_matrix_right.astype(np.float64),
                self.dist_coeffs_right.astype(np.float64),
                image_size,
                self.R.astype(np.float64),
                self.T.astype(np.float64)
            )

            self.map1_left, self.map2_left = cv2.initUndistortRectifyMap(
                self.camera_matrix_left.astype(np.float64),
                self.dist_coeffs_left.astype(np.float64),
                R1, P1, image_size, cv2.CV_16SC2
            )

            self.map1_right, self.map2_right = cv2.initUndistortRectifyMap(
                self.camera_matrix_right.astype(np.float64),
                self.dist_coeffs_right.astype(np.float64),
                R2, P2, image_size, cv2.CV_16SC2
            )

        except Exception as e:
            print(f"Error computing rectification maps: {e}")
            # 设置为None避免后续错误
            self.map1_left = None
            self.map2_left = None
            self.map1_right = None
            self.map2_right = None
            self.Q_matrix = None

    def rectify_images(self, left_img, right_img):
        """校正图像"""
        try:
            # 如果没有标定参数，直接返回原图像
            if not self.calibration_available:
                return left_img, right_img

            # 检查映射是否有效
            if (self.map1_left is None or self.map2_left is None or
                self.map1_right is None or self.map2_right is None):
                print("Rectification maps not available, returning original images")
                return left_img, right_img

            left_rectified = cv2.remap(left_img, self.map1_left, self.map2_left, cv2.INTER_LINEAR)
            right_rectified = cv2.remap(right_img, self.map1_right, self.map2_right, cv2.INTER_LINEAR)
            return left_rectified, right_rectified
        except Exception as e:
            print(f"Error rectifying images: {e}")
            return left_img, right_img

    def compute_disparity(self, left_img, right_img):
        """计算视差图 - 基于ceshiliti代码的方法"""
        # 检查立体视觉功能是否可用
        if not getattr(self, 'stereo_available', False):
            print("错误: 立体视觉功能不可用")
            raise RuntimeError("Stereo vision functionality is not available")

        # 检查立体匹配器是否可用
        if self.stereo_matcher is None:
            print("错误: 立体匹配器未初始化")
            raise RuntimeError("Stereo matcher is not initialized")

        # 验证立体匹配器是否具有compute方法
        if not hasattr(self.stereo_matcher, 'compute') or not callable(getattr(self.stereo_matcher, 'compute')):
            print("错误: 立体匹配器没有可调用的compute方法")
            print(f"stereo_matcher类型: {type(self.stereo_matcher)}")
            print(f"stereo_matcher值: {self.stereo_matcher}")
            raise RuntimeError("Stereo matcher does not have callable compute method")

        # 转换为灰度图
        if len(left_img.shape) == 3:
            left_gray = cv2.cvtColor(left_img, cv2.COLOR_BGR2GRAY)
            right_gray = cv2.cvtColor(right_img, cv2.COLOR_BGR2GRAY)
        else:
            left_gray = left_img
            right_gray = right_img

        # 校正图像
        left_rect, right_rect = self.rectify_images(left_gray, right_gray)

        try:
            # 计算视差
            print("开始计算视差...")
            disparity = self.stereo_matcher.compute(left_rect, right_rect)

            if disparity is None:
                print("错误: 视差计算返回None")
                raise RuntimeError("Disparity computation returned None")

            print(f"视差计算成功，形状: {disparity.shape}")

        except Exception as e:
            print(f"视差计算失败: {e}")
            print(f"左图像形状: {left_rect.shape}, 右图像形状: {right_rect.shape}")
            raise

        # 转换为浮点数并归一化
        disparity = disparity.astype(np.float32) / 16.0

        return disparity, left_rect, right_rect

    def disparity_to_depth(self, disparity, reference_image=None):
        """将视差转换为深度 - 增加完整的深度处理流水线"""
        if self.Q_matrix is None:
            return None

        # 使用Q矩阵重投影到3D
        points_3d = cv2.reprojectImageTo3D(disparity, self.Q_matrix)

        # 提取深度信息（Z坐标）
        depth = points_3d[:, :, 2]

        # 过滤无效深度值
        depth[depth <= 0] = 0
        depth[depth > 5000] = 0  # 限制最大深度为5米

        # 应用完整的深度处理流水线
        depth_processed = self.apply_depth_processing_pipeline(depth, reference_image)

        return depth_processed

    def create_depth_colormap(self, depth):
        """创建深度图的彩色可视化 - 基于ceshiliti代码的方法"""
        # 归一化深度值到0-255
        depth_norm = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX)
        depth_norm = depth_norm.astype(np.uint8)

        # 应用彩色映射
        depth_color = cv2.applyColorMap(depth_norm, cv2.COLORMAP_JET)

        # 将无效区域设为黑色
        mask = depth <= 0
        depth_color[mask] = [0, 0, 0]

        return depth_color

    def smooth_depth_bilateral(self, depth):
        """双边滤波深度平滑 - 保持边缘的同时减少噪声"""
        try:
            # 创建有效深度掩码
            valid_mask = depth > 0

            if not np.any(valid_mask):
                return depth

            # 将深度图转换为8位用于滤波
            depth_normalized = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX)
            depth_8bit = depth_normalized.astype(np.uint8)

            # 应用双边滤波
            smoothed_8bit = cv2.bilateralFilter(
                depth_8bit,
                self.bilateral_d,
                self.bilateral_sigma_color,
                self.bilateral_sigma_space
            )

            # 转换回原始深度范围
            depth_min, depth_max = np.min(depth[valid_mask]), np.max(depth[valid_mask])
            smoothed_depth = smoothed_8bit.astype(np.float32) * (depth_max - depth_min) / 255.0 + depth_min

            # 只在有效区域应用平滑
            result = depth.copy()
            result[valid_mask] = smoothed_depth[valid_mask]

            return result

        except Exception as e:
            print(f"双边滤波深度平滑错误: {e}")
            return depth

    def smooth_depth_gaussian(self, depth):
        """高斯滤波深度平滑"""
        try:
            valid_mask = depth > 0

            if not np.any(valid_mask):
                return depth

            # 应用高斯滤波
            smoothed = cv2.GaussianBlur(depth, (self.gaussian_kernel_size, self.gaussian_kernel_size), self.gaussian_sigma)

            # 只在有效区域应用平滑
            result = depth.copy()
            result[valid_mask] = smoothed[valid_mask]

            return result

        except Exception as e:
            print(f"高斯滤波深度平滑错误: {e}")
            return depth

    def smooth_depth_median(self, depth):
        """中值滤波深度平滑 - 去除椒盐噪声"""
        try:
            valid_mask = depth > 0

            if not np.any(valid_mask):
                return depth

            # 转换为适合中值滤波的格式
            depth_for_median = depth.astype(np.float32)
            depth_for_median[~valid_mask] = 0

            # 应用中值滤波
            smoothed = cv2.medianBlur(depth_for_median.astype(np.uint16), self.median_kernel_size).astype(np.float32)

            # 只在有效区域应用平滑
            result = depth.copy()
            result[valid_mask] = smoothed[valid_mask]

            return result

        except Exception as e:
            print(f"中值滤波深度平滑错误: {e}")
            return depth

    def smooth_depth_edge_preserving(self, depth, reference_image=None):
        """边缘保持滤波深度平滑"""
        try:
            valid_mask = depth > 0

            if not np.any(valid_mask):
                return depth

            # 将深度图转换为8位
            depth_normalized = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX)
            depth_8bit = depth_normalized.astype(np.uint8)

            # 转换为3通道图像
            depth_3ch = cv2.cvtColor(depth_8bit, cv2.COLOR_GRAY2BGR)

            # 应用边缘保持滤波
            smoothed_3ch = cv2.edgePreservingFilter(
                depth_3ch,
                flags=self.edge_preserving_flags,
                sigma_s=self.edge_preserving_sigma_s,
                sigma_r=self.edge_preserving_sigma_r
            )

            # 转换回单通道
            smoothed_8bit = cv2.cvtColor(smoothed_3ch, cv2.COLOR_BGR2GRAY)

            # 转换回原始深度范围
            depth_min, depth_max = np.min(depth[valid_mask]), np.max(depth[valid_mask])
            smoothed_depth = smoothed_8bit.astype(np.float32) * (depth_max - depth_min) / 255.0 + depth_min

            # 只在有效区域应用平滑
            result = depth.copy()
            result[valid_mask] = smoothed_depth[valid_mask]

            return result

        except Exception as e:
            print(f"边缘保持滤波深度平滑错误: {e}")
            return depth

    def smooth_depth_guided(self, depth, guide_image):
        """引导滤波深度平滑 - 使用参考图像引导"""
        try:
            # 这里需要安装opencv-contrib-python来使用引导滤波
            # 如果没有安装，回退到双边滤波
            if not hasattr(cv2, 'ximgproc'):
                print("引导滤波需要opencv-contrib-python，回退到双边滤波")
                return self.smooth_depth_bilateral(depth)

            valid_mask = depth > 0

            if not np.any(valid_mask):
                return depth

            # 将深度图和引导图像转换为合适的格式
            depth_normalized = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX)
            depth_8bit = depth_normalized.astype(np.uint8)

            if len(guide_image.shape) == 3:
                guide_gray = cv2.cvtColor(guide_image, cv2.COLOR_BGR2GRAY)
            else:
                guide_gray = guide_image

            # 应用引导滤波
            smoothed_8bit = cv2.ximgproc.guidedFilter(guide_gray, depth_8bit, radius=8, eps=0.2)

            # 转换回原始深度范围
            depth_min, depth_max = np.min(depth[valid_mask]), np.max(depth[valid_mask])
            smoothed_depth = smoothed_8bit.astype(np.float32) * (depth_max - depth_min) / 255.0 + depth_min

            # 只在有效区域应用平滑
            result = depth.copy()
            result[valid_mask] = smoothed_depth[valid_mask]

            return result

        except Exception as e:
            print(f"引导滤波深度平滑错误: {e}")
            return self.smooth_depth_bilateral(depth)

    def apply_depth_smoothing(self, depth, reference_image=None):
        """应用深度平滑处理"""
        if not self.enable_depth_smoothing:
            return depth

        try:
            print(f"应用深度平滑: {self.smoothing_method}")

            if self.smoothing_method == "bilateral":
                return self.smooth_depth_bilateral(depth)
            elif self.smoothing_method == "gaussian":
                return self.smooth_depth_gaussian(depth)
            elif self.smoothing_method == "median":
                return self.smooth_depth_median(depth)
            elif self.smoothing_method == "edge_preserving":
                return self.smooth_depth_edge_preserving(depth, reference_image)
            elif self.smoothing_method == "guided" and reference_image is not None:
                return self.smooth_depth_guided(depth, reference_image)
            else:
                print(f"未知的平滑方法: {self.smoothing_method}，使用双边滤波")
                return self.smooth_depth_bilateral(depth)

        except Exception as e:
            print(f"深度平滑处理错误: {e}")
            return depth

    def interpolate_depth(self, depth):
        """深度图插值处理"""
        try:
            if not self.enable_depth_interpolation:
                return depth

            print(f"应用深度插值: {self.interpolation_method}")

            # 创建有效深度掩码
            valid_mask = depth > 0

            if not np.any(valid_mask):
                return depth

            if self.interpolation_method == "linear":
                return self._interpolate_linear(depth, valid_mask)
            elif self.interpolation_method == "cubic":
                return self._interpolate_cubic(depth, valid_mask)
            elif self.interpolation_method == "nearest":
                return self._interpolate_nearest(depth, valid_mask)
            elif self.interpolation_method == "inpaint":
                return self._interpolate_inpaint(depth, valid_mask)
            else:
                print(f"未知插值方法: {self.interpolation_method}")
                return depth

        except Exception as e:
            print(f"深度插值错误: {e}")
            return depth

    def _interpolate_linear(self, depth, valid_mask):
        """线性插值"""
        from scipy.interpolate import griddata

        # 获取有效点的坐标和值
        y_coords, x_coords = np.where(valid_mask)
        values = depth[valid_mask]

        # 创建目标网格
        yi, xi = np.mgrid[0:depth.shape[0], 0:depth.shape[1]]

        # 线性插值
        interpolated = griddata(
            (y_coords, x_coords), values, (yi, xi),
            method='linear', fill_value=0
        )

        return interpolated

    def _interpolate_cubic(self, depth, valid_mask):
        """三次插值"""
        from scipy.interpolate import griddata

        y_coords, x_coords = np.where(valid_mask)
        values = depth[valid_mask]

        yi, xi = np.mgrid[0:depth.shape[0], 0:depth.shape[1]]

        interpolated = griddata(
            (y_coords, x_coords), values, (yi, xi),
            method='cubic', fill_value=0
        )

        # 处理NaN值
        interpolated = np.nan_to_num(interpolated, nan=0.0)

        return interpolated

    def _interpolate_nearest(self, depth, valid_mask):
        """最近邻插值"""
        from scipy.interpolate import griddata

        y_coords, x_coords = np.where(valid_mask)
        values = depth[valid_mask]

        yi, xi = np.mgrid[0:depth.shape[0], 0:depth.shape[1]]

        interpolated = griddata(
            (y_coords, x_coords), values, (yi, xi),
            method='nearest', fill_value=0
        )

        return interpolated

    def _interpolate_inpaint(self, depth, valid_mask):
        """基于修复的插值"""
        # 将深度图转换为8位
        depth_normalized = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX)
        depth_8bit = depth_normalized.astype(np.uint8)

        # 创建无效区域掩码
        invalid_mask = (~valid_mask).astype(np.uint8)

        # 使用OpenCV的inpaint函数
        inpainted = cv2.inpaint(depth_8bit, invalid_mask, self.inpaint_radius, cv2.INPAINT_TELEA)

        # 转换回原始深度范围
        if np.any(valid_mask):
            depth_min, depth_max = np.min(depth[valid_mask]), np.max(depth[valid_mask])
            result = inpainted.astype(np.float32) * (depth_max - depth_min) / 255.0 + depth_min
        else:
            result = inpainted.astype(np.float32)

        return result

    def enhance_depth(self, depth):
        """深度图增强处理"""
        try:
            if not self.enable_depth_enhancement:
                return depth

            print(f"应用深度增强: {self.enhancement_method}")

            valid_mask = depth > 0

            if not np.any(valid_mask):
                return depth

            if self.enhancement_method == "histogram_eq":
                return self._enhance_histogram_equalization(depth, valid_mask)
            elif self.enhancement_method == "clahe":
                return self._enhance_clahe(depth, valid_mask)
            elif self.enhancement_method == "gamma_correction":
                return self._enhance_gamma_correction(depth, valid_mask)
            else:
                print(f"未知增强方法: {self.enhancement_method}")
                return depth

        except Exception as e:
            print(f"深度增强错误: {e}")
            return depth

    def _enhance_histogram_equalization(self, depth, valid_mask):
        """直方图均衡化"""
        # 将深度图转换为8位
        depth_normalized = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX)
        depth_8bit = depth_normalized.astype(np.uint8)

        # 应用直方图均衡化
        equalized = cv2.equalizeHist(depth_8bit)

        # 转换回原始深度范围
        if np.any(valid_mask):
            depth_min, depth_max = np.min(depth[valid_mask]), np.max(depth[valid_mask])
            result = equalized.astype(np.float32) * (depth_max - depth_min) / 255.0 + depth_min
        else:
            result = equalized.astype(np.float32)

        # 只在有效区域应用增强
        enhanced_depth = depth.copy()
        enhanced_depth[valid_mask] = result[valid_mask]

        return enhanced_depth

    def _enhance_clahe(self, depth, valid_mask):
        """对比度限制自适应直方图均衡化"""
        # 将深度图转换为8位
        depth_normalized = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX)
        depth_8bit = depth_normalized.astype(np.uint8)

        # 创建CLAHE对象
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))

        # 应用CLAHE
        enhanced = clahe.apply(depth_8bit)

        # 转换回原始深度范围
        if np.any(valid_mask):
            depth_min, depth_max = np.min(depth[valid_mask]), np.max(depth[valid_mask])
            result = enhanced.astype(np.float32) * (depth_max - depth_min) / 255.0 + depth_min
        else:
            result = enhanced.astype(np.float32)

        # 只在有效区域应用增强
        enhanced_depth = depth.copy()
        enhanced_depth[valid_mask] = result[valid_mask]

        return enhanced_depth

    def _enhance_gamma_correction(self, depth, valid_mask, gamma=1.2):
        """伽马校正"""
        # 归一化到0-1范围
        if np.any(valid_mask):
            depth_min, depth_max = np.min(depth[valid_mask]), np.max(depth[valid_mask])
            normalized = (depth - depth_min) / (depth_max - depth_min)
        else:
            normalized = depth

        # 应用伽马校正
        corrected = np.power(normalized, gamma)

        # 转换回原始范围
        if np.any(valid_mask):
            result = corrected * (depth_max - depth_min) + depth_min
        else:
            result = corrected

        # 只在有效区域应用校正
        enhanced_depth = depth.copy()
        enhanced_depth[valid_mask] = result[valid_mask]

        return enhanced_depth

    def fill_holes(self, depth):
        """深度图孔填充处理"""
        try:
            if not self.enable_hole_filling:
                return depth

            print(f"应用孔填充: {self.hole_filling_method}")

            if self.hole_filling_method == "morphology":
                return self._fill_holes_morphology(depth)
            elif self.hole_filling_method == "inpaint":
                return self._fill_holes_inpaint(depth)
            elif self.hole_filling_method == "interpolation":
                return self._fill_holes_interpolation(depth)
            else:
                print(f"未知孔填充方法: {self.hole_filling_method}")
                return depth

        except Exception as e:
            print(f"孔填充错误: {e}")
            return depth

    def _fill_holes_morphology(self, depth):
        """基于形态学操作的孔填充"""
        # 创建有效深度掩码
        valid_mask = (depth > 0).astype(np.uint8)

        # 创建形态学核
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE,
                                         (self.hole_filling_kernel_size, self.hole_filling_kernel_size))

        # 膨胀操作填充小孔
        dilated_mask = cv2.dilate(valid_mask, kernel, iterations=1)

        # 对深度图进行膨胀操作
        depth_dilated = cv2.dilate(depth.astype(np.float32), kernel, iterations=1)

        # 只在新填充的区域应用膨胀结果
        result = depth.copy()
        new_filled_mask = (dilated_mask > valid_mask) & (depth_dilated > 0)
        result[new_filled_mask] = depth_dilated[new_filled_mask]

        return result

    def _fill_holes_inpaint(self, depth):
        """基于图像修复的孔填充"""
        # 将深度图转换为8位
        depth_normalized = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX)
        depth_8bit = depth_normalized.astype(np.uint8)

        # 创建孔洞掩码
        hole_mask = (depth == 0).astype(np.uint8)

        # 使用图像修复填充孔洞
        inpainted = cv2.inpaint(depth_8bit, hole_mask, self.inpaint_radius, cv2.INPAINT_TELEA)

        # 转换回原始深度范围
        valid_mask = depth > 0
        if np.any(valid_mask):
            depth_min, depth_max = np.min(depth[valid_mask]), np.max(depth[valid_mask])
            result = inpainted.astype(np.float32) * (depth_max - depth_min) / 255.0 + depth_min
        else:
            result = inpainted.astype(np.float32)

        return result

    def _fill_holes_interpolation(self, depth):
        """基于插值的孔填充"""
        from scipy.interpolate import griddata

        # 获取有效点
        valid_mask = depth > 0
        y_coords, x_coords = np.where(valid_mask)
        values = depth[valid_mask]

        if len(values) == 0:
            return depth

        # 获取孔洞点
        hole_mask = depth == 0
        hole_y, hole_x = np.where(hole_mask)

        if len(hole_y) == 0:
            return depth

        # 对孔洞点进行插值
        interpolated_values = griddata(
            (y_coords, x_coords), values, (hole_y, hole_x),
            method='linear', fill_value=0
        )

        # 填充孔洞
        result = depth.copy()
        valid_interpolated = ~np.isnan(interpolated_values) & (interpolated_values > 0)
        result[hole_y[valid_interpolated], hole_x[valid_interpolated]] = interpolated_values[valid_interpolated]

        return result

    def apply_depth_processing_pipeline(self, depth, reference_image=None):
        """应用深度图处理流水线"""
        try:
            processed_depth = depth.copy()

            # 1. 深度平滑
            if self.enable_depth_smoothing:
                processed_depth = self.apply_depth_smoothing(processed_depth, reference_image)

            # 2. 孔填充
            if self.enable_hole_filling:
                processed_depth = self.fill_holes(processed_depth)

            # 3. 深度插值
            if self.enable_depth_interpolation:
                processed_depth = self.interpolate_depth(processed_depth)

            # 4. 深度增强
            if self.enable_depth_enhancement:
                processed_depth = self.enhance_depth(processed_depth)

            return processed_depth

        except Exception as e:
            print(f"深度处理流水线错误: {e}")
            return depth

    def compute_depth(self, disparity):
        """从视差计算深度 - 根据真实标定参数优化"""
        try:
            if self.Q_matrix is None:
                print("Q matrix not available for depth computation")
                return None, None

            # 重投影到3D
            points_3d = cv2.reprojectImageTo3D(disparity, self.Q_matrix)
            depth = points_3d[:, :, 2]

            # 根据真实标定参数过滤深度值
            # 基线: 100.88mm, 焦距: ~2348像素
            # 理论最小深度 = (基线 * 焦距) / 最大视差
            # 理论最大深度 = (基线 * 焦距) / 最小视差
            depth[depth <= 0] = 0
            depth[depth < 1500] = 0    # 最小深度1.5米 (考虑实际应用场景)
            depth[depth > 8000] = 0    # 最大深度8米 (根据视差精度限制)

            # 过滤异常值 (深度变化过大的点)
            depth_median = np.median(depth[depth > 0])
            if depth_median > 0:
                depth_std = np.std(depth[depth > 0])
                depth[np.abs(depth - depth_median) > 3 * depth_std] = 0

            return depth, points_3d
        except Exception as e:
            print(f"Error computing depth: {e}")
            return None, None

    def add_image_pair(self, left_img, right_img):
        """添加图像对到处理队列"""
        try:
            if not self.left_image_queue.full():
                self.left_image_queue.put(left_img, block=False)
            if not self.right_image_queue.full():
                self.right_image_queue.put(right_img, block=False)
        except:
            pass

    def start_processing(self):
        """启动处理线程"""
        if not self.processing:
            self.processing = True
            self.process_thread = threading.Thread(target=self.processing_loop)
            self.process_thread.daemon = True
            self.process_thread.start()

    def stop_processing(self):
        """停止处理"""
        self.processing = False
        if self.process_thread:
            self.process_thread.join()

    def processing_loop(self):
        """处理循环"""
        while self.processing:
            try:
                if not self.left_image_queue.empty() and not self.right_image_queue.empty():
                    left_img = self.left_image_queue.get()
                    right_img = self.right_image_queue.get()
                    self.process_stereo_pair(left_img, right_img)
                else:
                    time.sleep(0.01)
            except Exception as e:
                print(f"Stereo processing error: {e}")
                time.sleep(0.1)

    def process_stereo_pair(self, left_img, right_img):
        """处理立体图像对 - 基于ceshiliti代码的方法"""
        try:
            # 检查立体匹配器状态
            is_valid, status_msg = self.check_stereo_matcher()
            if not is_valid:
                print(f"立体匹配器状态检查失败: {status_msg}")
                # 尝试重新初始化
                if not self.reinitialize_stereo_matcher():
                    print("无法重新初始化立体匹配器，跳过立体处理")
                    return None

            current_time = time.time()

            # 控制图像保存频率，避免过于频繁保存
            should_save_images = (current_time - self.last_save_time) >= self.image_save_interval

            if should_save_images:
                # 保存原始立体图像对（立体匹配前）
                if self.save_input_images:
                    self.save_stereo_input_images(left_img, right_img)

                self.last_save_time = current_time

            print("开始处理立体图像对...")

            # 计算视差（包含图像校正）
            disparity, left_rect, right_rect = self.compute_disparity(left_img, right_img)
            print(f"视差图计算完成，尺寸: {disparity.shape}")

            if should_save_images:
                # 保存校正后的立体图像对
                if self.save_rectified_images:
                    self.save_rectified_images(left_rect, right_rect)

            # 计算深度（传递左校正图像作为参考图像用于引导滤波）
            depth = self.disparity_to_depth(disparity, left_rect)

            if depth is not None:
                print(f"深度图计算完成，深度范围: {np.min(depth):.2f} - {np.max(depth):.2f}")

                # 创建深度彩色图
                depth_color = self.create_depth_colormap(depth)

                # 更新最新结果
                with self.depth_lock:
                    self.latest_disparity = disparity.copy()
                    self.latest_depth = depth.copy()

                # 保存深度图和视差图到文件
                self.save_depth_image(depth_color)

                # 生成并保存点云（仅PLY格式）
                # 重新计算3D点用于点云生成
                points_3d = cv2.reprojectImageTo3D(disparity, self.Q_matrix) if self.Q_matrix is not None else None
                if points_3d is not None:
                    self.save_point_cloud(points_3d, left_rect, format='ply')

                # 发射信号更新UI
                self.depth_result_signal.emit(disparity, depth, depth_color)

                # 返回处理结果
                return {
                    'disparity': disparity,
                    'depth': depth,
                    'depth_color': depth_color,
                    'points_3d': points_3d,
                    'left_rectified': left_rect,
                    'right_rectified': right_rect
                }

        except Exception as e:
            print(f"立体处理错误: {e}")



    def set_image_save_settings(self, save_input=True, save_rectified=True, save_comparison=True, interval=5):
        """设置图像保存参数
        Args:
            save_input: 是否保存原始输入图像
            save_rectified: 是否保存校正后图像
            save_comparison: 是否保存对比图像
            interval: 保存间隔（秒）
        """
        self.save_input_images = save_input
        self.save_rectified_images = save_rectified
        self.save_comparison_images = save_comparison
        self.image_save_interval = interval
        print(f"图像保存设置已更新: 输入图像={save_input}, 校正图像={save_rectified}, 对比图像={save_comparison}, 间隔={interval}秒")

    def force_save_current_pair(self, left_img, right_img):
        """强制保存当前图像对（忽略时间间隔限制）"""
        try:
            print("强制保存当前立体图像对...")

            # 保存原始图像
            if self.save_input_images:
                self.save_stereo_input_images(left_img, right_img)

            # 校正并保存校正图像
            left_rectified, right_rectified = self.rectify_images(left_img, right_img)
            if self.save_rectified_images:
                self.save_rectified_images(left_rectified, right_rectified)

            print("强制保存完成")

        except Exception as e:
            print(f"强制保存图像对错误: {e}")

    def set_depth_smoothing_params(self, enable=True, method="bilateral", **kwargs):
        """设置深度平滑参数
        Args:
            enable: 是否启用深度平滑
            method: 平滑方法 ("bilateral", "gaussian", "median", "guided", "edge_preserving")
            **kwargs: 其他参数
                bilateral_d: 双边滤波邻域直径 (默认9)
                bilateral_sigma_color: 双边滤波颜色空间标准差 (默认75)
                bilateral_sigma_space: 双边滤波坐标空间标准差 (默认75)
                gaussian_kernel_size: 高斯滤波核大小 (默认5)
                gaussian_sigma: 高斯滤波标准差 (默认1.0)
                median_kernel_size: 中值滤波核大小 (默认5)
                edge_preserving_sigma_s: 边缘保持滤波空间窗口大小 (默认50)
                edge_preserving_sigma_r: 边缘保持滤波相似性阈值 (默认0.4)
        """
        self.enable_depth_smoothing = enable
        self.smoothing_method = method

        # 更新参数
        if 'bilateral_d' in kwargs:
            self.bilateral_d = kwargs['bilateral_d']
        if 'bilateral_sigma_color' in kwargs:
            self.bilateral_sigma_color = kwargs['bilateral_sigma_color']
        if 'bilateral_sigma_space' in kwargs:
            self.bilateral_sigma_space = kwargs['bilateral_sigma_space']
        if 'gaussian_kernel_size' in kwargs:
            self.gaussian_kernel_size = kwargs['gaussian_kernel_size']
        if 'gaussian_sigma' in kwargs:
            self.gaussian_sigma = kwargs['gaussian_sigma']
        if 'median_kernel_size' in kwargs:
            self.median_kernel_size = kwargs['median_kernel_size']
        if 'edge_preserving_sigma_s' in kwargs:
            self.edge_preserving_sigma_s = kwargs['edge_preserving_sigma_s']
        if 'edge_preserving_sigma_r' in kwargs:
            self.edge_preserving_sigma_r = kwargs['edge_preserving_sigma_r']

        print(f"深度平滑参数已更新: 启用={enable}, 方法={method}")

    def get_depth_smoothing_info(self):
        """获取当前深度平滑设置信息"""
        info = {
            'enabled': self.enable_depth_smoothing,
            'method': self.smoothing_method,
            'bilateral_d': self.bilateral_d,
            'bilateral_sigma_color': self.bilateral_sigma_color,
            'bilateral_sigma_space': self.bilateral_sigma_space,
            'gaussian_kernel_size': self.gaussian_kernel_size,
            'gaussian_sigma': self.gaussian_sigma,
            'median_kernel_size': self.median_kernel_size,
            'edge_preserving_sigma_s': self.edge_preserving_sigma_s,
            'edge_preserving_sigma_r': self.edge_preserving_sigma_r
        }
        return info

    def save_depth_image(self, depth_color):
        """保存深度图像"""
        try:
            save_dir = "depth_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            filename = f"depth_{timestamp}.png"
            filepath = os.path.join(save_dir, filename)
            cv2.imwrite(filepath, depth_color)
        except Exception as e:
            print(f"Error saving depth image: {e}")

    def save_point_cloud(self, points_3d, color_image=None, format='ply'):
        """保存点云数据
        Args:
            points_3d: 3D点云数据 (H, W, 3)
            color_image: 彩色图像用于纹理 (H, W, 3)
            format: 保存格式 (仅支持 'ply')
        """
        try:
            save_dir = "point_clouds"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 应用点云处理流水线
            if self.enable_pointcloud_processing:
                print("应用点云处理流水线...")
                processed_points_3d, processed_colors = self.process_point_cloud(points_3d, color_image)
                points_3d = processed_points_3d
                if processed_colors is not None:
                    color_image = processed_colors

            # 提取有效的3D点
            height, width = points_3d.shape[:2]
            total_pixels = height * width

            # 过滤有效点（深度值在1.5m-8m范围内，根据真实标定参数调整）
            valid_mask = (points_3d[:, :, 2] >= 1500) & (points_3d[:, :, 2] <= 8000)
            valid_count = np.sum(valid_mask)

            # 计算点云覆盖率
            coverage_percentage = (valid_count / total_pixels) * 100

            if not np.any(valid_mask):
                print("No valid 3D points found for point cloud generation")
                return

            # 提取有效的XYZ坐标
            valid_points = points_3d[valid_mask]

            # 统计深度范围
            depth_values = valid_points[:, 2]
            min_depth = np.min(depth_values)
            max_depth = np.max(depth_values)
            mean_depth = np.mean(depth_values)

            # 提取对应的颜色信息
            if color_image is not None and color_image.shape[:2] == (height, width):
                if len(color_image.shape) == 3:
                    # BGR转RGB
                    color_rgb = cv2.cvtColor(color_image, cv2.COLOR_BGR2RGB)
                    valid_colors = color_rgb[valid_mask]
                else:
                    # 灰度图转RGB
                    gray_rgb = np.stack([color_image[valid_mask]] * 3, axis=1)
                    valid_colors = gray_rgb
            else:
                # 默认白色
                valid_colors = np.full((len(valid_points), 3), 255, dtype=np.uint8)

            # 输出详细统计信息
            print(f"=== 点云生成统计 ===")
            print(f"图像分辨率: {width} x {height} ({total_pixels:,} 像素)")
            print(f"有效点云数量: {valid_count:,} 点")
            print(f"点云覆盖率: {coverage_percentage:.2f}%")
            print(f"深度范围: {min_depth:.1f}mm - {max_depth:.1f}mm (有效范围: 1500-8000mm)")
            print(f"平均深度: {mean_depth:.1f}mm")
            print(f"基线距离: {getattr(self, 'baseline_mm', 'Unknown')}mm")
            print(f"相机内参 - 左相机焦距: fx={getattr(self, 'camera_matrix_left', [[0]])[0][0]:.1f}px")
            print(f"相机内参 - 右相机焦距: fx={getattr(self, 'camera_matrix_right', [[0]])[0][0]:.1f}px")

            # 深度范围检查提示
            if min_depth < 1500 or max_depth > 8000:
                print(f"警告: 检测到超出预期范围的深度值，建议检查标定参数或场景设置")

            # 保存为PLY格式
            self._save_ply(valid_points, valid_colors, save_dir, timestamp)

        except Exception as e:
            print(f"Error saving point cloud: {e}")

    def save_stereo_input_images(self, left_img, right_img):
        """保存立体匹配前的原始图像对"""
        try:
            save_dir = "stereo_input_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 保存左图像
            left_filename = f"stereo_left_input_{timestamp}.png"
            left_filepath = os.path.join(save_dir, left_filename)
            cv2.imwrite(left_filepath, left_img)

            # 保存右图像
            right_filename = f"stereo_right_input_{timestamp}.png"
            right_filepath = os.path.join(save_dir, right_filename)
            cv2.imwrite(right_filepath, right_img)

            print(f"立体输入图像已保存: {left_filename}, {right_filename}")

            # 创建并保存拼接图像用于对比显示
            self.save_stereo_comparison_image(left_img, right_img, save_dir, timestamp, "input")

        except Exception as e:
            print(f"保存立体输入图像错误: {e}")

    def save_rectified_images(self, left_rectified, right_rectified):
        """保存校正后的立体图像对"""
        try:
            save_dir = "stereo_rectified_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 保存左校正图像
            left_filename = f"stereo_left_rectified_{timestamp}.png"
            left_filepath = os.path.join(save_dir, left_filename)
            cv2.imwrite(left_filepath, left_rectified)

            # 保存右校正图像
            right_filename = f"stereo_right_rectified_{timestamp}.png"
            right_filepath = os.path.join(save_dir, right_filename)
            cv2.imwrite(right_filepath, right_rectified)

            print(f"立体校正图像已保存: {left_filename}, {right_filename}")

            # 创建并保存拼接图像用于对比显示
            self.save_stereo_comparison_image(left_rectified, right_rectified, save_dir, timestamp, "rectified")

        except Exception as e:
            print(f"保存立体校正图像错误: {e}")

    def save_stereo_comparison_image(self, left_img, right_img, save_dir, timestamp, image_type):
        """创建并保存立体图像对比图（左右拼接+水平线）"""
        try:
            # 确保两个图像尺寸一致
            if left_img.shape != right_img.shape:
                print(f"警告: 左右图像尺寸不一致 - 左: {left_img.shape}, 右: {right_img.shape}")
                # 调整到相同尺寸
                min_height = min(left_img.shape[0], right_img.shape[0])
                min_width = min(left_img.shape[1], right_img.shape[1])
                left_img = left_img[:min_height, :min_width]
                right_img = right_img[:min_height, :min_width]

            # 水平拼接左右图像
            stereo_pair = np.hstack((left_img, right_img))

            # 添加水平参考线（用于检查校正效果）
            height = stereo_pair.shape[0]
            width = stereo_pair.shape[1]

            # 在图像上绘制水平参考线
            line_color = (0, 255, 0) if len(stereo_pair.shape) == 3 else 255  # 绿色或白色
            line_thickness = 2

            # 绘制多条水平线
            for y in range(height // 10, height, height // 10):
                cv2.line(stereo_pair, (0, y), (width, y), line_color, line_thickness)

            # 在中间添加分割线
            middle_x = width // 2
            cv2.line(stereo_pair, (middle_x, 0), (middle_x, height), (0, 0, 255), 3)  # 红色分割线

            # 添加文字标注
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 2.0
            font_thickness = 3
            text_color = (255, 255, 255) if len(stereo_pair.shape) == 3 else 255

            # 左图标注
            cv2.putText(stereo_pair, "LEFT", (50, 80), font, font_scale, text_color, font_thickness)
            # 右图标注
            cv2.putText(stereo_pair, "RIGHT", (middle_x + 50, 80), font, font_scale, text_color, font_thickness)

            # 保存对比图像
            comparison_filename = f"stereo_comparison_{image_type}_{timestamp}.png"
            comparison_filepath = os.path.join(save_dir, comparison_filename)
            cv2.imwrite(comparison_filepath, stereo_pair)

            print(f"立体对比图像已保存: {comparison_filename}")

        except Exception as e:
            print(f"保存立体对比图像错误: {e}")

    def _save_ply(self, points, colors, save_dir, timestamp):
        """保存PLY格式点云"""
        filename = f"pointcloud_{timestamp}.ply"
        filepath = os.path.join(save_dir, filename)

        with open(filepath, 'w') as f:
            # PLY文件头
            f.write("ply\n")
            f.write("format ascii 1.0\n")
            f.write(f"element vertex {len(points)}\n")
            f.write("property float x\n")
            f.write("property float y\n")
            f.write("property float z\n")
            f.write("property uchar red\n")
            f.write("property uchar green\n")
            f.write("property uchar blue\n")
            f.write("end_header\n")

            # 写入点云数据
            for i in range(len(points)):
                x, y, z = points[i]
                r, g, b = colors[i]
                f.write(f"{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n")

        print(f"Point cloud saved as PLY: {filepath}")

    def process_point_cloud(self, points_3d, colors=None):
        """点云处理流水线"""
        try:
            if not self.enable_pointcloud_processing:
                return points_3d, colors

            print("开始点云处理...")

            # 转换为适合处理的格式
            processed_points = points_3d.copy()
            processed_colors = colors.copy() if colors is not None else None

            # 1. 点云降噪
            if self.pointcloud_denoising:
                processed_points, processed_colors = self.denoise_point_cloud(processed_points, processed_colors)

            # 2. 法向量估计
            if self.enable_normal_estimation:
                normals = self.estimate_normals(processed_points)
                print(f"法向量估计完成，计算了 {len(normals)} 个法向量")

            # 3. 平面拟合
            if self.enable_plane_fitting:
                plane_model, inliers = self.fit_plane(processed_points)
                if plane_model is not None:
                    print(f"平面拟合完成，内点数量: {len(inliers)}")

            # 4. 表面重建
            if self.enable_surface_reconstruction:
                mesh = self.reconstruct_surface(processed_points, processed_colors)
                if mesh is not None:
                    print("表面重建完成")

            return processed_points, processed_colors

        except Exception as e:
            print(f"点云处理错误: {e}")
            return points_3d, colors

    def denoise_point_cloud(self, points_3d, colors=None):
        """点云降噪"""
        try:
            print(f"应用点云降噪: {self.denoising_method}")

            # 将3D点云重塑为N×3格式
            original_shape = points_3d.shape
            points_reshaped = points_3d.reshape(-1, 3)

            # 过滤有效点
            valid_mask = np.all(np.isfinite(points_reshaped), axis=1) & (points_reshaped[:, 2] > 0)
            valid_points = points_reshaped[valid_mask]

            if len(valid_points) == 0:
                return points_3d, colors

            if self.denoising_method == "statistical":
                filtered_points, filtered_indices = self._denoise_statistical(valid_points)
            elif self.denoising_method == "radius":
                filtered_points, filtered_indices = self._denoise_radius(valid_points)
            elif self.denoising_method == "bilateral":
                filtered_points, filtered_indices = self._denoise_bilateral(valid_points)
            else:
                print(f"未知降噪方法: {self.denoising_method}")
                return points_3d, colors

            # 重建点云
            result_points = np.zeros_like(points_reshaped)
            result_points[valid_mask] = filtered_points
            result_points = result_points.reshape(original_shape)

            # 处理颜色
            result_colors = colors
            if colors is not None and len(filtered_indices) < len(valid_points):
                # 如果有点被过滤掉，相应地处理颜色
                colors_reshaped = colors.reshape(-1, colors.shape[-1])
                valid_colors = colors_reshaped[valid_mask]
                filtered_colors = valid_colors[filtered_indices]

                result_colors_reshaped = np.zeros_like(colors_reshaped)
                result_colors_reshaped[valid_mask] = filtered_colors
                result_colors = result_colors_reshaped.reshape(colors.shape)

            print(f"降噪完成，保留 {len(filtered_points)}/{len(valid_points)} 个点")
            return result_points, result_colors

        except Exception as e:
            print(f"点云降噪错误: {e}")
            return points_3d, colors

    def _denoise_statistical(self, points):
        """统计滤波降噪"""
        try:
            from sklearn.neighbors import NearestNeighbors

            # 构建KD树
            nbrs = NearestNeighbors(n_neighbors=min(self.statistical_neighbors, len(points)), algorithm='kd_tree').fit(points)
            distances, indices = nbrs.kneighbors(points)

            # 计算每个点到邻居的平均距离
            mean_distances = np.mean(distances[:, 1:], axis=1)  # 排除自身

            # 计算统计阈值
            global_mean = np.mean(mean_distances)
            global_std = np.std(mean_distances)
            threshold = global_mean + self.statistical_std_ratio * global_std

            # 过滤离群点
            inlier_mask = mean_distances < threshold
            filtered_points = points[inlier_mask]
            filtered_indices = np.where(inlier_mask)[0]

            return filtered_points, filtered_indices
        except ImportError:
            print("sklearn未安装，跳过统计滤波")
            return points, np.arange(len(points))

    def _denoise_radius(self, points):
        """半径滤波降噪"""
        try:
            from sklearn.neighbors import NearestNeighbors

            # 构建KD树
            nbrs = NearestNeighbors(radius=self.radius_filter_radius, algorithm='kd_tree').fit(points)

            filtered_indices = []
            for i, point in enumerate(points):
                # 查找半径内的邻居
                indices = nbrs.radius_neighbors([point], return_distance=False)[0]

                # 如果邻居数量足够，保留该点
                if len(indices) >= self.radius_filter_min_neighbors:
                    filtered_indices.append(i)

            filtered_indices = np.array(filtered_indices)
            filtered_points = points[filtered_indices]

            return filtered_points, filtered_indices
        except ImportError:
            print("sklearn未安装，跳过半径滤波")
            return points, np.arange(len(points))

    def _denoise_bilateral(self, points):
        """双边滤波降噪（简化版本）"""
        try:
            from sklearn.neighbors import NearestNeighbors

            # 构建KD树
            nbrs = NearestNeighbors(n_neighbors=min(20, len(points)), algorithm='kd_tree').fit(points)
            distances, indices = nbrs.kneighbors(points)

            filtered_points = []
            filtered_indices = []

            for i, point in enumerate(points):
                neighbor_indices = indices[i][1:]  # 排除自身
                neighbor_points = points[neighbor_indices]
                neighbor_distances = distances[i][1:]

                # 计算权重（基于距离）
                spatial_weights = np.exp(-neighbor_distances**2 / (2 * 0.1**2))

                # 计算加权平均位置
                if len(neighbor_points) > 0:
                    weighted_position = np.average(neighbor_points, axis=0, weights=spatial_weights)
                    filtered_points.append(weighted_position)
                    filtered_indices.append(i)

            filtered_points = np.array(filtered_points)
            filtered_indices = np.array(filtered_indices)

            return filtered_points, filtered_indices
        except ImportError:
            print("sklearn未安装，跳过双边滤波")
            return points, np.arange(len(points))

    def estimate_normals(self, points_3d):
        """法向量估计"""
        try:
            print("开始法向量估计...")

            # 将3D点云重塑为N×3格式
            points_reshaped = points_3d.reshape(-1, 3)

            # 过滤有效点
            valid_mask = np.all(np.isfinite(points_reshaped), axis=1) & (points_reshaped[:, 2] > 0)
            valid_points = points_reshaped[valid_mask]

            if len(valid_points) < 3:
                return None

            try:
                from sklearn.neighbors import NearestNeighbors

                # 构建KD树
                nbrs = NearestNeighbors(n_neighbors=min(self.normal_max_neighbors, len(valid_points)),
                                      algorithm='kd_tree').fit(valid_points)

                normals = []
                for point in valid_points:
                    # 查找邻居点
                    distances, indices = nbrs.kneighbors([point])
                    neighbor_points = valid_points[indices[0]]

                    # 计算法向量
                    normal = self._compute_point_normal(neighbor_points)
                    normals.append(normal)

                return np.array(normals)

            except ImportError:
                print("sklearn未安装，使用简化法向量估计")
                return self._estimate_normals_simple(valid_points)

        except Exception as e:
            print(f"法向量估计错误: {e}")
            return None

    def _compute_point_normal(self, neighbor_points):
        """计算单个点的法向量"""
        if len(neighbor_points) < 3:
            return np.array([0, 0, 1])  # 默认向上

        # 计算协方差矩阵
        centered_points = neighbor_points - np.mean(neighbor_points, axis=0)
        covariance_matrix = np.cov(centered_points.T)

        # 计算特征值和特征向量
        eigenvalues, eigenvectors = np.linalg.eigh(covariance_matrix)

        # 最小特征值对应的特征向量就是法向量
        normal = eigenvectors[:, np.argmin(eigenvalues)]

        # 确保法向量指向正确方向（向上）
        if normal[2] < 0:
            normal = -normal

        return normal / np.linalg.norm(normal)

    def _estimate_normals_simple(self, points):
        """简化的法向量估计（不依赖sklearn）"""
        normals = []

        for i, point in enumerate(points):
            # 找到最近的几个点（简化版本）
            distances = np.linalg.norm(points - point, axis=1)
            nearest_indices = np.argsort(distances)[:min(10, len(points))]
            neighbor_points = points[nearest_indices]

            # 计算法向量
            normal = self._compute_point_normal(neighbor_points)
            normals.append(normal)

        return np.array(normals)

    def fit_plane(self, points_3d):
        """平面拟合"""
        try:
            print("开始平面拟合...")

            # 将3D点云重塑为N×3格式
            points_reshaped = points_3d.reshape(-1, 3)

            # 过滤有效点
            valid_mask = np.all(np.isfinite(points_reshaped), axis=1) & (points_reshaped[:, 2] > 0)
            valid_points = points_reshaped[valid_mask]

            if len(valid_points) < 3:
                return None, None

            try:
                from sklearn.linear_model import RANSACRegressor

                # 使用RANSAC进行平面拟合
                X = valid_points[:, :2]  # x, y坐标
                y = valid_points[:, 2]   # z坐标

                ransac = RANSACRegressor(
                    residual_threshold=self.plane_distance_threshold,
                    min_samples=self.plane_ransac_n,
                    max_trials=self.plane_num_iterations,
                    random_state=42
                )

                ransac.fit(X, y)

                # 获取平面参数
                a, b = ransac.estimator_.coef_
                c = ransac.estimator_.intercept_

                # 平面方程: ax + by - z + c = 0
                plane_model = np.array([a, b, -1, c])

                # 获取内点
                inlier_mask = ransac.inlier_mask_
                inliers = valid_points[inlier_mask]

                return plane_model, inliers

            except ImportError:
                print("sklearn未安装，使用简化平面拟合")
                return self._fit_plane_simple(valid_points)

        except Exception as e:
            print(f"平面拟合错误: {e}")
            return None, None

    def _fit_plane_simple(self, points):
        """简化的平面拟合（不依赖sklearn）"""
        # 使用最小二乘法拟合平面
        if len(points) < 3:
            return None, None

        # 构建系数矩阵
        A = np.column_stack([points[:, 0], points[:, 1], np.ones(len(points))])
        b = points[:, 2]

        # 最小二乘解
        try:
            coeffs = np.linalg.lstsq(A, b, rcond=None)[0]
            plane_model = np.array([coeffs[0], coeffs[1], -1, coeffs[2]])

            # 计算内点（距离平面较近的点）
            distances = np.abs(np.dot(np.column_stack([points, np.ones(len(points))]), plane_model)) / np.linalg.norm(plane_model[:3])
            inlier_mask = distances < self.plane_distance_threshold
            inliers = points[inlier_mask]

            return plane_model, inliers

        except np.linalg.LinAlgError:
            return None, None

    def reconstruct_surface(self, points_3d, colors=None):
        """表面重建"""
        try:
            print(f"开始表面重建: {self.reconstruction_method}")

            # 将3D点云重塑为N×3格式
            points_reshaped = points_3d.reshape(-1, 3)

            # 过滤有效点
            valid_mask = np.all(np.isfinite(points_reshaped), axis=1) & (points_reshaped[:, 2] > 0)
            valid_points = points_reshaped[valid_mask]

            if len(valid_points) < 4:
                return None

            if self.reconstruction_method == "delaunay":
                return self._reconstruct_delaunay(valid_points)
            elif self.reconstruction_method == "alpha_shape":
                return self._reconstruct_alpha_shape(valid_points)
            elif self.reconstruction_method == "poisson":
                print("泊松重建需要Open3D库，使用Delaunay三角剖分")
                return self._reconstruct_delaunay(valid_points)
            else:
                print(f"未知重建方法: {self.reconstruction_method}")
                return None

        except Exception as e:
            print(f"表面重建错误: {e}")
            return None

    def _reconstruct_delaunay(self, points):
        """Delaunay三角剖分重建"""
        try:
            from scipy.spatial import Delaunay

            # 对于3D点云，我们可以投影到主平面进行2D三角剖分
            # 或者使用3D Delaunay（四面体）

            # 简化版本：投影到XY平面
            points_2d = points[:, :2]
            tri = Delaunay(points_2d)

            print(f"Delaunay三角剖分完成，生成 {len(tri.simplices)} 个三角形")
            return tri

        except ImportError:
            print("scipy未安装，无法进行Delaunay三角剖分")
            return None

    def _reconstruct_alpha_shape(self, points):
        """Alpha Shape重建"""
        try:
            from scipy.spatial import Delaunay

            # Alpha Shape是Delaunay三角剖分的子集
            # 这里提供一个简化版本
            points_2d = points[:, :2]
            tri = Delaunay(points_2d)

            # 简化的alpha过滤（基于三角形边长）
            alpha = 0.1  # alpha参数
            filtered_simplices = []

            for simplex in tri.simplices:
                # 计算三角形的最大边长
                triangle_points = points_2d[simplex]
                edge_lengths = [
                    np.linalg.norm(triangle_points[1] - triangle_points[0]),
                    np.linalg.norm(triangle_points[2] - triangle_points[1]),
                    np.linalg.norm(triangle_points[0] - triangle_points[2])
                ]
                max_edge_length = max(edge_lengths)

                # 如果最大边长小于alpha，保留这个三角形
                if max_edge_length < alpha:
                    filtered_simplices.append(simplex)

            tri.simplices = np.array(filtered_simplices)
            print(f"Alpha Shape重建完成，生成 {len(filtered_simplices)} 个三角形")
            return tri

        except ImportError:
            print("scipy未安装，无法进行Alpha Shape重建")
            return None

# 将返回的错误码转换为十六进制显示
def ToHexStr(num):
    chaDic = {10: 'a', 11: 'b', 12: 'c', 13: 'd', 14: 'e', 15: 'f'}
    hexStr = ""
    if num < 0:
        num = num + 2 ** 32
    while num >= 16:
        digit = num % 16
        hexStr = chaDic.get(digit, str(digit)) + hexStr
        num //= 16
    hexStr = chaDic.get(num, str(num)) + hexStr
    return hexStr

# Decoding Characters
def decoding_char(c_ubyte_value):
    c_char_p_value = ctypes.cast(c_ubyte_value, ctypes.c_char_p)
    try:
        decode_str = c_char_p_value.value.decode('gbk')  # Chinese characters
    except UnicodeDecodeError:
        decode_str = str(c_char_p_value.value)
    return decode_str


if __name__ == "__main__":

    global deviceList
    deviceList = MV_CC_DEVICE_INFO_LIST()

    global cam_checked_list
    cam_checked_list = []

    global obj_cam_operation
    obj_cam_operation = []

    global win_display_handles
    win_display_handles = []

    global valid_number
    valid_number = 0

    global b_is_open
    b_is_open = False

    global b_is_grab
    b_is_grab = False

    global b_is_trigger
    b_is_trigger = False

    global b_is_software_trigger
    b_is_software_trigger = False

    global b_is_hardware_trigger
    b_is_hardware_trigger = False

    global hardware_trigger_line
    hardware_trigger_line = "Line0"  # 默认硬件触发线

    global hardware_trigger_activation
    hardware_trigger_activation = "RisingEdge"  # 默认上升沿触发

    # 硬件触发监控相关变量
    global hardware_trigger_monitor_timer
    hardware_trigger_monitor_timer = None

    # 立体视觉相关变量
    global stereo_processor
    stereo_processor = None

    global stereo_enabled
    stereo_enabled = False

    global stereo_thread
    stereo_thread = None

    global stereo_running
    stereo_running = False

    # 触发后立体匹配相关变量
    global trigger_stereo_match
    trigger_stereo_match = False

    global last_trigger_images
    last_trigger_images = {"left": None, "right": None}

    # 自动保存相关变量
    global auto_save_enabled
    auto_save_enabled = True

    global auto_save_interval
    auto_save_interval = 30  # 30秒自动保存一次

    global auto_save_timer
    auto_save_timer = None

    # ch:初始化SDK | en: initialize SDK
    MvCamera.MV_CC_Initialize()

    # print info in ui
    def print_text(str_info):
        ui.textEdit.append(str_info)  # 使用 append 代替手动操作光标

    # 初始化立体视觉
    def init_stereo_vision():
        global stereo_processor
        try:
            print_text("正在初始化立体视觉系统...")
            stereo_processor = StereoVisionProcessor()

            # 检查立体视觉处理器是否正确初始化
            if (stereo_processor.camera_matrix_left is None or
                stereo_processor.camera_matrix_right is None):
                print_text("立体视觉初始化失败: 标定参数无效")
                stereo_processor = None
                return False

            # 连接信号
            stereo_processor.depth_result_signal.connect(update_depth_display)

            # 设置默认的图像保存参数
            stereo_processor.set_image_save_settings(
                save_input=True,      # 保存原始输入图像
                save_rectified=True,  # 保存校正后图像
                save_comparison=True, # 保存对比图像
                interval=10           # 每10秒保存一次
            )

            # 设置默认的深度平滑参数
            stereo_processor.set_depth_smoothing_params(
                enable=True,          # 启用深度平滑
                method="bilateral",   # 使用双边滤波
                bilateral_d=9,        # 邻域直径
                bilateral_sigma_color=75,  # 颜色空间标准差
                bilateral_sigma_space=75   # 坐标空间标准差
            )

            # 设置默认的深度处理参数
            stereo_processor.enable_hole_filling = True
            stereo_processor.hole_filling_method = "morphology"
            stereo_processor.enable_depth_interpolation = False
            stereo_processor.enable_depth_enhancement = False

            # 设置默认的点云处理参数
            stereo_processor.enable_pointcloud_processing = True
            stereo_processor.pointcloud_denoising = True
            stereo_processor.denoising_method = "statistical"
            stereo_processor.enable_normal_estimation = False
            stereo_processor.enable_plane_fitting = False
            stereo_processor.enable_surface_reconstruction = False

            print_text("立体视觉系统初始化完成")
            print_text("图像保存设置: 输入图像=开启, 校正图像=开启, 保存间隔=10秒")
            print_text("深度处理设置: 平滑=开启, 孔填充=开启, 插值=关闭, 增强=关闭")
            print_text("点云处理设置: 降噪=开启, 法向量=关闭, 平面拟合=关闭, 表面重建=关闭")
            return True
        except Exception as e:
            print_text(f"立体视觉初始化失败: {e}")
            print_text("将使用简化的立体视觉模式")
            stereo_processor = None
            return False

    def update_depth_display(disparity, depth, depth_color):
        """更新深度显示"""
        try:
            print_text(f"深度图更新: 尺寸 {depth.shape}, 深度范围 {depth.min():.1f}-{depth.max():.1f}mm")
        except Exception as e:
            print_text(f"深度显示更新错误: {e}")

    def set_stereo_image_save_settings(save_input=True, save_rectified=True, interval=5):
        """设置立体图像保存参数"""
        global stereo_processor
        if stereo_processor is not None:
            stereo_processor.set_image_save_settings(save_input, save_rectified, True, interval)
            print_text(f"立体图像保存设置已更新: 输入图像={save_input}, 校正图像={save_rectified}, 间隔={interval}秒")
        else:
            print_text("立体视觉处理器未初始化")

    def force_save_stereo_images():
        """强制保存当前立体图像对"""
        global stereo_processor, obj_cam_operation

        if stereo_processor is None:
            print_text("立体视觉处理器未初始化")
            return

        try:
            # 获取当前左右相机图像
            left_image = get_camera_image(0)
            right_image = get_camera_image(1)

            if left_image is not None and right_image is not None:
                stereo_processor.force_save_current_pair(left_image, right_image)
                print_text("已强制保存当前立体图像对")
            else:
                print_text("无法获取有效的立体图像对")

        except Exception as e:
            print_text(f"强制保存立体图像错误: {e}")

    def display_latest_stereo_images():
        """显示最新保存的立体图像（用于调试和验证）"""
        try:
            import glob

            # 查找最新的输入图像
            input_dir = "stereo_input_images"
            if os.path.exists(input_dir):
                input_files = glob.glob(os.path.join(input_dir, "stereo_comparison_input_*.png"))
                if input_files:
                    latest_input = max(input_files, key=os.path.getctime)
                    print_text(f"最新输入图像对比图: {os.path.basename(latest_input)}")

            # 查找最新的校正图像
            rectified_dir = "stereo_rectified_images"
            if os.path.exists(rectified_dir):
                rectified_files = glob.glob(os.path.join(rectified_dir, "stereo_comparison_rectified_*.png"))
                if rectified_files:
                    latest_rectified = max(rectified_files, key=os.path.getctime)
                    print_text(f"最新校正图像对比图: {os.path.basename(latest_rectified)}")

        except Exception as e:
            print_text(f"显示最新立体图像错误: {e}")

    def set_depth_smoothing_settings(enable=True, method="bilateral", **kwargs):
        """设置深度平滑参数"""
        global stereo_processor
        if stereo_processor is not None:
            stereo_processor.set_depth_smoothing_params(enable, method, **kwargs)
            print_text(f"深度平滑设置已更新: 启用={enable}, 方法={method}")
        else:
            print_text("立体视觉处理器未初始化")

    def get_depth_smoothing_info():
        """获取当前深度平滑设置信息"""
        global stereo_processor
        if stereo_processor is not None:
            info = stereo_processor.get_depth_smoothing_info()
            print_text("=== 深度平滑设置信息 ===")
            print_text(f"启用状态: {info['enabled']}")
            print_text(f"平滑方法: {info['method']}")
            print_text(f"双边滤波参数: d={info['bilateral_d']}, σ_color={info['bilateral_sigma_color']}, σ_space={info['bilateral_sigma_space']}")
            print_text(f"高斯滤波参数: kernel_size={info['gaussian_kernel_size']}, σ={info['gaussian_sigma']}")
            print_text(f"中值滤波参数: kernel_size={info['median_kernel_size']}")
            print_text(f"边缘保持滤波参数: σ_s={info['edge_preserving_sigma_s']}, σ_r={info['edge_preserving_sigma_r']}")
            return info
        else:
            print_text("立体视觉处理器未初始化")
            return None

    def test_different_smoothing_methods():
        """测试不同的深度平滑方法"""
        global stereo_processor
        if stereo_processor is None:
            print_text("立体视觉处理器未初始化")
            return

        methods = ["bilateral", "gaussian", "median", "edge_preserving"]
        print_text("开始测试不同的深度平滑方法...")

        for method in methods:
            print_text(f"切换到 {method} 平滑方法")
            stereo_processor.set_depth_smoothing_params(True, method)
            time.sleep(2)  # 等待2秒让用户观察效果

        print_text("深度平滑方法测试完成")

    def set_depth_processing_settings(**kwargs):
        """设置深度图处理参数"""
        global stereo_processor
        if stereo_processor is not None:
            # 深度插值设置
            if 'enable_interpolation' in kwargs:
                stereo_processor.enable_depth_interpolation = kwargs['enable_interpolation']
            if 'interpolation_method' in kwargs:
                stereo_processor.interpolation_method = kwargs['interpolation_method']

            # 深度增强设置
            if 'enable_enhancement' in kwargs:
                stereo_processor.enable_depth_enhancement = kwargs['enable_enhancement']
            if 'enhancement_method' in kwargs:
                stereo_processor.enhancement_method = kwargs['enhancement_method']

            # 孔填充设置
            if 'enable_hole_filling' in kwargs:
                stereo_processor.enable_hole_filling = kwargs['enable_hole_filling']
            if 'hole_filling_method' in kwargs:
                stereo_processor.hole_filling_method = kwargs['hole_filling_method']

            print_text("深度处理设置已更新")
        else:
            print_text("立体视觉处理器未初始化")

    def set_pointcloud_processing_settings(**kwargs):
        """设置点云处理参数"""
        global stereo_processor
        if stereo_processor is not None:
            # 点云降噪设置
            if 'enable_denoising' in kwargs:
                stereo_processor.pointcloud_denoising = kwargs['enable_denoising']
            if 'denoising_method' in kwargs:
                stereo_processor.denoising_method = kwargs['denoising_method']

            # 法向量估计设置
            if 'enable_normal_estimation' in kwargs:
                stereo_processor.enable_normal_estimation = kwargs['enable_normal_estimation']

            # 平面拟合设置
            if 'enable_plane_fitting' in kwargs:
                stereo_processor.enable_plane_fitting = kwargs['enable_plane_fitting']

            # 表面重建设置
            if 'enable_surface_reconstruction' in kwargs:
                stereo_processor.enable_surface_reconstruction = kwargs['enable_surface_reconstruction']
            if 'reconstruction_method' in kwargs:
                stereo_processor.reconstruction_method = kwargs['reconstruction_method']

            print_text("点云处理设置已更新")
        else:
            print_text("立体视觉处理器未初始化")

    def get_processing_info():
        """获取当前所有处理设置信息"""
        global stereo_processor
        if stereo_processor is not None:
            print_text("=== 深度图和点云处理设置信息 ===")

            # 深度平滑信息
            print_text(f"深度平滑: {stereo_processor.enable_depth_smoothing} ({stereo_processor.smoothing_method})")

            # 深度处理信息
            print_text(f"深度插值: {stereo_processor.enable_depth_interpolation} ({stereo_processor.interpolation_method})")
            print_text(f"深度增强: {stereo_processor.enable_depth_enhancement} ({stereo_processor.enhancement_method})")
            print_text(f"孔填充: {stereo_processor.enable_hole_filling} ({stereo_processor.hole_filling_method})")

            # 点云处理信息
            print_text(f"点云处理: {stereo_processor.enable_pointcloud_processing}")
            print_text(f"点云降噪: {stereo_processor.pointcloud_denoising} ({stereo_processor.denoising_method})")
            print_text(f"法向量估计: {stereo_processor.enable_normal_estimation}")
            print_text(f"平面拟合: {stereo_processor.enable_plane_fitting}")
            print_text(f"表面重建: {stereo_processor.enable_surface_reconstruction} ({stereo_processor.reconstruction_method})")

        else:
            print_text("立体视觉处理器未初始化")

    def create_processing_pipeline(pipeline_config):
        """创建自定义处理流水线
        Args:
            pipeline_config: 流水线配置字典
            例如: {
                'depth_smoothing': {'enable': True, 'method': 'bilateral'},
                'hole_filling': {'enable': True, 'method': 'morphology'},
                'depth_enhancement': {'enable': False},
                'pointcloud_denoising': {'enable': True, 'method': 'statistical'},
                'normal_estimation': {'enable': True},
                'plane_fitting': {'enable': False}
            }
        """
        global stereo_processor
        if stereo_processor is not None:
            print_text("配置处理流水线...")

            # 配置深度处理
            if 'depth_smoothing' in pipeline_config:
                config = pipeline_config['depth_smoothing']
                stereo_processor.enable_depth_smoothing = config.get('enable', True)
                if 'method' in config:
                    stereo_processor.smoothing_method = config['method']

            if 'hole_filling' in pipeline_config:
                config = pipeline_config['hole_filling']
                stereo_processor.enable_hole_filling = config.get('enable', True)
                if 'method' in config:
                    stereo_processor.hole_filling_method = config['method']

            if 'depth_enhancement' in pipeline_config:
                config = pipeline_config['depth_enhancement']
                stereo_processor.enable_depth_enhancement = config.get('enable', False)
                if 'method' in config:
                    stereo_processor.enhancement_method = config['method']

            if 'depth_interpolation' in pipeline_config:
                config = pipeline_config['depth_interpolation']
                stereo_processor.enable_depth_interpolation = config.get('enable', False)
                if 'method' in config:
                    stereo_processor.interpolation_method = config['method']

            # 配置点云处理
            if 'pointcloud_denoising' in pipeline_config:
                config = pipeline_config['pointcloud_denoising']
                stereo_processor.pointcloud_denoising = config.get('enable', True)
                if 'method' in config:
                    stereo_processor.denoising_method = config['method']

            if 'normal_estimation' in pipeline_config:
                config = pipeline_config['normal_estimation']
                stereo_processor.enable_normal_estimation = config.get('enable', False)

            if 'plane_fitting' in pipeline_config:
                config = pipeline_config['plane_fitting']
                stereo_processor.enable_plane_fitting = config.get('enable', False)

            if 'surface_reconstruction' in pipeline_config:
                config = pipeline_config['surface_reconstruction']
                stereo_processor.enable_surface_reconstruction = config.get('enable', False)
                if 'method' in config:
                    stereo_processor.reconstruction_method = config['method']

            print_text("处理流水线配置完成")
            get_processing_info()  # 显示当前配置
        else:
            print_text("立体视觉处理器未初始化")

    def apply_preset_pipeline(preset_name):
        """应用预设的处理流水线
        Args:
            preset_name: 预设名称
                - "basic": 基础处理（平滑+孔填充）
                - "enhanced": 增强处理（平滑+孔填充+增强+降噪）
                - "research": 研究级处理（全功能开启）
                - "fast": 快速处理（最小处理）
                - "quality": 高质量处理（重点质量优化）
        """
        presets = {
            "basic": {
                'depth_smoothing': {'enable': True, 'method': 'bilateral'},
                'hole_filling': {'enable': True, 'method': 'morphology'},
                'depth_enhancement': {'enable': False},
                'depth_interpolation': {'enable': False},
                'pointcloud_denoising': {'enable': True, 'method': 'statistical'},
                'normal_estimation': {'enable': False},
                'plane_fitting': {'enable': False},
                'surface_reconstruction': {'enable': False}
            },
            "enhanced": {
                'depth_smoothing': {'enable': True, 'method': 'bilateral'},
                'hole_filling': {'enable': True, 'method': 'inpaint'},
                'depth_enhancement': {'enable': True, 'method': 'clahe'},
                'depth_interpolation': {'enable': True, 'method': 'linear'},
                'pointcloud_denoising': {'enable': True, 'method': 'statistical'},
                'normal_estimation': {'enable': True},
                'plane_fitting': {'enable': False},
                'surface_reconstruction': {'enable': False}
            },
            "research": {
                'depth_smoothing': {'enable': True, 'method': 'bilateral'},
                'hole_filling': {'enable': True, 'method': 'inpaint'},
                'depth_enhancement': {'enable': True, 'method': 'clahe'},
                'depth_interpolation': {'enable': True, 'method': 'cubic'},
                'pointcloud_denoising': {'enable': True, 'method': 'statistical'},
                'normal_estimation': {'enable': True},
                'plane_fitting': {'enable': True},
                'surface_reconstruction': {'enable': True, 'method': 'delaunay'}
            },
            "fast": {
                'depth_smoothing': {'enable': True, 'method': 'gaussian'},
                'hole_filling': {'enable': False},
                'depth_enhancement': {'enable': False},
                'depth_interpolation': {'enable': False},
                'pointcloud_denoising': {'enable': False},
                'normal_estimation': {'enable': False},
                'plane_fitting': {'enable': False},
                'surface_reconstruction': {'enable': False}
            },
            "quality": {
                'depth_smoothing': {'enable': True, 'method': 'edge_preserving'},
                'hole_filling': {'enable': True, 'method': 'inpaint'},
                'depth_enhancement': {'enable': True, 'method': 'histogram_eq'},
                'depth_interpolation': {'enable': True, 'method': 'cubic'},
                'pointcloud_denoising': {'enable': True, 'method': 'bilateral'},
                'normal_estimation': {'enable': True},
                'plane_fitting': {'enable': False},
                'surface_reconstruction': {'enable': False}
            }
        }

        if preset_name in presets:
            print_text(f"应用预设流水线: {preset_name}")
            create_processing_pipeline(presets[preset_name])
        else:
            print_text(f"未知预设: {preset_name}")
            print_text("可用预设: basic, enhanced, research, fast, quality")

    def show_processing_examples():
        """显示处理方法使用示例"""
        print_text("=== 深度图和点云处理使用示例 ===")
        print_text("")
        print_text("1. 应用预设流水线:")
        print_text("   apply_preset_pipeline('basic')      # 基础处理")
        print_text("   apply_preset_pipeline('enhanced')   # 增强处理")
        print_text("   apply_preset_pipeline('research')   # 研究级处理")
        print_text("")
        print_text("2. 自定义深度处理:")
        print_text("   set_depth_processing_settings(")
        print_text("       enable_interpolation=True,")
        print_text("       interpolation_method='linear',")
        print_text("       enable_enhancement=True,")
        print_text("       enhancement_method='clahe'")
        print_text("   )")
        print_text("")
        print_text("3. 自定义点云处理:")
        print_text("   set_pointcloud_processing_settings(")
        print_text("       enable_denoising=True,")
        print_text("       denoising_method='statistical',")
        print_text("       enable_normal_estimation=True")
        print_text("   )")
        print_text("")
        print_text("4. 创建自定义流水线:")
        print_text("   pipeline_config = {")
        print_text("       'depth_smoothing': {'enable': True, 'method': 'bilateral'},")
        print_text("       'hole_filling': {'enable': True, 'method': 'morphology'},")
        print_text("       'pointcloud_denoising': {'enable': True, 'method': 'statistical'}")
        print_text("   }")
        print_text("   create_processing_pipeline(pipeline_config)")
        print_text("")
        print_text("5. 查看当前设置:")
        print_text("   get_processing_info()")

    def get_camera_image(camera_index):
        """获取指定相机的图像"""
        global obj_cam_operation
        try:
            if (camera_index < len(obj_cam_operation) and
                obj_cam_operation[camera_index] != 0 and
                obj_cam_operation[camera_index].buf_save_image is not None):

                frame_info = obj_cam_operation[camera_index].st_frame_info
                if frame_info.nHeight > 0 and frame_info.nWidth > 0:
                    # 转换图像数据为numpy数组
                    image_data = np.frombuffer(
                        obj_cam_operation[camera_index].buf_save_image,
                        dtype=np.uint8
                    )

                    # 根据像素格式重塑图像
                    # 使用数值常量代替可能未定义的常量
                    try:
                        # 尝试获取像素格式常量，如果失败则使用数值
                        mono8_format = getattr(sys.modules[__name__], 'PixelType_Gvsp_Mono8', 0x01080001)
                        rgb8_format = getattr(sys.modules[__name__], 'PixelType_Gvsp_RGB8_Packed', 0x02180014)

                        if frame_info.enPixelType == mono8_format:
                            image = image_data.reshape((frame_info.nHeight, frame_info.nWidth))
                            return cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                        elif frame_info.enPixelType == rgb8_format:
                            image = image_data.reshape((frame_info.nHeight, frame_info.nWidth, 3))
                            return cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
                        else:
                            # 默认处理为单通道
                            image = image_data.reshape((frame_info.nHeight, frame_info.nWidth))
                            return cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                    except Exception as e:
                        print_text(f"像素格式处理错误: {e}")
                        # 简单的默认处理
                        try:
                            image = image_data.reshape((frame_info.nHeight, frame_info.nWidth))
                            return cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                        except:
                            return None
            return None
        except Exception as e:
            print_text(f"获取相机{camera_index}图像失败: {e}")
            return None

    def start_stereo_vision():
        """启动立体视觉"""
        global stereo_enabled, stereo_processor, stereo_thread, stereo_running

        if not b_is_grab:
            print_text("请先启动相机采集")
            return

        # 检查是否至少选择了两个相机
        selected_count = sum(1 for i in range(4) if i < len(obj_cam_operation) and obj_cam_operation[i] != 0)
        if selected_count < 2:
            print_text("立体视觉需要至少两个相机")
            return

        if stereo_processor is None:
            if not init_stereo_vision():
                return

        # 启动立体处理
        stereo_processor.start_processing()

        # 启动图像获取线程
        if not stereo_running:
            stereo_running = True
            stereo_thread = threading.Thread(target=stereo_image_acquisition_loop)
            stereo_thread.daemon = True
            stereo_thread.start()

        stereo_enabled = True
        print_text("立体视觉已启动")
        print_text("立体图像将自动保存到以下目录:")
        print_text("  - 原始图像: stereo_input_images/")
        print_text("  - 校正图像: stereo_rectified_images/")
        print_text("  - 深度图像: depth_images/")
        print_text("  - 点云数据: point_clouds/ (PLY格式)")
        print_text("")
        print_text("=== 深度图和点云处理功能已启用 ===")
        print_text("深度图处理方法:")
        print_text("  - 深度平滑: 双边滤波、高斯滤波、中值滤波、边缘保持滤波、引导滤波")
        print_text("  - 插值法: 线性插值、三次插值、最近邻插值、修复插值")
        print_text("  - 深度增强: 直方图均衡化、CLAHE、伽马校正")
        print_text("  - 孔填充: 形态学操作、图像修复、插值填充")
        print_text("")
        print_text("点云处理方法:")
        print_text("  - 降噪: 统计滤波、半径滤波、双边滤波")
        print_text("  - 表面重建: Delaunay三角剖分、Alpha Shape、泊松重建")
        print_text("  - 法向量估计: 基于邻域的法向量计算")
        print_text("  - 平面拟合: RANSAC平面拟合")
        print_text("")
        print_text("控制函数:")
        print_text("  - set_depth_processing_settings() 设置深度处理参数")
        print_text("  - set_pointcloud_processing_settings() 设置点云处理参数")
        print_text("  - create_processing_pipeline() 创建自定义处理流水线")
        print_text("  - get_processing_info() 查看当前所有设置")
        print_text("  - force_save_stereo_images() 强制保存当前图像对")

    def stop_stereo_vision():
        """停止立体视觉"""
        global stereo_enabled, stereo_processor, stereo_running

        stereo_enabled = False
        stereo_running = False

        if stereo_processor:
            stereo_processor.stop_processing()

        print_text("立体视觉已停止")

    def start_continuous_stereo_matching():
        """启动持续立体匹配"""
        global stereo_enabled, stereo_processor, stereo_thread, stereo_running

        if not b_is_grab:
            print_text("请先启动相机采集")
            return

        # 检查是否有足够的相机
        selected_count = sum(1 for i in range(4) if i < len(obj_cam_operation) and obj_cam_operation[i] != 0)
        if selected_count < 2:
            print_text("持续立体匹配需要至少两个相机")
            return

        if stereo_processor is None:
            if not init_stereo_vision():
                return

        # 启动立体处理
        stereo_processor.start_processing()

        # 启动图像获取线程
        if not stereo_running:
            stereo_running = True
            stereo_thread = threading.Thread(target=stereo_image_acquisition_loop)
            stereo_thread.daemon = True
            stereo_thread.start()

        stereo_enabled = True
        print_text("持续立体匹配已启动")
        print_text("立体图像将自动保存到以下目录:")
        print_text("  - 原始图像: stereo_input_images/")
        print_text("  - 校正图像: stereo_rectified_images/")
        print_text("  - 深度图像: depth_images/")
        print_text("  - 点云数据: point_clouds/")

    def stop_continuous_stereo_matching():
        """停止持续立体匹配"""
        stop_stereo_vision()

    def toggle_continuous_stereo_matching():
        """切换持续立体匹配状态"""
        global stereo_enabled

        if stereo_enabled:
            stop_continuous_stereo_matching()
            print_text("持续立体匹配已停止")
        else:
            start_continuous_stereo_matching()

    def get_stereo_status():
        """获取立体匹配状态信息"""
        global stereo_enabled, stereo_processor

        status_info = []
        status_info.append("=== 立体匹配状态 ===")

        if stereo_processor is None:
            status_info.append("立体视觉处理器: 未初始化")
        else:
            status_info.append("立体视觉处理器: 已初始化")
            status_info.append(f"标定状态: {'已加载' if stereo_processor.calibration_available else '未加载'}")
            status_info.append(f"立体匹配器: {'可用' if getattr(stereo_processor, 'stereo_available', False) else '不可用'}")

        status_info.append(f"持续匹配状态: {'运行中' if stereo_enabled else '已停止'}")

        # 检查相机状态
        camera_count = sum(1 for i in range(4) if i < len(obj_cam_operation) and obj_cam_operation[i] != 0)
        status_info.append(f"可用相机数量: {camera_count}")

        if camera_count >= 2:
            status_info.append("✓ 可以进行立体匹配")
        else:
            status_info.append("✗ 需要至少2个相机进行立体匹配")

        # 检查触发图像状态
        if last_trigger_images["left"] is not None and last_trigger_images["right"] is not None:
            status_info.append("✓ 有可用的触发图像")
        else:
            status_info.append("○ 无触发图像（将使用实时图像）")

        return "\n".join(status_info)

    def show_stereo_status():
        """显示立体匹配状态"""
        status = get_stereo_status()
        print_text(status)

    def stereo_image_acquisition_loop():
        """立体图像获取循环"""
        global stereo_running, stereo_processor, obj_cam_operation

        while stereo_running:
            try:
                # 获取左右相机图像（假设相机0是左相机，相机1是右相机）
                left_image = get_camera_image(0)
                right_image = get_camera_image(1)

                if left_image is not None and right_image is not None and stereo_processor is not None:
                    # 添加到立体处理队列
                    stereo_processor.add_image_pair(left_image, right_image)

                time.sleep(0.033)  # 约30FPS

            except Exception as e:
                print_text(f"立体图像获取错误: {e}")
                time.sleep(0.1)

    # ch:枚举相机 | en:enum devices
    def enum_devices():
        global deviceList
        global valid_number
        deviceList = MV_CC_DEVICE_INFO_LIST()
        n_layer_type = (MV_GIGE_DEVICE | MV_USB_DEVICE
                        | MV_GENTL_GIGE_DEVICE | MV_GENTL_CAMERALINK_DEVICE
                        | MV_GENTL_CXP_DEVICE | MV_GENTL_XOF_DEVICE)
        ret = MvCamera.MV_CC_EnumDevicesEx2(n_layer_type, deviceList, '', SortMethod_SerialNumber)
        if ret != 0:
            str_error = "Enum devices fail! ret = :" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", str_error, QMessageBox.Ok)
            return ret

        if deviceList.nDeviceNum == 0:
            QMessageBox.warning(mainWindow, "Info", "Find no device", QMessageBox.Ok)
            return ret
        print_text("Find %d devices!" % deviceList.nDeviceNum)

        valid_number = 0
        for i in range(0, 4):
            if (i < deviceList.nDeviceNum) is True:
                serial_number = ""
                model_name = ""
                mvcc_dev_info = cast(deviceList.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
                if mvcc_dev_info.nTLayerType == MV_GIGE_DEVICE or mvcc_dev_info.nTLayerType == MV_GENTL_GIGE_DEVICE:
                    print("\ngige device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    nip1 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0xff000000) >> 24)
                    nip2 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x00ff0000) >> 16)
                    nip3 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x0000ff00) >> 8)
                    nip4 = (mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x000000ff)
                    print("current ip: %d.%d.%d.%d " % (nip1, nip2, nip3, nip4))

                    for per in mvcc_dev_info.SpecialInfo.stGigEInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)

                elif mvcc_dev_info.nTLayerType == MV_USB_DEVICE:
                    print("\nu3v device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_CAMERALINK_DEVICE:
                    print("\nCML device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stCMLInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stCMLInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stCMLInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_CXP_DEVICE:
                    print("\nCXP device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stCXPInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stCXPInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stCXPInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_XOF_DEVICE:
                    print("\nXoF device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stXoFInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stXoFInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stXoFInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)

                button_by_id = cam_button_group.button(i)
                button_by_id.setText("(" + serial_number + ")" + model_name)
                button_by_id.setEnabled(True)
                valid_number = valid_number + 1
            else:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(False)

    def cam_check_box_clicked():
        global cam_checked_list
        cam_checked_list = []
        for i in range(0, 4):
            button = cam_button_group.button(i)
            if button.isChecked() is True:
                cam_checked_list.append(True)
            else:
                cam_checked_list.append(False)

    def enable_ui_controls():
        global b_is_open
        global b_is_grab
        global b_is_trigger
        global b_is_software_trigger
        global b_is_hardware_trigger
        ui.pushButton_enum.setEnabled(not b_is_open)
        ui.pushButton_open.setEnabled(not b_is_open)
        ui.pushButton_close.setEnabled(b_is_open)
        result1 = False if b_is_grab else b_is_open
        result2 = b_is_open if b_is_grab else False
        ui.pushButton_startGrab.setEnabled(result1)
        ui.pushButton_stopGrab.setEnabled(result2)
        ui.pushButton_saveImg.setEnabled(result2)
        ui.radioButton_continuous.setEnabled(b_is_open)
        ui.radioButton_trigger.setEnabled(b_is_open)
        ui.pushButton_setParams.setEnabled(b_is_open)
        ui.lineEdit_gain.setEnabled(b_is_open)
        ui.lineEdit_frameRate.setEnabled(b_is_open)
        ui.lineEdit_exposureTime.setEnabled(b_is_open)
        result3 = b_is_open if b_is_trigger else False
        ui.pushButton_triggerOnce.setEnabled(b_is_software_trigger and result3)
        ui.checkBox_software_trigger.setEnabled(b_is_trigger)
        ui.checkBox_hardware_trigger.setEnabled(b_is_trigger)

    def open_devices():
        global deviceList
        global obj_cam_operation
        global b_is_open
        global valid_number
        global cam_checked_list
        b_checked = 0
        if b_is_open is True:
            return

        if len(cam_checked_list) <= 0:
            print_text("please select a camera !")
            return
        obj_cam_operation = []
        for i in range(0, 4):
            if cam_checked_list[i] is True:
                b_checked = True
                camObj = MvCamera()
                obj_cam_operation.append(CameraOperation(camObj, deviceList, i))
                ret = obj_cam_operation[i].open_device()
                if 0 != ret:
                    obj_cam_operation.pop()
                    print_text("open cam %d fail ret[0x%x]" % (i, ret))
                    continue
                else:
                    b_is_open = True
            else:
                obj_cam_operation.append(0)
        if b_checked is False:
            print_text("please select a camera !")
            return
        if b_is_open is False:
            print_text("no camera opened successfully !")
            return
        else:
            ui.radioButton_continuous.setChecked(True)
            enable_ui_controls()

        for i in range(0, 4):
            if(i < valid_number) is True:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(not b_is_open)

    def software_trigger_check_box_clicked():
        global obj_cam_operation
        global b_is_software_trigger
        global b_is_hardware_trigger
        global hardware_trigger_line
        global hardware_trigger_activation

        if (ui.checkBox_software_trigger.isChecked()) is True:
            b_is_software_trigger = True
            b_is_hardware_trigger = False
            ui.checkBox_hardware_trigger.setChecked(False)

            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_trigger_source("software")
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger source: software  fail! ret = ' + ToHexStr(ret))
                    else:
                        print_text('camera' + str(i) + ' set to software trigger mode')
        else:
            b_is_software_trigger = False

        enable_ui_controls()

    def hardware_trigger_check_box_clicked():
        global obj_cam_operation
        global b_is_hardware_trigger
        global b_is_software_trigger
        global hardware_trigger_line
        global hardware_trigger_activation

        if (ui.checkBox_hardware_trigger.isChecked()) is True:
            b_is_hardware_trigger = True
            b_is_software_trigger = False
            ui.checkBox_software_trigger.setChecked(False)

            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    # 设置硬件触发源
                    ret = obj_cam_operation[i].set_trigger_source("hardware", hardware_trigger_line)
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger source: hardware fail! ret = ' + ToHexStr(ret))
                        continue

                    # 设置触发极性
                    ret = obj_cam_operation[i].set_trigger_activation(hardware_trigger_activation)
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger activation fail! ret = ' + ToHexStr(ret))
                        continue

                    # 启用硬件触发检测
                    obj_cam_operation[i].enable_hardware_trigger_detection()
                    obj_cam_operation[i].prepare_for_hardware_trigger()

                    print_text('camera' + str(i) + f' set to hardware trigger mode ({hardware_trigger_line}, {hardware_trigger_activation})')
        else:
            b_is_hardware_trigger = False
            # 禁用硬件触发检测
            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    obj_cam_operation[i].disable_hardware_trigger_detection()

        enable_ui_controls()

        # 如果启用了硬件触发，开始监控触发图像
        if b_is_hardware_trigger:
            start_hardware_trigger_monitoring()
        else:
            stop_hardware_trigger_monitoring()

    def start_hardware_trigger_monitoring():
        """开始硬件触发监控"""
        global hardware_trigger_monitor_timer

        if hardware_trigger_monitor_timer is not None:
            hardware_trigger_monitor_timer.stop()

        hardware_trigger_monitor_timer = QTimer()
        hardware_trigger_monitor_timer.timeout.connect(check_hardware_trigger_images)
        hardware_trigger_monitor_timer.start(100)  # 每100ms检查一次
        print_text("硬件触发监控已启动")

    def stop_hardware_trigger_monitoring():
        """停止硬件触发监控"""
        global hardware_trigger_monitor_timer

        if hardware_trigger_monitor_timer is not None:
            hardware_trigger_monitor_timer.stop()
            hardware_trigger_monitor_timer = None
            print_text("硬件触发监控已停止")

    def check_hardware_trigger_images():
        """检查硬件触发图像"""
        global obj_cam_operation, last_trigger_images

        try:
            # 检查是否有相机捕获了触发图像
            ready_cameras = []
            for i in range(0, 4):
                if (i < len(obj_cam_operation) and obj_cam_operation[i] != 0 and
                    obj_cam_operation[i].is_trigger_image_ready()):
                    ready_cameras.append(i)

            if ready_cameras:
                print_text(f"检测到硬件触发图像: cameras {ready_cameras}")
                # 立即处理触发图像
                process_hardware_trigger_images(ready_cameras)

        except Exception as e:
            print_text(f"硬件触发检查错误: {e}")

    def process_hardware_trigger_images(ready_cameras):
        """处理硬件触发图像"""
        global last_trigger_images

        try:
            # 清空上次的图像
            last_trigger_images = {"left": None, "right": None}

            # 创建保存目录
            save_dir = "hardware_trigger_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            saved_count = 0

            # 处理每个准备好的相机
            for camera_id in ready_cameras:
                try:
                    image_data, frame_info = obj_cam_operation[camera_id].get_trigger_image_data()

                    if image_data is None or frame_info is None:
                        continue

                    # 转换图像数据
                    image_array = np.frombuffer(image_data, dtype=np.uint8)

                    if len(image_array) >= frame_info.nHeight * frame_info.nWidth:
                        image = image_array[:frame_info.nHeight * frame_info.nWidth].reshape(
                            (frame_info.nHeight, frame_info.nWidth)
                        )

                        # 保存图像
                        filename = f"hw_trigger_cam{camera_id}_{timestamp}.png"
                        filepath = os.path.join(save_dir, filename)
                        cv2.imwrite(filepath, image)

                        saved_count += 1
                        print_text(f'camera{camera_id} hardware trigger image saved: {filename}')

                        # 保存到立体匹配变量
                        if camera_id == 0:
                            last_trigger_images["left"] = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                        elif camera_id == 1:
                            last_trigger_images["right"] = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)

                except Exception as e:
                    print_text(f'camera{camera_id} hardware trigger save error: {e}')

            # 清空触发图像缓存
            for camera_id in ready_cameras:
                obj_cam_operation[camera_id].clear_trigger_image()
                # 重新准备接收下一次硬件触发
                obj_cam_operation[camera_id].prepare_for_hardware_trigger()

            if saved_count > 0:
                print_text(f"硬件触发图像保存完成！共保存 {saved_count} 张图像")
                print_text("确保每次硬件触发只保存一帧图像")

                # 检查双目图像
                if last_trigger_images["left"] is not None and last_trigger_images["right"] is not None:
                    print_text("硬件触发双目图像捕获成功，可以进行立体匹配")
                    if trigger_stereo_match:
                        perform_trigger_stereo_match()

        except Exception as e:
            print_text(f"硬件触发图像处理错误: {e}")

    def radio_button_clicked(button):
        global obj_cam_operation
        global b_is_trigger
        button_id = raio_button_group.id(button)
        if (button_id == 0) is True:
            b_is_trigger = False
            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_trigger_mode("continuous")
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger mode: continuous fail! ret = ' + ToHexStr(ret))
            enable_ui_controls()

        else:
            b_is_trigger = True
            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_trigger_mode("triggermode")
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger on fail! ret = ' + ToHexStr(ret))
            enable_ui_controls()

    def close_devices():
        global b_is_open
        global obj_cam_operation
        global valid_number

        if b_is_open is False:
            return
        if b_is_grab is True:
            stop_grabbing()
        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].close_device()
                if 0 != ret:
                    print_text('camera' + str(i) + ' close device fail! ret = ' + ToHexStr(ret))

            if i < valid_number:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(True)
        b_is_open = False
        enable_ui_controls()

    def start_grabbing():
        global obj_cam_operation
        global win_display_handles
        global b_is_open
        global b_is_grab

        if (not b_is_open) or (b_is_grab is True):
            return

        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].start_grabbing(i, win_display_handles[i])
                if 0 != ret:
                    print_text('camera' + str(i) + ' start grabbing fail! ret = ' + ToHexStr(ret))
                b_is_grab = True
        enable_ui_controls()

        # 检查是否可以进行立体视觉（如果有两个或以上相机）
        selected_count = sum(1 for i in range(4) if i < len(obj_cam_operation) and obj_cam_operation[i] != 0)
        if selected_count >= 2:
            print_text("检测到多个相机，立体视觉功能可用")
            print_text("点击'立体匹配'按钮可进行单次立体匹配")
            # 只初始化立体视觉处理器，不启动持续处理
            if stereo_processor is None:
                init_stereo_vision()

        # 启动自动保存
        if auto_save_enabled:
            start_auto_save()

    def stop_grabbing():
        global b_is_grab
        global obj_cam_operation
        global b_is_open

        if (not b_is_open) or (b_is_grab is False):
            return

        # 停止立体视觉
        stop_stereo_vision()

        # 停止自动保存
        stop_auto_save()

        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].stop_grabbing()
                if 0 != ret:
                    print_text('camera' + str(i) + ' stop grabbing fail!ret = ' + ToHexStr(ret))
                b_is_grab = False
        enable_ui_controls()

    # ch:存图 | en:save image - 增强版本支持触发图像保存
    def save_bmp():
        global b_is_grab
        global obj_cam_operation
        global last_trigger_images

        # 优先保存触发图像，如果没有则保存当前图像
        if (last_trigger_images["left"] is not None or
            last_trigger_images["right"] is not None):
            print_text("保存触发后的图像...")
            save_trigger_images_manual()
            return

        if b_is_grab is False:
            print_text("无法保存图像：相机未在采集状态")
            return

        try:
            # 创建保存目录
            save_dir = "saved_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            saved_count = 0

            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    # 检查图像缓冲区是否有有效的图像数据
                    if (obj_cam_operation[i].buf_save_image is None or
                        obj_cam_operation[i].st_frame_info is None):
                        print_text(f'camera{i} has no image data to save.')
                        continue

                    frame_info = obj_cam_operation[i].st_frame_info

                    # 检查图像尺寸是否有效
                    if frame_info.nHeight <= 0 or frame_info.nWidth <= 0:
                        print_text(f'camera{i} frame info has invalid dimensions')
                        continue

                    try:
                        # 使用改进的保存方法，传递保存目录
                        ret = obj_cam_operation[i].save_bmp(save_dir)
                        if 0 == ret:
                            saved_count += 1
                            print_text(f'camera{i} image saved successfully to {save_dir}')
                        elif ret != -1:  # -1 表示没有图像数据，不是真正的错误
                            print_text('camera' + str(i) + ' save bmp fail!ret = ' + ToHexStr(ret))

                    except Exception as e:
                        print_text(f'camera{i} save error: {e}')
                        continue

            if saved_count > 0:
                print_text(f"成功保存 {saved_count} 张图像")
            else:
                print_text("未能保存任何图像")

        except Exception as e:
            print_text(f"保存图像错误: {e}")

    def save_trigger_images_manual():
        """手动保存触发后的图像"""
        global last_trigger_images

        try:
            save_dir = "trigger_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            saved_count = 0

            if last_trigger_images["left"] is not None:
                filename = f"trigger_left_{timestamp}.png"
                filepath = os.path.join(save_dir, filename)
                cv2.imwrite(filepath, last_trigger_images["left"])
                saved_count += 1
                print_text(f'Left trigger image saved: {filename}')

            if last_trigger_images["right"] is not None:
                filename = f"trigger_right_{timestamp}.png"
                filepath = os.path.join(save_dir, filename)
                cv2.imwrite(filepath, last_trigger_images["right"])
                saved_count += 1
                print_text(f'Right trigger image saved: {filename}')

            if saved_count > 0:
                print_text(f"成功保存 {saved_count} 张触发图像到 {save_dir}")
                # 清空触发图像缓存
                last_trigger_images = {"left": None, "right": None}
            else:
                print_text("没有触发图像可保存")

        except Exception as e:
            print_text(f"保存触发图像错误: {e}")

    def is_float(str_value):
        try:
            float(str_value)
            return True
        except ValueError:
            return False

    def set_parameters():
        global obj_cam_operation
        global b_is_open
        if b_is_open is False:
            return

        frame_rate = ui.lineEdit_frameRate.text()
        exposure_time = ui.lineEdit_exposureTime.text()
        gain = ui.lineEdit_gain.text()

        if is_float(frame_rate) is False or is_float(exposure_time) is False or is_float(gain) is False:
            print_text("parameters is valid, please check")
            return

        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].set_exposure_time(exposure_time)
                if ret != 0:
                    print_text('camera' + str(i) + ' Set exposure time failed ret:' + ToHexStr(ret))
                ret = obj_cam_operation[i].set_gain(gain)
                if ret != 0:
                    print_text('camera' + str(i) + ' Set gain failed ret:' + ToHexStr(ret))
                ret = obj_cam_operation[i].set_frame_rate(frame_rate)
                if ret != 0:
                    print_text('camera' + str(i) + ' set acquisition frame rate failed ret:' + ToHexStr(ret))

    def software_trigger_once():
        global last_trigger_images

        # 清空上次触发的图像
        last_trigger_images = {"left": None, "right": None}

        # 首先清空所有相机的触发图像缓存
        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                obj_cam_operation[i].clear_trigger_image()

        # 执行软件触发
        triggered_cameras = []
        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].trigger_once()
                if ret != 0:
                    print_text('camera' + str(i) + ' TriggerSoftware failed ret:' + ToHexStr(ret))
                else:
                    print_text('camera' + str(i) + ' software trigger executed')
                    triggered_cameras.append(i)

        if triggered_cameras:
            # 使用同步等待机制，确保获取触发后的第一帧
            QTimer.singleShot(100, lambda: wait_and_capture_trigger_images(triggered_cameras))
        else:
            print_text("No cameras were successfully triggered")

    def wait_and_capture_trigger_images(triggered_cameras):
        """等待并捕获触发图像 - 确保只获取触发后的第一帧"""
        global last_trigger_images, obj_cam_operation

        try:
            print_text("等待触发图像准备就绪...")

            # 等待所有触发的相机图像准备就绪
            ready_cameras = []
            max_wait_time = 2000  # 最大等待2秒

            for camera_id in triggered_cameras:
                if obj_cam_operation[camera_id].wait_for_trigger_image(max_wait_time):
                    ready_cameras.append(camera_id)
                    print_text(f'camera{camera_id} trigger image ready')
                else:
                    print_text(f'camera{camera_id} trigger image timeout')

            if not ready_cameras:
                print_text("没有相机的触发图像准备就绪")
                return

            # 创建保存目录
            save_dir = "trigger_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            saved_count = 0

            # 获取并保存触发图像
            for camera_id in ready_cameras:
                try:
                    image_data, frame_info = obj_cam_operation[camera_id].get_trigger_image_data()

                    if image_data is None or frame_info is None:
                        print_text(f'camera{camera_id} no trigger image data available')
                        continue

                    # 检查图像尺寸是否有效
                    if frame_info.nHeight <= 0 or frame_info.nWidth <= 0:
                        print_text(f'camera{camera_id} invalid frame dimensions')
                        continue

                    # 转换图像数据
                    image_array = np.frombuffer(image_data, dtype=np.uint8)

                    if len(image_array) >= frame_info.nHeight * frame_info.nWidth:
                        # 重塑为图像
                        image = image_array[:frame_info.nHeight * frame_info.nWidth].reshape(
                            (frame_info.nHeight, frame_info.nWidth)
                        )

                        # 保存图像
                        filename = f"trigger_cam{camera_id}_{timestamp}.png"
                        filepath = os.path.join(save_dir, filename)
                        cv2.imwrite(filepath, image)

                        saved_count += 1
                        print_text(f'camera{camera_id} trigger image saved: {filename}')

                        # 如果是前两个相机，保存到立体匹配用的变量中
                        if camera_id == 0:
                            last_trigger_images["left"] = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                        elif camera_id == 1:
                            last_trigger_images["right"] = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)

                except Exception as e:
                    print_text(f'camera{camera_id} save error: {e}')
                    continue

            # 清空所有相机的触发图像缓存
            for camera_id in ready_cameras:
                obj_cam_operation[camera_id].clear_trigger_image()

            if saved_count > 0:
                print_text(f"触发图像保存完成！共保存 {saved_count} 张图像到 {save_dir}")
                print_text(f"确保每个相机只保存了触发后的第一帧图像")

                # 检查是否成功获取了双目图像
                if last_trigger_images["left"] is not None and last_trigger_images["right"] is not None:
                    print_text("双目图像捕获成功，可以进行立体匹配")
                    if trigger_stereo_match:
                        perform_trigger_stereo_match()
            else:
                print_text("未能保存任何触发图像")

        except Exception as e:
            print_text(f"触发图像捕获错误: {e}")

    def capture_and_save_trigger_images():
        """捕获触发后的图像并自动保存 - 已弃用，使用wait_and_capture_trigger_images替代"""
        print_text("警告: 使用了已弃用的capture_and_save_trigger_images函数")
        print_text("该函数无法保证每次触发只保存一帧图像")

        # 为了向后兼容，调用新的函数
        triggered_cameras = []
        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                triggered_cameras.append(i)

        if triggered_cameras:
            wait_and_capture_trigger_images(triggered_cameras)
        else:
            print_text("没有可用的相机")

    def capture_trigger_images():
        """捕获触发后的图像"""
        global last_trigger_images, obj_cam_operation

        try:
            # 获取左相机图像（相机0）
            left_image = get_camera_image(0)
            if left_image is not None:
                last_trigger_images["left"] = left_image.copy()
                print_text("Left camera image captured")

            # 获取右相机图像（相机1）
            right_image = get_camera_image(1)
            if right_image is not None:
                last_trigger_images["right"] = right_image.copy()
                print_text("Right camera image captured")

            # 检查是否成功获取了双目图像
            if last_trigger_images["left"] is not None and last_trigger_images["right"] is not None:
                print_text("Stereo image pair captured successfully")
                # 如果启用了自动立体匹配，则自动进行立体匹配
                if trigger_stereo_match:
                    perform_trigger_stereo_match()
            else:
                print_text("Failed to capture stereo image pair")

        except Exception as e:
            print_text(f"Error capturing trigger images: {e}")

    def perform_trigger_stereo_match():
        """对触发后的图像进行立体匹配"""
        global last_trigger_images, stereo_processor

        if (last_trigger_images["left"] is None or
            last_trigger_images["right"] is None):
            print_text("No captured images available for stereo matching")
            return

        try:
            if stereo_processor is None:
                if not init_stereo_vision():
                    return

            print_text("Processing stereo matching...")

            # 处理立体图像对
            result = stereo_processor.process_stereo_pair(
                last_trigger_images["left"],
                last_trigger_images["right"]
            )

            # 保存结果
            save_stereo_results(last_trigger_images["left"], last_trigger_images["right"], result)

            print_text("Stereo matching completed")

        except Exception as e:
            print_text(f"Error in stereo matching: {e}")

    def save_stereo_results(left_img, right_img, result):
        """保存立体匹配结果"""
        try:
            save_dir = "stereo_results"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 保存原始图像
            cv2.imwrite(os.path.join(save_dir, f"left_{timestamp}.png"), left_img)
            cv2.imwrite(os.path.join(save_dir, f"right_{timestamp}.png"), right_img)

            # 保存深度图和点云
            if result and 'depth' in result and result['depth'] is not None:
                depth_norm = cv2.normalize(result['depth'], None, 0, 255, cv2.NORM_MINMAX)
                depth_color = cv2.applyColorMap(depth_norm.astype(np.uint8), cv2.COLORMAP_JET)
                cv2.imwrite(os.path.join(save_dir, f"depth_{timestamp}.png"), depth_color)

                # 保存点云数据（如果有3D点云数据）
                if 'points_3d' in result and result['points_3d'] is not None:
                    print_text("Saving point cloud data...")
                    save_trigger_point_cloud(result['points_3d'], left_img, save_dir, timestamp)

            print_text(f"Stereo results saved to {save_dir}")

        except Exception as e:
            print_text(f"Error saving stereo results: {e}")

    def save_trigger_point_cloud(points_3d, color_image, save_dir, timestamp):
        """保存触发模式下的点云数据"""
        try:
            # 提取有效的3D点
            height, width = points_3d.shape[:2]
            total_pixels = height * width

            # 过滤有效点（深度值在2m-6m范围内）
            valid_mask = (points_3d[:, :, 2] >= 2000) & (points_3d[:, :, 2] <= 6000)
            valid_count = np.sum(valid_mask)

            # 计算点云覆盖率
            coverage_percentage = (valid_count / total_pixels) * 100

            if not np.any(valid_mask):
                print_text("No valid 3D points found for point cloud generation")
                return

            # 提取有效的XYZ坐标
            valid_points = points_3d[valid_mask]

            # 统计深度范围
            depth_values = valid_points[:, 2]
            min_depth = np.min(depth_values)
            max_depth = np.max(depth_values)
            mean_depth = np.mean(depth_values)

            # 提取对应的颜色信息
            if color_image is not None and color_image.shape[:2] == (height, width):
                if len(color_image.shape) == 3:
                    # BGR转RGB
                    color_rgb = cv2.cvtColor(color_image, cv2.COLOR_BGR2RGB)
                    valid_colors = color_rgb[valid_mask]
                else:
                    # 灰度图转RGB
                    gray_rgb = np.stack([color_image[valid_mask]] * 3, axis=1)
                    valid_colors = gray_rgb
            else:
                # 默认白色
                valid_colors = np.full((len(valid_points), 3), 255, dtype=np.uint8)

            # 输出详细统计信息
            print_text("=== 触发模式点云生成统计 ===")
            print_text(f"图像分辨率: {width} x {height} ({total_pixels:,} 像素)")
            print_text(f"有效点云数量: {valid_count:,} 点")
            print_text(f"点云覆盖率: {coverage_percentage:.2f}%")
            print_text(f"深度范围: {min_depth:.1f}mm - {max_depth:.1f}mm (有效范围: 2000-6000mm)")
            print_text(f"平均深度: {mean_depth:.1f}mm")
            print_text(f"基线距离: 100.88mm")

            # 深度范围检查提示
            if min_depth < 2000 or max_depth > 6000:
                print_text("警告: 检测到超出预期范围的深度值，建议检查标定参数或场景设置")

            # 保存多种格式的点云
            save_point_cloud_file(valid_points, valid_colors, save_dir, timestamp, 'ply')
            save_point_cloud_file(valid_points, valid_colors, save_dir, timestamp, 'pcd')
            save_point_cloud_file(valid_points, valid_colors, save_dir, timestamp, 'txt')

        except Exception as e:
            print_text(f"Error saving trigger point cloud: {e}")

    def save_point_cloud_file(points, colors, save_dir, timestamp, format):
        """保存点云文件的通用函数"""
        try:
            if format.lower() == 'ply':
                filename = f"pointcloud_{timestamp}.ply"
                filepath = os.path.join(save_dir, filename)

                with open(filepath, 'w') as f:
                    # PLY文件头
                    f.write("ply\n")
                    f.write("format ascii 1.0\n")
                    f.write(f"element vertex {len(points)}\n")
                    f.write("property float x\n")
                    f.write("property float y\n")
                    f.write("property float z\n")
                    f.write("property uchar red\n")
                    f.write("property uchar green\n")
                    f.write("property uchar blue\n")
                    f.write("end_header\n")

                    # 写入点云数据
                    for i in range(len(points)):
                        x, y, z = points[i]
                        r, g, b = colors[i]
                        f.write(f"{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n")

                print_text(f"Point cloud saved as PLY: {filename}")

            elif format.lower() == 'pcd':
                filename = f"pointcloud_{timestamp}.pcd"
                filepath = os.path.join(save_dir, filename)

                with open(filepath, 'w') as f:
                    # PCD文件头
                    f.write("# .PCD v0.7 - Point Cloud Data file format\n")
                    f.write("VERSION 0.7\n")
                    f.write("FIELDS x y z rgb\n")
                    f.write("SIZE 4 4 4 4\n")
                    f.write("TYPE F F F U\n")
                    f.write("COUNT 1 1 1 1\n")
                    f.write(f"WIDTH {len(points)}\n")
                    f.write("HEIGHT 1\n")
                    f.write("VIEWPOINT 0 0 0 1 0 0 0\n")
                    f.write(f"POINTS {len(points)}\n")
                    f.write("DATA ascii\n")

                    # 写入点云数据
                    for i in range(len(points)):
                        x, y, z = points[i]
                        r, g, b = colors[i]
                        # 将RGB打包为单个32位整数
                        rgb_packed = (int(r) << 16) | (int(g) << 8) | int(b)
                        f.write(f"{x:.6f} {y:.6f} {z:.6f} {rgb_packed}\n")

                print_text(f"Point cloud saved as PCD: {filename}")

            elif format.lower() == 'txt':
                filename = f"pointcloud_{timestamp}.txt"
                filepath = os.path.join(save_dir, filename)

                with open(filepath, 'w') as f:
                    # 写入文件头注释
                    f.write("# Point Cloud Data\n")
                    f.write("# Format: X Y Z R G B\n")
                    f.write(f"# Points: {len(points)}\n")

                    # 写入点云数据
                    for i in range(len(points)):
                        x, y, z = points[i]
                        r, g, b = colors[i]
                        f.write(f"{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n")

                print_text(f"Point cloud saved as TXT: {filename}")

        except Exception as e:
            print_text(f"Error saving {format} point cloud: {e}")

    def manual_stereo_match():
        """手动触发立体匹配 - 只执行一次"""
        global trigger_stereo_match

        # 优先使用触发图像，如果没有则使用当前实时图像
        left_img = None
        right_img = None

        # 检查是否有触发图像
        if (last_trigger_images["left"] is not None and
            last_trigger_images["right"] is not None):
            left_img = last_trigger_images["left"]
            right_img = last_trigger_images["right"]
            print_text("使用触发图像进行立体匹配...")
        else:
            # 获取当前实时图像
            left_img = get_camera_image(0)
            right_img = get_camera_image(1)

            if left_img is None or right_img is None:
                print_text("无法获取有效的立体图像对。请先触发相机或确保相机正在采集。")
                return

            print_text("使用当前实时图像进行立体匹配...")

        # 执行单次立体匹配
        perform_single_stereo_match(left_img, right_img)

    def perform_single_stereo_match(left_img, right_img):
        """执行单次立体匹配 - 不启动持续处理"""
        global stereo_processor

        try:
            # 确保立体视觉处理器已初始化
            if stereo_processor is None:
                if not init_stereo_vision():
                    print_text("立体视觉初始化失败")
                    return

            print_text("开始单次立体匹配处理...")

            # 直接调用立体处理，不使用队列
            result = stereo_processor.process_stereo_pair(left_img, right_img)

            if result is not None:
                # 保存结果
                save_stereo_results(left_img, right_img, result)
                print_text("单次立体匹配完成")

                # 显示结果统计
                if 'depth' in result and result['depth'] is not None:
                    depth = result['depth']
                    valid_depth_count = np.sum(depth > 0)
                    total_pixels = depth.shape[0] * depth.shape[1]
                    coverage = (valid_depth_count / total_pixels) * 100

                    depth_min = np.min(depth[depth > 0]) if valid_depth_count > 0 else 0
                    depth_max = np.max(depth[depth > 0]) if valid_depth_count > 0 else 0

                    print_text(f"深度图统计:")
                    print_text(f"  - 有效像素: {valid_depth_count:,} / {total_pixels:,} ({coverage:.1f}%)")
                    print_text(f"  - 深度范围: {depth_min:.1f}mm - {depth_max:.1f}mm")

                # 显示保存路径
                print_text("结果已保存到:")
                print_text("  - 立体结果: stereo_results/")
                print_text("  - 深度图像: depth_images/")
                print_text("  - 点云数据: point_clouds/")
            else:
                print_text("立体匹配处理失败")

        except Exception as e:
            print_text(f"单次立体匹配错误: {e}")

    def enable_auto_stereo_match():
        """启用自动立体匹配"""
        global trigger_stereo_match
        trigger_stereo_match = True
        print_text("自动立体匹配已启用")

    def disable_auto_stereo_match():
        """禁用自动立体匹配"""
        global trigger_stereo_match
        trigger_stereo_match = False
        print_text("自动立体匹配已禁用")

    # 自动保存功能
    def start_auto_save():
        """启动自动保存"""
        global auto_save_timer, auto_save_enabled, auto_save_interval

        if auto_save_enabled and b_is_grab:
            auto_save_timer = QTimer()
            auto_save_timer.timeout.connect(auto_save_images)
            auto_save_timer.start(auto_save_interval * 1000)  # 转换为毫秒
            print_text(f"自动保存已启动，间隔 {auto_save_interval} 秒")

    def stop_auto_save():
        """停止自动保存"""
        global auto_save_timer

        if auto_save_timer:
            auto_save_timer.stop()
            auto_save_timer = None
            print_text("自动保存已停止")

    def auto_save_images():
        """自动保存图像"""
        global b_is_grab, obj_cam_operation

        if not b_is_grab:
            stop_auto_save()
            return

        try:
            # 创建自动保存目录
            save_dir = "auto_saved_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            saved_count = 0

            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    # 检查图像缓冲区是否有有效的图像数据
                    if (obj_cam_operation[i].buf_save_image is None or
                        obj_cam_operation[i].st_frame_info is None):
                        continue

                    frame_info = obj_cam_operation[i].st_frame_info

                    # 检查图像尺寸是否有效
                    if frame_info.nHeight <= 0 or frame_info.nWidth <= 0:
                        continue

                    try:
                        # 获取图像数据
                        image_data = np.frombuffer(
                            obj_cam_operation[i].buf_save_image,
                            dtype=np.uint8
                        )

                        # 根据像素格式重塑图像
                        if len(image_data) >= frame_info.nHeight * frame_info.nWidth:
                            # 简单处理为灰度图像
                            image = image_data[:frame_info.nHeight * frame_info.nWidth].reshape(
                                (frame_info.nHeight, frame_info.nWidth)
                            )

                            # 保存图像
                            filename = f"auto_cam{i}_{timestamp}.png"
                            filepath = os.path.join(save_dir, filename)
                            cv2.imwrite(filepath, image)
                            saved_count += 1

                    except Exception as e:
                        print_text(f'自动保存相机{i}图像错误: {e}')
                        continue

            if saved_count > 0:
                print_text(f"自动保存完成：{saved_count} 张图像")

        except Exception as e:
            print_text(f"自动保存错误: {e}")

    def toggle_auto_save():
        """切换自动保存状态"""
        global auto_save_enabled

        auto_save_enabled = not auto_save_enabled
        if auto_save_enabled:
            print_text("自动保存已启用")
            if b_is_grab:
                start_auto_save()
        else:
            print_text("自动保存已禁用")
            stop_auto_save()

    # 设置硬件触发线
    def set_hardware_trigger_line(line_name):
        global hardware_trigger_line
        global obj_cam_operation
        global b_is_open
        global b_is_software_trigger

        if not b_is_open or b_is_software_trigger:
            hardware_trigger_line = line_name
            print_text(f'Hardware trigger line set to: {line_name}')
            return

        hardware_trigger_line = line_name
        # 如果当前是硬件触发模式，立即应用设置
        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].set_trigger_source("hardware", hardware_trigger_line)
                if 0 != ret:
                    print_text('camera' + str(i) + f' set trigger line to {line_name} fail! ret = ' + ToHexStr(ret))
                else:
                    print_text('camera' + str(i) + f' trigger line set to: {line_name}')

    # 设置硬件触发极性
    def set_hardware_trigger_activation(activation):
        global hardware_trigger_activation
        global obj_cam_operation
        global b_is_open
        global b_is_software_trigger

        if not b_is_open or b_is_software_trigger:
            hardware_trigger_activation = activation
            print_text(f'Hardware trigger activation set to: {activation}')
            return

        hardware_trigger_activation = activation
        # 如果当前是硬件触发模式，立即应用设置
        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].set_trigger_activation(hardware_trigger_activation)
                if 0 != ret:
                    print_text('camera' + str(i) + f' set trigger activation to {activation} fail! ret = ' + ToHexStr(ret))
                else:
                    print_text('camera' + str(i) + f' trigger activation set to: {activation}')

    # ch: 初始化app, 绑定控件与函数 | en: Init app, bind ui and api
    app = QApplication(sys.argv)
    mainWindow = QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(mainWindow)
    ui.pushButton_enum.clicked.connect(enum_devices)
    ui.pushButton_open.clicked.connect(open_devices)
    ui.pushButton_close.clicked.connect(close_devices)
    ui.pushButton_startGrab.clicked.connect(start_grabbing)
    ui.pushButton_stopGrab.clicked.connect(stop_grabbing)
    ui.pushButton_saveImg.clicked.connect(save_bmp)
    ui.pushButton_setParams.clicked.connect(set_parameters)
    ui.checkBox_software_trigger.clicked.connect(software_trigger_check_box_clicked)
    ui.checkBox_hardware_trigger.clicked.connect(hardware_trigger_check_box_clicked)
    ui.pushButton_triggerOnce.clicked.connect(software_trigger_once)

    # 绑定立体匹配按钮（如果UI中有的话）
    if hasattr(ui, 'pushButton_stereo_match'):
        ui.pushButton_stereo_match.clicked.connect(manual_stereo_match)

    # 绑定持续立体匹配按钮（如果UI中有的话）
    if hasattr(ui, 'pushButton_continuous_stereo'):
        ui.pushButton_continuous_stereo.clicked.connect(toggle_continuous_stereo_matching)
    cam_button_group = QButtonGroup(mainWindow)
    cam_button_group.addButton(ui.checkBox_1, 0)
    cam_button_group.addButton(ui.checkBox_2, 1)
    cam_button_group.addButton(ui.checkBox_3, 2)
    cam_button_group.addButton(ui.checkBox_4, 3) 

    cam_button_group.setExclusive(False)
    cam_button_group.buttonClicked.connect(cam_check_box_clicked)

    raio_button_group = QButtonGroup(mainWindow)
    raio_button_group.addButton(ui.radioButton_continuous, 0)
    raio_button_group.addButton(ui.radioButton_trigger, 1)
    raio_button_group.buttonClicked.connect(radio_button_clicked)

    win_display_handles.append(ui.widget_display1.winId())
    win_display_handles.append(ui.widget_display2.winId())
    win_display_handles.append(ui.widget_display3.winId())
    win_display_handles.append(ui.widget_display4.winId())

    mainWindow.show()
    enum_devices()
    enable_ui_controls()

    # 初始化立体视觉
    init_stereo_vision()

    app.exec_()

    close_devices()

    # ch:反初始化SDK | en: finalize SDK
    MvCamera.MV_CC_Finalize()

    sys.exit()
