import sys
from ctypes import *
import cv2
import numpy as np
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *
import socket
import threading
#import win32api
# 导入相机控制模块
sys.path.append("../MvImport")
from MvImport.MvCameraControl_class import *
from MvImport.CameraParams_header import *

# 导入 YOLOv11 模型
from ultralytics import YOLO

# 定义一些可能未定义的常量
MV_E_TRIGGER_TIMEOUT = 0x80040001  # 根据实际值修改

class CameraWindow(QMainWindow):
    # 定义一个信号，用于传递图像数据
    update_image_signal = pyqtSignal(np.ndarray)

    def __init__(self):
        super().__init__()
        self.setWindowTitle("千歌机器人视觉相机采集系统")
        self.setGeometry(100, 100, 800, 600)

        # 初始化相机
        self.cam = None
        self.running = False
        self.server_running = False
        self.trigger_mode = "Off"  # Off/Software/Hardware
        self.trigger_count = 0
        self.current_image = None  # 添加一个变量来存储当前显示的图像

        # 创建UI
        self.create_ui()

        # 初始化相机
        self.init_camera()

        # 初始化服务器
        self.server_thread = None

        # 加载 YOLOv11 模型
        self.model = YOLO(model=r'/opt/MVS/Samples/aarch64/yolo11_detect/weights/FAbest.pt')  # 替换为你的模型路径
        #self.model = YOLO(model=r'/opt/MVS/Samples/aarch64/yolo11_detect/data/best.pt')  # 替换为你的模型路径
        
        #self.model = YOLO(model=r'weights/yolov12.pt')  # 替换为你的模型路径

        # 连接信号到槽函数
        self.update_image_signal.connect(self.display_image)

    def create_ui(self):
        # 主布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)

        # 图像显示区域
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("background-color: black;")
        layout.addWidget(self.image_label, stretch=1)

        # 触发模式选择区域
        trigger_group = QGroupBox("触发模式")
        trigger_layout = QHBoxLayout()

        self.trigger_off = QRadioButton("连续采集")
        self.trigger_off.setChecked(True)
        self.trigger_off.toggled.connect(lambda: self.set_trigger_mode("Off"))

        self.trigger_software = QRadioButton("软件触发")
        self.trigger_software.toggled.connect(lambda: self.set_trigger_mode("Software"))

        self.trigger_hardware = QRadioButton("硬件触发")
        self.trigger_hardware.toggled.connect(lambda: self.set_trigger_mode("Hardware"))

        self.soft_trigger_btn = QPushButton("触发采集")
        self.soft_trigger_btn.clicked.connect(self.software_trigger)
        self.soft_trigger_btn.setEnabled(False)

        trigger_layout.addWidget(self.trigger_off)
        trigger_layout.addWidget(self.trigger_software)
        trigger_layout.addWidget(self.trigger_hardware)
        trigger_layout.addWidget(self.soft_trigger_btn)
        trigger_group.setLayout(trigger_layout)
        layout.addWidget(trigger_group)

        # 控制按钮区域
        control_layout = QHBoxLayout()

        self.start_btn = QPushButton("开始采集")
        self.start_btn.clicked.connect(self.start_capture)
        control_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("停止采集")
        self.stop_btn.clicked.connect(self.stop_capture)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)

        self.save_btn = QPushButton("保存图像")
        self.save_btn.clicked.connect(self.save_image)
        self.save_btn.setEnabled(False)
        control_layout.addWidget(self.save_btn)

        self.server_btn = QPushButton("启动服务器")
        self.server_btn.clicked.connect(self.toggle_server)
        control_layout.addWidget(self.server_btn)

        layout.addLayout(control_layout)

        # 状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("准备就绪")

    def init_camera(self):
        try:
            ret = MvCamera.MV_CC_Initialize()
            if ret != 0:
                raise Exception(f"初始化SDK失败! ret[0x{ret:x}]")

            deviceList = MV_CC_DEVICE_INFO_LIST()
            tlayerType = MV_GIGE_DEVICE | MV_USB_DEVICE
            ret = MvCamera.MV_CC_EnumDevices(tlayerType, deviceList)
            if ret != 0:
                raise Exception(f"枚举设备失败! ret[0x{ret:x}]")
            if deviceList.nDeviceNum < 1:
                raise Exception("未找到设备!")

            self.cam = MvCamera()
            stDeviceList = cast(deviceList.pDeviceInfo[0], POINTER(MV_CC_DEVICE_INFO)).contents
            ret = self.cam.MV_CC_CreateHandle(stDeviceList)
            if ret != 0:
                raise Exception(f"创建句柄失败! ret[0x{ret:x}]")

            ret = self.cam.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)
            if ret != 0:
                raise Exception(f"打开设备失败! ret[0x{ret:x}]")

            ret = self.cam.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_OFF)
            if ret != 0:
                print(f"警告: 设置触发模式失败! ret[0x{ret:x}]")

            self.status_bar.showMessage("相机初始化成功")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"初始化相机失败: {str(e)}")
            self.close()

    def set_trigger_mode(self, mode):
        if self.cam is None:
            return

        self.trigger_mode = mode

        try:
            if mode == "Off":
                ret = self.cam.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_OFF)
                self.soft_trigger_btn.setEnabled(False)
                self.status_bar.showMessage("触发模式: 连续采集")
            elif mode == "Software":
                ret = self.cam.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_ON)
                ret = self.cam.MV_CC_SetEnumValue("TriggerSource", MV_TRIGGER_SOURCE_SOFTWARE)
                self.soft_trigger_btn.setEnabled(True)
                self.status_bar.showMessage("触发模式: 软件触发 (点击按钮触发采集)")
            elif mode == "Hardware":
                ret = self.cam.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_ON)
                ret = self.cam.MV_CC_SetEnumValue("TriggerSource", MV_TRIGGER_SOURCE_LINE0)
                self.soft_trigger_btn.setEnabled(False)
                self.status_bar.showMessage("触发模式: 硬件触发 (等待外部信号)")

            if ret != 0:
                QMessageBox.warning(self, "警告", f"设置触发模式失败! 错误码: 0x{ret:x}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"设置触发模式时出错: {str(e)}")

    def software_trigger(self):
        if self.cam is None or self.trigger_mode != "Software":
            return

        try:
            ret = self.cam.MV_CC_SetCommandValue("TriggerSoftware")
            if ret == 0:
                self.trigger_count += 1
                self.status_bar.showMessage(f"软件触发已发送 (总计: {self.trigger_count})")
            else:
                QMessageBox.warning(self, "警告", f"软件触发失败! 错误码: 0x{ret:x}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"执行软件触发时出错: {str(e)}")

    def start_capture(self):
        if self.cam is None:
            QMessageBox.warning(self, "警告", "相机未初始化!")
            return

        ret = self.cam.MV_CC_StartGrabbing()
        if ret != 0:
            QMessageBox.critical(self, "错误", f"开始采集失败! ret[0x{ret:x}]")
            return

        self.running = True
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.save_btn.setEnabled(True)
        self.status_bar.showMessage("正在采集...")

        self.capture_thread = threading.Thread(target=self.capture_frame)
        self.capture_thread.daemon = True
        self.capture_thread.start()

    def stop_capture(self):
        self.running = False
        if hasattr(self, 'capture_thread') and self.capture_thread.is_alive():
            self.capture_thread.join()

        ret = self.cam.MV_CC_StopGrabbing()
        if ret != 0:
            QMessageBox.critical(self, "错误", f"停止采集失败! ret[0x{ret:x}]")
            return

        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_bar.showMessage("采集已停止")

    def capture_frame(self):
        stOutFrame = MV_FRAME_OUT()
        memset(byref(stOutFrame), 0, sizeof(stOutFrame))

        while self.running:
            ret = self.cam.MV_CC_GetImageBuffer(stOutFrame, 1000)
            if ret != 0:
                if ret == MV_E_TRIGGER_TIMEOUT:
                    continue
                # print(f"获取图像失败! ret[0x{ret:x}]")
                continue

            if stOutFrame.pBufAddr is None or stOutFrame.stFrameInfo.nFrameLen == 0:
                print("图像缓冲区为空或帧长度为0!")
                self.cam.MV_CC_FreeImageBuffer(stOutFrame)
                continue

            try:
                pData = cast(stOutFrame.pBufAddr, POINTER(c_ubyte * stOutFrame.stFrameInfo.nFrameLen))
                np_data = np.frombuffer(pData.contents, dtype=np.uint8)

                nWidth = stOutFrame.stFrameInfo.nWidth
                nHeight = stOutFrame.stFrameInfo.nHeight
                pixel_format = stOutFrame.stFrameInfo.enPixelType

                if pixel_format == PixelType_Gvsp_BGR8_Packed:
                    image = np_data.reshape(nHeight, nWidth, 3)
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                elif pixel_format == PixelType_Gvsp_RGB8_Packed:
                    image = np_data.reshape(nHeight, nWidth, 3)
                elif pixel_format in [PixelType_Gvsp_BayerRG8, PixelType_Gvsp_BayerGB8,
                                      PixelType_Gvsp_BayerGR8, PixelType_Gvsp_BayerBG8]:
                    image = np_data.reshape(nHeight, nWidth)
                    image = cv2.cvtColor(image, cv2.COLOR_BAYER_RG2RGB)
                else:
                    print(f"不支持的像素格式: {pixel_format}")
                    continue

                # 进行目标检测
                results = self.model.predict(image, conf=0.5)  # 设置置信度阈值
                annotated_frame = results[0].plot()  # 绘制检测结果

                # 遍历检测结果并打印详细信息
                for det in results[0]:
                    if det.boxes:
                        for box in det.boxes:
                            # 获取边界框坐标
                            x1, y1, x2, y2 = map(int, box.xyxy[0])
                            # 获取类别名称
                            label = det.names[int(box.cls[0])]
                            # 获取置信度
                            confidence = float(box.conf[0])

                            # 计算中心点坐标
                            cx = (x1 + x2) // 2
                            cy = (y1 + y2) // 2

                            # 打印边界框信息
                            print(f"检测到的目标: 类别={label}, 置信度={confidence:.2f}")
                            print(
                                f"  边界框: 左上角=({x1}, {y1}), 右上角=({x2}, {y1}), 左下角=({x1}, {y2}), 右下角=({x2}, {y2})")
                            print(f"  中心点: ({cx}, {cy})")

                            # 在图像上绘制中心点
                            cv2.circle(annotated_frame, (cx, cy), 5, (0, 0, 255), -1)

                            # 在图像上绘制中心点坐标信息
                            text = f"({cx}, {cy})"
                            cv2.putText(annotated_frame, text, (cx + 5, cy + 5), cv2.FONT_HERSHEY_SIMPLEX, 1.2,
                                        (0, 0, 255), 3)

                            # 调整左上角（LT）的文本显示位置
                            lt_text = f"LT({x1}, {y1})"
                            lt_text_x = max(x1 + 10, 10)  # 确保文本不会超出图像边界
                            lt_text_y = max(y1 + 50, 10)
                            cv2.putText(annotated_frame, lt_text, (lt_text_x, lt_text_y), cv2.FONT_HERSHEY_SIMPLEX, 1.2,
                                        (0, 255, 0), 3)

                            # 右上角（RT）
                            rt_text = f"RT({x2}, {y1})"
                            rt_text_x = min(x2 - 50, nWidth - 50)  # 确保文本不会超出图像边界
                            rt_text_y = max(y1 - 25, 10)
                            cv2.putText(annotated_frame, rt_text, (rt_text_x, rt_text_y), cv2.FONT_HERSHEY_SIMPLEX, 1.2,
                                        (0, 255, 0), 3)

                            # 左下角（LB）
                            lb_text = f"LB({x1}, {y2})"
                            lb_text_x = max(x1 + 10, 10)
                            lb_text_y = min(y2 + 40, nHeight - 20)
                            cv2.putText(annotated_frame, lb_text, (lb_text_x, lb_text_y), cv2.FONT_HERSHEY_SIMPLEX, 1.2,
                                        (0, 255, 0), 3)

                            # 右下角（RB）
                            rb_text = f"RB({x2}, {y2})"
                            rb_text_x = min(x2 - 50, nWidth - 50)
                            rb_text_y = min(y2 + 40, nHeight - 20)
                            cv2.putText(annotated_frame, rb_text, (rb_text_x, rb_text_y), cv2.FONT_HERSHEY_SIMPLEX, 1.2,
                                        (0, 255, 0), 3)

                # 通过信号将图像传递到主线程
                self.update_image_signal.emit(annotated_frame)

            except Exception as e:
                print(f"图像处理错误: {e}")
            finally:
                self.cam.MV_CC_FreeImageBuffer(stOutFrame)

    @pyqtSlot(np.ndarray)
    def display_image(self, image):
        print("图像已传递到主线程")
        h, w, ch = image.shape
        bytes_per_line = ch * w
        q_img = QImage(image.data, w, h, bytes_per_line, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(q_img)

        scaled_pixmap = pixmap.scaled(
            self.image_label.size(),
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )

        self.image_label.setPixmap(scaled_pixmap)
        self.current_image = image  # 更新当前显示的图像
        print("界面已更新")

    def save_image(self):
        if self.current_image is None:
            QMessageBox.warning(self, "警告", "没有可保存的图像!")
            return

        try:
            # 构造文件路径
            file_path = "captured_image_with_detection.png"
            c_file_path = file_path.encode('ascii')

            # 创建保存参数结构体
            stSaveParam = MV_SAVE_IMAGE_TO_FILE_PARAM_EX()
            stSaveParam.enPixelType = PixelType_Gvsp_RGB8_Packed  # 根据实际像素格式调整
            stSaveParam.nWidth = self.current_image.shape[1]
            stSaveParam.nHeight = self.current_image.shape[0]
            stSaveParam.nDataLen = self.current_image.nbytes
            stSaveParam.pData = self.current_image.ctypes.data_as(POINTER(c_ubyte))
            stSaveParam.enImageType = MV_Image_Png  # 保存为PNG格式
            stSaveParam.pcImagePath = create_string_buffer(c_file_path)
            stSaveParam.iMethodValue = 1
            stSaveParam.nQuality = 100

            # 调用SDK保存图像
            ret = self.cam.MV_CC_SaveImageToFileEx(stSaveParam)
            if ret != 0:
                QMessageBox.critical(self, "错误", f"保存图像失败! 错误码: 0x{ret:x}")
            else:
                QMessageBox.information(self, "成功", f"图像已保存为: {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存图像失败! 错误: {str(e)}")

    def toggle_server(self):
        if self.server_running:
            self.stop_server()
        else:
            self.start_server()

    def start_server(self):
        self.server_thread = threading.Thread(target=self.run_server)
        self.server_thread.daemon = True
        self.server_running = True
        self.server_thread.start()
        self.server_btn.setText("停止服务器")
        self.status_bar.showMessage("服务器已启动")

    def stop_server(self):
        self.server_running = False
        self.server_btn.setText("启动服务器")
        self.status_bar.showMessage("服务器已停止")

    def run_server(self):
        SERVER_IP = "127.0.0.1"
        SERVER_PORT = 8868

        try:
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            server_socket.bind((SERVER_IP, SERVER_PORT))
            server_socket.listen(1)

            print(f"服务器启动在 {SERVER_IP}:{SERVER_PORT}")

            while self.server_running:
                try:
                    conn, addr = server_socket.accept()
                    print(f"客户端连接: {addr}")

                    while self.server_running:
                        try:
                            data = conn.recv(1024)
                            if not data:
                                break

                            # 直接将接收到的消息发送回客户端
                            conn.send(data)

                        except Exception as e:
                            print(f"客户端通信错误: {e}")
                            break

                    conn.close()
                except Exception as e:
                    print(f"接受连接错误: {e}")
        except Exception as e:
            print(f"服务器错误: {e}")
        finally:
            server_socket.close()
            print("服务器已关闭")

    def closeEvent(self, event):
        if self.running:
            self.stop_capture()

        if self.server_running:
            self.stop_server()

        if self.cam is not None:
            self.cam.MV_CC_StopGrabbing()
            self.cam.MV_CC_CloseDevice()
            self.cam.MV_CC_DestroyHandle()
            MvCamera.MV_CC_Finalize()

        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = CameraWindow()
    window.show()
    sys.exit(app.exec_())