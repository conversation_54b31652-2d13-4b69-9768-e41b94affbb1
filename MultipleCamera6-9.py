class StereoVisionProcessor(QObject):
    # 定义信号用于更新UI
    depth_result_signal = pyqtSignal(np.ndarray, np.ndarray, np.ndarray)  # disparity, depth, depth_color
    tcp_status_signal = pyqtSignal(str)  # 用于更新 TCP/IP 通讯状态

    def __init__(self):
        super().__init__()
        self.calibration_params = None
        self.rectification_maps = None
        self.Q_matrix = None
        self.calibration_available = False

        self.yolo_model = YOLO("/opt/MVS/Samples/aarch64/yolo11_detect/data/best.pt")  # 或 "yolov11.pt"
        self.target_classes = [1]  # process_stereo_pair

        # 立体匹配器
        self.stereo_matcher = self.create_stereo_matcher()

        # 图像队列用于同步
        self.left_image_queue = Queue(maxsize=2)
        self.right_image_queue = Queue(maxsize=2)

        self.robot_ip = "*************"  # 替换为机器人的IP地址
        self.robot_port = 12345  # 替换为机器人的端口号

        # 处理标志
        self.processing = False
        self.process_thread = None

        # 存储最新的深度图
        self.latest_depth = None
        self.latest_disparity = None
        self.depth_lock = threading.Lock()

        # 图像保存控制参数
        self.save_input_images = True      # 是否保存原始输入图像
        self.save_rectified_images = True  # 是否保存校正后图像
        self.save_comparison_images = True # 是否保存对比图像
        self.image_save_interval = 5       # 图像保存间隔（秒）
        self.last_save_time = 0           # 上次保存时间

        # 尝试初始化标定参数
        try:
            self.init_calibration_params()
        except Exception as e:
            print(f"Warning: Calibration initialization failed: {e}")
            print("Using simplified stereo processing without calibration")
            self.calibration_available = False

    def create_stereo_matcher(self):
        """创建立体匹配器 - 基于ceshiliti代码的方法"""
        # 使用与ceshiliti代码相同的参数设置
        stereo = cv2.StereoSGBM_create(
            minDisparity=0,
            numDisparities=96,  # 增加视差范围
            blockSize=7,  # 增加块大小
            P1=8 * 3 * 7 ** 2,
            P2=32 * 3 * 7 ** 2,
            disp12MaxDiff=1,
            uniquenessRatio=10,
            speckleWindowSize=100,
            speckleRange=32,
            preFilterCap=63,
            mode=cv2.STEREO_SGBM_MODE_SGBM_3WAY
        )
        return stereo

    def init_calibration_params(self):
        """初始化相机标定参数"""
        try:
            print("Initializing stereo calibration parameters...")

            # 尝试从JSON文件加载真实标定参数
            try:
                import json
                with open('stereo_calibration.json', 'r') as f:
                    calib_data = json.load(f)

                print("Loading calibration parameters from stereo_calibration.json")

                # 加载真实的标定参数
                self.camera_matrix_left = np.array(calib_data['camera_matrix_left'], dtype=np.float64)
                self.camera_matrix_right = np.array(calib_data['camera_matrix_right'], dtype=np.float64)

                # 畸变系数
                self.dist_coeffs_left = np.array(calib_data['dist_coeffs_left'], dtype=np.float64)
                self.dist_coeffs_right = np.array(calib_data['dist_coeffs_right'], dtype=np.float64)

                # 立体标定参数
                self.R = np.array(calib_data['R'], dtype=np.float64)
                self.T = np.array(calib_data['T'], dtype=np.float64)

                # 图像尺寸
                self.image_size = tuple(calib_data['image_size'])  # (2448, 2048)
                self.baseline_mm = calib_data['baseline_mm']

                print(f"Loaded calibration: Image size {self.image_size}, Baseline {self.baseline_mm:.2f}mm")

            except (FileNotFoundError, KeyError, json.JSONDecodeError) as e:
                print(f"Failed to load calibration file: {e}")
                print("Using provided calibration parameters...")

                # 使用您提供的真实标定参数
                self.camera_matrix_left = np.array([
                    [2349.599303017811, 0.0, 1221.0886985822297],
                    [0.0, 2347.04087849075, 1021.2297950652342],
                    [0.0, 0.0, 1.0]
                ], dtype=np.float64)

                self.camera_matrix_right = np.array([
                    [2347.7080632127045, 0.0, 1219.3168735048296],
                    [0.0, 2347.528871737054, 1010.4282529230558],
                    [0.0, 0.0, 1.0]
                ], dtype=np.float64)

                # 畸变系数 - 转换为正确的形状
                self.dist_coeffs_left = np.array([
                    [-0.0677072434895743, 0.16840134514589222, -0.00013311325437381048,
                     -0.0010946605867930416, -0.19743756744235746]
                ], dtype=np.float64).reshape(5, 1)

                self.dist_coeffs_right = np.array([
                    [-0.07691265481784593, 0.22703604995053306, 0.00015959041360151294,
                     -0.0011580170802655745, -0.3538743014783903]
                ], dtype=np.float64).reshape(5, 1)

                # 立体标定参数
                self.R = np.array([
                    [0.9998155478060632, -0.0008436404567199599, -0.01918746038540903],
                    [0.0008172502236645439, 0.9999987094440285, -0.0013831891951225166],
                    [0.019188602537281753, 0.0013672531065493177, 0.9998149469534894]
                ], dtype=np.float64)

                self.T = np.array([
                    [-100.87040766250446],
                    [0.06079718879422688],
                    [-1.3284405860235702]
                ], dtype=np.float64)

                # 图像尺寸和基线距离
                self.image_size = (2448, 2048)  # 使用您提供的真实图像尺寸
                self.baseline_mm = 100.87917323555243  # 使用您提供的真实基线距离

                print(f"Using provided calibration: Image size {self.image_size}, Baseline {self.baseline_mm:.2f}mm")

            # 尝试计算校正映射，如果失败则跳过
            try:
                self.compute_rectification_maps()
                self.calibration_available = True
                print("Calibration parameters initialized successfully")
            except Exception as calib_error:
                print(f"Calibration mapping failed: {calib_error}")
                print("Will use simplified stereo processing")
                self.calibration_available = False
                # 设置基于真实标定参数的简化Q矩阵
                # 使用真实的主点坐标和焦距
                cx = 1220.0  # 主点x坐标的平均值
                cy = 1015.0  # 主点y坐标的平均值
                fx = 2348.0  # 焦距的平均值
                baseline = 100.88  # 真实基线距离(mm)

                self.Q_matrix = np.array([
                    [1.0, 0.0, 0.0, -cx],
                    [0.0, 1.0, 0.0, -cy],
                    [0.0, 0.0, 0.0, fx],
                    [0.0, 0.0, -1.0/baseline, 0.0]
                ], dtype=np.float64)

        except Exception as e:
            print(f"Error initializing calibration parameters: {e}")
            # 设置默认值避免崩溃
            self.camera_matrix_left = None
            self.camera_matrix_right = None
            self.dist_coeffs_left = None
            self.dist_coeffs_right = None
            self.R = None
            self.T = None
            self.Q_matrix = None
            self.calibration_available = False

    def compute_rectification_maps(self):
        """计算校正映射"""
        try:
            # 检查参数是否有效
            if (self.camera_matrix_left is None or self.camera_matrix_right is None or
                self.dist_coeffs_left is None or self.dist_coeffs_right is None or
                self.R is None or self.T is None):
                print("Calibration parameters not properly initialized")
                return

            # 使用真实的图像尺寸
            image_size = getattr(self, 'image_size', (640, 480))

            # 确保所有参数都是正确的数据类型
            R1, R2, P1, P2, self.Q_matrix, _, _ = cv2.stereoRectify(
                self.camera_matrix_left.astype(np.float64),
                self.dist_coeffs_left.astype(np.float64),
                self.camera_matrix_right.astype(np.float64),
                self.dist_coeffs_right.astype(np.float64),
                image_size,
                self.R.astype(np.float64),
                self.T.astype(np.float64)
            )

            self.map1_left, self.map2_left = cv2.initUndistortRectifyMap(
                self.camera_matrix_left.astype(np.float64),
                self.dist_coeffs_left.astype(np.float64),
                R1, P1, image_size, cv2.CV_16SC2
            )

            self.map1_right, self.map2_right = cv2.initUndistortRectifyMap(
                self.camera_matrix_right.astype(np.float64),
                self.dist_coeffs_right.astype(np.float64),
                R2, P2, image_size, cv2.CV_16SC2
            )

        except Exception as e:
            print(f"Error computing rectification maps: {e}")
            # 设置为None避免后续错误
            self.map1_left = None
            self.map2_left = None
            self.map1_right = None
            self.map2_right = None
            self.Q_matrix = None

    def rectify_images(self, left_img, right_img):
        """校正图像"""
        try:
            # 如果没有标定参数，直接返回原图像
            if not self.calibration_available:
                return left_img, right_img

            # 检查映射是否有效
            if (self.map1_left is None or self.map2_left is None or
                self.map1_right is None or self.map2_right is None):
                print("Rectification maps not available, returning original images")
                return left_img, right_img

            left_rectified = cv2.remap(left_img, self.map1_left, self.map2_left, cv2.INTER_LINEAR)
            right_rectified = cv2.remap(right_img, self.map1_right, self.map2_right, cv2.INTER_LINEAR)
            return left_rectified, right_rectified
        except Exception as e:
            print(f"Error rectifying images: {e}")
            return left_img, right_img

    def compute_disparity(self, left_img, right_img):
        """计算视差图 - 基于ceshiliti代码的方法"""
        # 转换为灰度图
        if len(left_img.shape) == 3:
            left_gray = cv2.cvtColor(left_img, cv2.COLOR_BGR2GRAY)
            right_gray = cv2.cvtColor(right_img, cv2.COLOR_BGR2GRAY)
        else:
            left_gray = left_img
            right_gray = right_img

        # 校正图像
        left_rect, right_rect = self.rectify_images(left_gray, right_gray)

        # 计算视差
        disparity = self.stereo_matcher.compute(left_rect, right_rect)

        # 转换为浮点数并归一化
        disparity = disparity.astype(np.float32) / 16.0

        return disparity, left_rect, right_rect

    def disparity_to_depth(self, disparity):
        """将视差转换为深度 - 基于ceshiliti代码的方法"""
        if self.Q_matrix is None:
            return None

        # 使用Q矩阵重投影到3D
        points_3d = cv2.reprojectImageTo3D(disparity, self.Q_matrix)

        # 提取深度信息（Z坐标）
        depth = points_3d[:, :, 2]

        # 过滤无效深度值
        depth[depth <= 0] = 0
        depth[depth > 5000] = 0  # 限制最大深度为5米

        return depth

    def create_depth_colormap(self, depth):
        """创建深度图的彩色可视化 - 基于ceshiliti代码的方法"""
        # 归一化深度值到0-255
        depth_norm = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX)
        depth_norm = depth_norm.astype(np.uint8)

        # 应用彩色映射
        depth_color = cv2.applyColorMap(depth_norm, cv2.COLORMAP_JET)

        # 将无效区域设为黑色
        mask = depth <= 0
        depth_color[mask] = [0, 0, 0]

        return depth_color

    def compute_depth(self, disparity):
        """从视差计算深度 - 根据真实标定参数优化"""
        try:
            if self.Q_matrix is None:
                print("Q matrix not available for depth computation")
                return None, None

            # 重投影到3D
            points_3d = cv2.reprojectImageTo3D(disparity, self.Q_matrix)
            depth = points_3d[:, :, 2]

            # 根据真实标定参数过滤深度值
            # 基线: 100.88mm, 焦距: ~2348像素
            # 理论最小深度 = (基线 * 焦距) / 最大视差
            # 理论最大深度 = (基线 * 焦距) / 最小视差
            depth[depth <= 0] = 0
            depth[depth < 2000] = 0    # 最小深度1.5米 (考虑实际应用场景)
            depth[depth > 5000] = 0    # 最大深度8米 (根据视差精度限制)

            # 过滤异常值 (深度变化过大的点)
            depth_median = np.median(depth[depth > 0])
            if depth_median > 0:
                depth_std = np.std(depth[depth > 0])
                depth[np.abs(depth - depth_median) > 3 * depth_std] = 0

            return depth, points_3d
        except Exception as e:
            print(f"Error computing depth: {e}")
            return None, None

    def add_image_pair(self, left_img, right_img):
        """添加图像对到处理队列"""
        try:
            if not self.left_image_queue.full():
                self.left_image_queue.put(left_img, block=False)
            if not self.right_image_queue.full():
                self.right_image_queue.put(right_img, block=False)
        except:
            pass

    def start_processing(self):
        """启动处理线程"""
        if not self.processing:
            self.processing = True
            self.process_thread = threading.Thread(target=self.processing_loop)
            self.process_thread.daemon = True
            self.process_thread.start()

    def stop_processing(self):
        """停止处理"""
        self.processing = False
        if self.process_thread:
            self.process_thread.join()

    def processing_loop(self):
        """处理循环"""
        while self.processing:
            try:
                if not self.left_image_queue.empty() and not self.right_image_queue.empty():
                    left_img = self.left_image_queue.get()
                    right_img = self.right_image_queue.get()
                    self.process_stereo_pair(left_img, right_img)
                else:
                    time.sleep(0.01)
            except Exception as e:
                print(f"Stereo processing error: {e}")
                time.sleep(0.1)

    def save_object_point_cloud(self, points_3d, bbox, color_image, obj_id, save_dir="object_point_clouds"):
        print(f"[DEBUG] save_object_point_cloud 被调用，Object {obj_id}")
        """
        保存单个物体的点云数据（统一走 _save_ply 的完整滤波流程）
        Args:
            points_3d: (H, W, 3) 3D点云
            bbox: (x1, y1, x2, y2) 物体边界框
            color_image: 彩色图像
            obj_id: 物体编号
            save_dir: 保存目录
        """
        try:
            os.makedirs(save_dir, exist_ok=True)
            x1, y1, x2, y2 = bbox
            h, w = points_3d.shape[:2]

            # 提取 ROI
            x1, x2 = max(0, x1), min(w, x2)
            y1, y2 = max(0, y1), min(h, y2)
            roi_points = points_3d[y1:y2, x1:x2]
            roi_colors = color_image[y1:y2, x1:x2]

            # 过滤无效深度
            valid_mask = roi_points[:, :, 2] > 0
            valid_pts = roi_points[valid_mask]
            valid_clr = roi_colors[valid_mask]

            if len(valid_pts) == 0:
                print(f"Object {obj_id}: 无有效点云，跳过")
                return

            # 转换为 RGB 并 reshape
            valid_clr = cv2.cvtColor(valid_clr.reshape(-1, 1, 3), cv2.COLOR_BGR2RGB).reshape(-1, 3)

            # 统一调用 _save_ply 进行完整滤波流程
            timestamp = int(time.time() * 1000)
            self._save_ply(valid_pts, valid_clr, save_dir, timestamp)

        except Exception as e:
            print(f"[ERROR] Object {obj_id} 保存点云失败: {e}")

    def crop_image_to_roi(self, image, roi_x1, roi_y1, roi_x2, roi_y2):
        """裁剪图像到 ROI 区域"""
        return image[roi_y1:roi_y2, roi_x1:roi_x2]

    def adjust_boxes_to_original(self, boxes, roi_x1, roi_y1):
        """将检测框坐标从裁剪后的图像映射回原始图像"""
        adjusted_boxes = []
        for box in boxes:
            x1, y1, x2, y2 = box
            adjusted_boxes.append([x1 + roi_x1, y1 + roi_y1, x2 + roi_x1, y2 + roi_y1])
        return np.array(adjusted_boxes)

    def process_stereo_pair(self, left_img, right_img):
        try:
            # 1. 调试图像
            cv2.imwrite("debug_left_before_yolo.png", left_img)

            # 2. 定义 ROI
            roi_x1, roi_y1, roi_x2, roi_y2 = 1300, 400, 2400, 2000
            cropped_left = self.crop_image_to_roi(left_img, roi_x1, roi_y1, roi_x2, roi_y2)

            # 3. YOLO 检测
            results = self.yolo_model(cropped_left, classes=self.target_classes)
            if not results or len(results[0].boxes) == 0:
                print("未检测到目标")
                return
            boxes = results[0].boxes.xyxy.cpu().numpy().astype(int)
            adjusted_boxes = self.adjust_boxes_to_original(boxes, roi_x1, roi_y1)

            # 4. 立体匹配
            disparity, left_rect, right_rect = self.compute_disparity(left_img, right_img)
            depth, points_3d = self.compute_depth(disparity)
            if depth is None:
                print("Failed to compute depth")
                return

            timestamp = int(time.time() * 1000)

            # 5. 对每个柜子
            for idx, (x1, y1, x2, y2) in enumerate(adjusted_boxes):
                cv2.rectangle(left_img, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.putText(left_img, f"Obj{idx}", (x1, y1 - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

                surface_pts, _ = self.extract_cabinet_surface(
                    points_3d, left_img, (x1, y1, x2, y2), depth)
                if len(surface_pts) < 4:
                    print(f"Object {idx}: 表面点不足，跳过")
                    continue

                refined_corners = self.find_corners_on_surface(
                    surface_pts, (x1, y1, x2, y2), timestamp)
                if refined_corners is None:
                    continue

                self.save_corners(refined_corners, "corner_pointclouds", timestamp)
                self.save_plane_fit_result(surface_pts, (x1, y1, x2, y2), timestamp)

            # 6. 画 ROI
            cv2.rectangle(left_img, (roi_x1, roi_y1), (roi_x2, roi_y2), (0, 0, 255), 2)
            cv2.putText(left_img, "ROI", (roi_x1, roi_y1 - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

            # 7. 发射信号 & 保存结果
            self.depth_result_signal.emit(disparity, depth, left_img)
            save_dir_det = "yolo_detected"
            os.makedirs(save_dir_det, exist_ok=True)
            cv2.imwrite(os.path.join(save_dir_det, f"yolo_detected_{timestamp}.png"), left_img)

            with self.depth_lock:
                self.latest_depth = depth
                self.latest_points_3d = points_3d

        except Exception as e:
            print(f"立体处理错误: {e}")

    def set_image_save_settings(self, save_input=True, save_rectified=True, save_comparison=True, interval=5):
        """设置图像保存参数
        Args:
            save_input: 是否保存原始输入图像
            save_rectified: 是否保存校正后图像
            save_comparison: 是否保存对比图像
            interval: 保存间隔（秒）
        """
        self.save_input_images = save_input
        self.save_rectified_images = save_rectified
        self.save_comparison_images = save_comparison
        self.image_save_interval = interval
        print(f"图像保存设置已更新: 输入图像={save_input}, 校正图像={save_rectified}, 对比图像={save_comparison}, 间隔={interval}秒")

    def force_save_current_pair(self, left_img, right_img):
        """强制保存当前图像对（忽略时间间隔限制）"""
        try:
            print("强制保存当前立体图像对...")

            # 保存原始图像
            if self.save_input_images:
                self.save_stereo_input_images(left_img, right_img)

            # 校正并保存校正图像
            left_rectified, right_rectified = self.rectify_images(left_img, right_img)
            if self.save_rectified_images:
                self.save_rectified_images(left_rectified, right_rectified)

            print("强制保存完成")

        except Exception as e:
            print(f"强制保存图像对错误: {e}")

    def save_depth_image(self, depth_color):
        """保存深度图像"""
        try:
            save_dir = "depth_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            filename = f"depth_{timestamp}.png"
            filepath = os.path.join(save_dir, filename)
            cv2.imwrite(filepath, depth_color)
        except Exception as e:
            print(f"Error saving depth image: {e}")

    def save_point_cloud(self, points_3d, color_image=None, format='ply'):
        """保存点云数据
        Args:
            points_3d: 3D点云数据 (H, W, 3)
            color_image: 彩色图像用于纹理 (H, W, 3)
            format: 保存格式 (仅支持 'ply')
        """
        try:
            save_dir = "point_clouds"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 提取有效的3D点
            height, width = points_3d.shape[:2]
            total_pixels = height * width

            # 过滤有效点（深度值在1.5m-8m范围内，根据真实标定参数调整）
            valid_mask = (points_3d[:, :, 2] >= 1500) & (points_3d[:, :, 2] <= 8000)
            valid_count = np.sum(valid_mask)

            # 计算点云覆盖率
            coverage_percentage = (valid_count / total_pixels) * 100

            if not np.any(valid_mask):
                print("No valid 3D points found for point cloud generation")
                return

            # 提取有效的XYZ坐标
            valid_points = points_3d[valid_mask]

            # 统计深度范围
            depth_values = valid_points[:, 2]
            min_depth = np.min(depth_values)
            max_depth = np.max(depth_values)
            mean_depth = np.mean(depth_values)

            # 提取对应的颜色信息
            if color_image is not None and color_image.shape[:2] == (height, width):
                if len(color_image.shape) == 3:
                    # BGR转RGB
                    color_rgb = cv2.cvtColor(color_image, cv2.COLOR_BGR2RGB)
                    valid_colors = color_rgb[valid_mask]
                else:
                    # 灰度图转RGB
                    gray_rgb = np.stack([color_image[valid_mask]] * 3, axis=1)
                    valid_colors = gray_rgb
            else:
                # 默认白色
                valid_colors = np.full((len(valid_points), 3), 255, dtype=np.uint8)

            # 输出详细统计信息
            print(f"=== 点云生成统计 ===")
            print(f"图像分辨率: {width} x {height} ({total_pixels:,} 像素)")
            print(f"有效点云数量: {valid_count:,} 点")
            print(f"点云覆盖率: {coverage_percentage:.2f}%")
            print(f"深度范围: {min_depth:.1f}mm - {max_depth:.1f}mm (有效范围: 1500-8000mm)")
            print(f"平均深度: {mean_depth:.1f}mm")
            print(f"基线距离: {getattr(self, 'baseline_mm', 'Unknown')}mm")
            print(f"相机内参 - 左相机焦距: fx={getattr(self, 'camera_matrix_left', [[0]])[0][0]:.1f}px")
            print(f"相机内参 - 右相机焦距: fx={getattr(self, 'camera_matrix_right', [[0]])[0][0]:.1f}px")

            # 深度范围检查提示
            if min_depth < 2000 or max_depth > 5000:
                print(f"警告: 检测到超出预期范围的深度值，建议检查标定参数或场景设置")

            # 保存为PLY格式
            self._save_ply(valid_points, valid_colors, save_dir, timestamp)

        except Exception as e:
            print(f"Error saving point cloud: {e}")

    def save_stereo_input_images(self, left_img, right_img):
        """保存立体匹配前的原始图像对"""
        try:
            save_dir = "stereo_input_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 保存左图像
            left_filename = f"stereo_left_input_{timestamp}.png"
            left_filepath = os.path.join(save_dir, left_filename)
            cv2.imwrite(left_filepath, left_img)

            # 保存右图像
            right_filename = f"stereo_right_input_{timestamp}.png"
            right_filepath = os.path.join(save_dir, right_filename)
            cv2.imwrite(right_filepath, right_img)

            print(f"立体输入图像已保存: {left_filename}, {right_filename}")

            # 创建并保存拼接图像用于对比显示
            self.save_stereo_comparison_image(left_img, right_img, save_dir, timestamp, "input")

        except Exception as e:
            print(f"保存立体输入图像错误: {e}")

    def save_rectified_images(self, left_rectified, right_rectified):
        """保存校正后的立体图像对"""
        try:
            save_dir = "stereo_rectified_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 保存左校正图像
            left_filename = f"stereo_left_rectified_{timestamp}.png"
            left_filepath = os.path.join(save_dir, left_filename)
            cv2.imwrite(left_filepath, left_rectified)

            # 保存右校正图像
            right_filename = f"stereo_right_rectified_{timestamp}.png"
            right_filepath = os.path.join(save_dir, right_filename)
            cv2.imwrite(right_filepath, right_rectified)

            print(f"立体校正图像已保存: {left_filename}, {right_filename}")

            # 创建并保存拼接图像用于对比显示
            self.save_stereo_comparison_image(left_rectified, right_rectified, save_dir, timestamp, "rectified")

        except Exception as e:
            print(f"保存立体校正图像错误: {e}")

    def save_stereo_comparison_image(self, left_img, right_img, save_dir, timestamp, image_type):
        """创建并保存立体图像对比图（左右拼接+水平线）"""
        try:
            # 确保两个图像尺寸一致
            if left_img.shape != right_img.shape:
                print(f"警告: 左右图像尺寸不一致 - 左: {left_img.shape}, 右: {right_img.shape}")
                # 调整到相同尺寸
                min_height = min(left_img.shape[0], right_img.shape[0])
                min_width = min(left_img.shape[1], right_img.shape[1])
                left_img = left_img[:min_height, :min_width]
                right_img = right_img[:min_height, :min_width]

            # 水平拼接左右图像
            stereo_pair = np.hstack((left_img, right_img))

            # 添加水平参考线（用于检查校正效果）
            height = stereo_pair.shape[0]
            width = stereo_pair.shape[1]

            # 在图像上绘制水平参考线
            line_color = (0, 255, 0) if len(stereo_pair.shape) == 3 else 255  # 绿色或白色
            line_thickness = 2

            # 绘制多条水平线
            for y in range(height // 10, height, height // 10):
                cv2.line(stereo_pair, (0, y), (width, y), line_color, line_thickness)

            # 在中间添加分割线
            middle_x = width // 2
            cv2.line(stereo_pair, (middle_x, 0), (middle_x, height), (0, 0, 255), 3)  # 红色分割线

            # 添加文字标注
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 2.0
            font_thickness = 3
            text_color = (255, 255, 255) if len(stereo_pair.shape) == 3 else 255

            # 左图标注
            cv2.putText(stereo_pair, "LEFT", (50, 80), font, font_scale, text_color, font_thickness)
            # 右图标注
            cv2.putText(stereo_pair, "RIGHT", (middle_x + 50, 80), font, font_scale, text_color, font_thickness)

            # 保存对比图像
            comparison_filename = f"stereo_comparison_{image_type}_{timestamp}.png"
            comparison_filepath = os.path.join(save_dir, comparison_filename)
            cv2.imwrite(comparison_filepath, stereo_pair)

            print(f"立体对比图像已保存: {comparison_filename}")

        except Exception as e:
            print(f"保存立体对比图像错误: {e}")

    def _save_ply(self, points, colors, save_dir, timestamp):
        """强制使用滤波保存点云"""
        try:
            if len(points) == 0:
                print("[WARNING] 无有效点，跳过保存")
                return

            # 构造 Open3D 点云
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points)
            pcd.colors = o3d.utility.Vector3dVector(colors / 255.0)

            # 1. 统计滤波去噪
            cl, ind = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
            pcd = pcd.select_by_index(ind)

            # 2. 结构保留（可选，默认开启）
            keep_frame = True
            if keep_frame:
                pts = np.asarray(pcd.points)
                kdtree = KDTree(pts)
                distances, _ = kdtree.query(pts, k=10)
                avg_distances = np.mean(distances, axis=1)
                threshold = np.percentile(avg_distances, 30)
                frame_mask = avg_distances < threshold
                frame_pcd = pcd.select_by_index(np.where(frame_mask)[0])

                # 3. DBSCAN 聚类保留最大簇
                labels = np.array(frame_pcd.cluster_dbscan(eps=20, min_points=10, print_progress=False))
                if len(labels) > 0 and labels.max() >= 0:
                    largest_cluster_idx = np.where(labels == np.argmax(np.bincount(labels[labels >= 0])))[0]
                    filtered_pcd = frame_pcd.select_by_index(largest_cluster_idx)
                else:
                    filtered_pcd = frame_pcd
            else:
                filtered_pcd = pcd

            # 4. 体素降采样
            filtered_pcd = filtered_pcd.voxel_down_sample(voxel_size=5)

            # 5. 保存最终滤波结果
            filename = f"filtered_pointcloud_{timestamp}.ply"
            filepath = os.path.join(save_dir, filename)
            o3d.io.write_point_cloud(filepath, filtered_pcd)
            print(f"[✅ 滤波点云已保存] {filepath} | 点数: {len(filtered_pcd.points)}")

            return filtered_pcd  # ✅ 加这一行

        except Exception as e:
            print(f"[ERROR] 点云滤波保存失败: {e}")

    def save_corner_pointcloud(self, points, colors, save_dir, timestamp):
        """保存角点点云（不同颜色）"""
        try:
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points)
            pcd.colors = o3d.utility.Vector3dVector(colors / 255.0)

            filename = f"corners_{timestamp}.ply"
            filepath = os.path.join(save_dir, filename)
            o3d.io.write_point_cloud(filepath, pcd)
            print(f"[�� 角点点云已保存] {filepath} | 点数: {len(pcd.points)}")

            # ✅ 可选：自动打开点云查看器
            o3d.visualization.draw_geometries([pcd], window_name="YOLO Corners in Filtered Cloud")
        except Exception as e:
            print(f"[ERROR] 保存角点点云失败: {e}")


    # ---------- 新增函数 1：提取柜子表面点云 ----------
    def extract_cabinet_surface(self, points_3d, color_img, bbox, depth_img):
        x1, y1, x2, y2 = bbox
        roi_pts = points_3d[y1:y2, x1:x2]
        roi_clr = color_img[y1:y2, x1:x2]
        roi_d = depth_img[y1:y2, x1:x2]

        mask = roi_d > 0
        pts = roi_pts[mask]
        clr = roi_clr[mask]
        d = roi_d[mask]

        # 深度升序，取最靠前 80 %（可调）
        idx = np.argsort(d)[:int(len(d) * 0.8)]
        return pts[idx], clr[idx]


    def find_corners_on_surface(self, surface_pts, bbox, timestamp):
        """
        在拟合平面上生成 4 角矩形，并计算宽、高、中心点
        结果写入 plane_fit/plane_params_<timestamp>.txt
        """

        save_dir = "plane_fit"
        os.makedirs(save_dir, exist_ok=True)
        txt_path = os.path.join(save_dir, f"plane_params_{timestamp}.txt")

        # ---------- 1. 平面拟合 ----------
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(surface_pts)
        plane_model, inliers = pcd.segment_plane(
            distance_threshold=5, ransac_n=3, num_iterations=1000)
        a, b, c, d = plane_model
        centroid = np.mean(surface_pts[inliers], axis=0)

        # ---------- 2. 建立平面坐标系 ----------
        normal = np.array([a, b, c], dtype=np.float64)
        normal = normal / np.linalg.norm(normal)
        u = np.cross(normal, [1, 0, 0])
        if np.linalg.norm(u) < 1e-6:
            u = np.cross(normal, [0, 1, 0])
        u = u / np.linalg.norm(u)
        v = np.cross(normal, u)

        # ---------- 3. 投影并求矩形 ----------
        plane_pts = surface_pts[inliers]
        local_2d = (plane_pts - centroid) @ np.stack([u, v]).T
        min_x, max_x = local_2d[:, 0].min(), local_2d[:, 0].max()
        min_y, max_y = local_2d[:, 1].min(), local_2d[:, 1].max()

        # 4 角点
        rect_2d = np.array([[min_x, min_y],
                            [max_x, min_y],
                            [max_x, max_y],
                            [min_x, max_y]])
        rect_3d = centroid + rect_2d @ np.stack([u, v])

        if len(rect_3d) != 4 or rect_3d.shape[1] != 3:
            print(f"Error: rect_3d shape is incorrect. Expected (4, 3), got {rect_3d.shape}")
            return None

        # 打印每个角点的坐标
        print("Corner points in 3D space:")
        for i, pt in enumerate(rect_3d):
            print(f"  C{i}: ({pt[0]:.2f}, {pt[1]:.2f}, {pt[2]:.2f})")

        # ---------- 4. 计算宽、高、中心 ----------
        # 使用自定义的宽、高计算方式
        p0, p1, p2, p3 = rect_3d  # 4 个角点顺序：左下、右下、右上、左上（或根据你实际顺序调整）

        # 打印每个角点的坐标
        print("Corner points in 3D space:")
        print(f"  p0: ({p0[0]:.2f}, {p0[1]:.2f}, {p0[2]:.2f})")
        print(f"  p1: ({p1[0]:.2f}, {p1[1]:.2f}, {p1[2]:.2f})")
        print(f"  p2: ({p2[0]:.2f}, {p2[1]:.2f}, {p2[2]:.2f})")
        print(f"  p3: ({p3[0]:.2f}, {p3[1]:.2f}, {p3[2]:.2f})")

        # 宽 = [(p1.x - p0.x) + (p3.x - p2.x)] / 2
        width_mm = ((p0[0] - p3[0]) + (p1[0] - p2[0])) / 2.0

        # 高 = [(p2.y - p0.y) + (p3.y - p1.y)] / 2
        height_mm = ((p1[1] - p0[1]) + (p2[1] - p3[1])) / 2.0
        center_pt = centroid + np.array([(min_x + max_x) / 2,
                                         (min_y + max_y) / 2]) @ np.stack([u, v])

        # ---------- 5. 写入文件 ----------
        with open(txt_path, "a") as f:  # 追加写入
            f.write("=== Rectangle Info ===\n")
            f.write(f"bbox: {bbox}\n")
            f.write(f"plane_model: {list(plane_model)}\n")
            f.write(f"inlier_cnt: {len(plane_pts)}\n")
            f.write(f"width_mm:  {width_mm:.2f}\n")
            f.write(f"height_mm: {height_mm:.2f}\n")
            f.write(f"center_mm: ({center_pt[0]:.2f}, {center_pt[1]:.2f}, {center_pt[2]:.2f})\n")
            f.write("corners_mm:\n")
            for i, pt in enumerate(rect_3d):
                f.write(f"  C{i}: ({pt[0]:.2f}, {pt[1]:.2f}, {pt[2]:.2f})\n")
            f.write("\n")

        # ---------- 6. 控制台打印 ----------
        print(f"[Rect] W={width_mm:.2f} mm, H={height_mm:.2f} mm")
        print(f"[Rect] Center=({center_pt[0]:.2f}, {center_pt[1]:.2f}, {center_pt[2]:.2f}) mm")

        return rect_3d


    def save_plane_fit_result(self, surface_pts, bbox, timestamp):
        """
        保存平面拟合的中间结果
        :param surface_pts: 柜子表面点云 (N,3)
        :param bbox:        物体 2D 检测框 (x1,y1,x2,y2)
        :param timestamp:   时间戳
        """
        try:
            save_dir = "plane_fit"
            os.makedirs(save_dir, exist_ok=True)

            # 1. 保存原始表面点云
            raw_pcd = o3d.geometry.PointCloud()
            raw_pcd.points = o3d.utility.Vector3dVector(surface_pts)
            o3d.io.write_point_cloud(
                os.path.join(save_dir, f"surface_raw_{timestamp}.ply"), raw_pcd
            )

            # 2. 平面拟合
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(surface_pts)
            plane_model, inliers = pcd.segment_plane(
                distance_threshold=5, ransac_n=3, num_iterations=1000
            )
            plane_pts = np.asarray(pcd.select_by_index(inliers).points)

            # 3. 保存平面点云
            plane_pcd = o3d.geometry.PointCloud()
            plane_pcd.points = o3d.utility.Vector3dVector(plane_pts)
            # 统一颜色：绿色平面
            plane_pcd.paint_uniform_color([0, 1, 0])
            o3d.io.write_point_cloud(
                os.path.join(save_dir, f"plane_fit_{timestamp}.ply"), plane_pcd
            )

            # 4. 保存平面参数文本
            with open(os.path.join(save_dir, f"plane_params_{timestamp}.txt"), "w") as f:
                f.write(f"bbox: {bbox}\n")
                f.write(f"plane_model (a,b,c,d): {list(plane_model)}\n")
                f.write(f"inlier_points_cnt: {len(plane_pts)}\n")

            print(f"[Plane-Fit] 结果已保存至 {save_dir}")
        except Exception as e:
            print(f"[ERROR] save_plane_fit_result: {e}")

    # ---------- 新增函数 3：保存角点点云 ----------
    def save_corners(self, corners, save_dir, ts):
        os.makedirs(save_dir, exist_ok=True)
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(corners)
        # 4 个颜色区分
        pcd.colors = o3d.utility.Vector3dVector(np.array([[1, 0, 0], [0, 1, 0], [1, 1, 1], [1, 1, 0]]))
        o3d.io.write_point_cloud(os.path.join(save_dir, f"cabinet_corners_{ts}.ply"), pcd)

# 将返回的错误码转换为十六进制显示
def ToHexStr(num):
    chaDic = {10: 'a', 11: 'b', 12: 'c', 13: 'd', 14: 'e', 15: 'f'}
    hexStr = ""
    if num < 0:
        num = num + 2 ** 32
    while num >= 16:
        digit = num % 16
        hexStr = chaDic.get(digit, str(digit)) + hexStr
        num //= 16
    hexStr = chaDic.get(num, str(num)) + hexStr
    return hexStr

# Decoding Characters
def decoding_char(c_ubyte_value):
    c_char_p_value = ctypes.cast(c_ubyte_value, ctypes.c_char_p)
    try:
        decode_str = c_char_p_value.value.decode('gbk')  # Chinese characters
    except UnicodeDecodeError:
        decode_str = str(c_char_p_value.value)
    return decode_str


if __name__ == "__main__":
    global deviceList
    deviceList = MV_CC_DEVICE_INFO_LIST()

    global cam_checked_list
    cam_checked_list = []

    global obj_cam_operation
    obj_cam_operation = []

    global win_display_handles
    win_display_handles = []

    global valid_number
    valid_number = 0

    global b_is_open
    b_is_open = False

    global b_is_grab
    b_is_grab = False

    global b_is_trigger
    b_is_trigger = False

    global b_is_software_trigger
    b_is_software_trigger = False

    global b_is_hardware_trigger
    b_is_hardware_trigger = False

    global hardware_trigger_line
    hardware_trigger_line = "Line0"  # 默认硬件触发线

    global hardware_trigger_activation
    hardware_trigger_activation = "RisingEdge"  # 默认上升沿触发

    # 立体视觉相关变量
    global stereo_processor
    stereo_processor = None

    global stereo_enabled
    stereo_enabled = False

    global stereo_thread
    stereo_thread = None

    global stereo_running
    stereo_running = False

    # 触发后立体匹配相关变量
    global trigger_stereo_match
    trigger_stereo_match = False

    global last_trigger_images
    last_trigger_images = {"left": None, "right": None}

    _trigger_processed = False   # 全局标志，防止重复处理
    _trigger_cooldown = False  # 全局标志，防止快速连续触发
    _trigger_cooldown_time = 1.0  # 冷却时间，单位为秒

    # 自动保存相关变量
    global auto_save_enabled
    auto_save_enabled = True

    global auto_save_interval
    auto_save_interval = 30  # 30秒自动保存一次

    global auto_save_timer
    auto_save_timer = None

    # ch:初始化SDK | en: initialize SDK
    MvCamera.MV_CC_Initialize()

    # print info in ui
    def print_text(str_info):
        ui.textEdit.append(str_info)  # 使用 append 代替手动操作光标

    # 初始化立体视觉
    def init_stereo_vision():
        global stereo_processor
        try:
            print_text("正在初始化立体视觉系统...")
            stereo_processor = StereoVisionProcessor()

            # 检查立体视觉处理器是否正确初始化
            if (stereo_processor.camera_matrix_left is None or
                stereo_processor.camera_matrix_right is None):
                print_text("立体视觉初始化失败: 标定参数无效")
                stereo_processor = None
                return False

            # 连接信号
            stereo_processor.depth_result_signal.connect(update_depth_display)

            # 设置默认的图像保存参数
            stereo_processor.set_image_save_settings(
                save_input=True,      # 保存原始输入图像
                save_rectified=True,  # 保存校正后图像
                save_comparison=True, # 保存对比图像
                interval=10           # 每10秒保存一次
            )

            print_text("立体视觉系统初始化完成")
            print_text("图像保存设置: 输入图像=开启, 校正图像=开启, 保存间隔=10秒")
            return True
        except Exception as e:
            print_text(f"立体视觉初始化失败: {e}")
            print_text("将使用简化的立体视觉模式")
            stereo_processor = None
            return False

    def update_depth_display(disparity, depth, depth_color):
        try:
            # 如果 depth_color 是带框的图像（BGR格式），直接显示
            if depth_color is not None and len(depth_color.shape) == 3:
                cv2.imshow("YOLO Detection", depth_color)
                #cv2.imshow("YOLO Detection", left_img)
                cv2.waitKey(1)
        except Exception as e:
            print_text(f"显示错误: {e}")
        """更新深度显示"""

        try:
            print_text(f"深度图更新: 尺寸 {depth.shape}, 深度范围 {depth.min():.1f}-{depth.max():.1f}mm")
        except Exception as e:
            print_text(f"深度显示更新错误: {e}")

    def set_stereo_image_save_settings(save_input=True, save_rectified=True, interval=5):
        """设置立体图像保存参数"""
        global stereo_processor
        if stereo_processor is not None:
            stereo_processor.set_image_save_settings(save_input, save_rectified, True, interval)
            print_text(f"立体图像保存设置已更新: 输入图像={save_input}, 校正图像={save_rectified}, 间隔={interval}秒")
        else:
            print_text("立体视觉处理器未初始化")

    def force_save_stereo_images():
        print("[DEBUG] force_save_stereo_images() 被调用了")  # ✅ 加这一行
        """强制保存当前立体图像对"""
        global stereo_processor, obj_cam_operation

        if stereo_processor is None:
            print_text("立体视觉处理器未初始化")
            return

        try:
            # 获取当前左右相机图像
            left_image = get_camera_image(0)
            right_image = get_camera_image(1)

            if left_image is not None and right_image is not None:
                # ✅ 显示 left_img（OpenCV 弹窗）
                cv2.imshow("Left Image Preview", left_image)
                cv2.waitKey(1)  # 保持窗口刷新，1ms 延迟

                # ✅ 保存 left_img 到文件夹
                save_dir = "debug_images"
                os.makedirs(save_dir, exist_ok=True)
                timestamp = int(time.time() * 1000)
                save_path = os.path.join(save_dir, f"left_img_{timestamp}.png")
                cv2.imwrite(save_path, left_image)
                print_text(f"Left image saved: {save_path}")

                stereo_processor.force_save_current_pair(left_image, right_image)
                print_text("已强制保存当前立体图像对")
            else:
                print_text("无法获取有效的立体图像对")

        except Exception as e:
            print_text(f"强制保存立体图像错误: {e}")

    def display_latest_stereo_images():
        """显示最新保存的立体图像（用于调试和验证）"""
        try:
            import glob

            # 查找最新的输入图像
            input_dir = "stereo_input_images"
            if os.path.exists(input_dir):
                input_files = glob.glob(os.path.join(input_dir, "stereo_comparison_input_*.png"))
                if input_files:
                    latest_input = max(input_files, key=os.path.getctime)
                    print_text(f"最新输入图像对比图: {os.path.basename(latest_input)}")

            # 查找最新的校正图像
            rectified_dir = "stereo_rectified_images"
            if os.path.exists(rectified_dir):
                rectified_files = glob.glob(os.path.join(rectified_dir, "stereo_comparison_rectified_*.png"))
                if rectified_files:
                    latest_rectified = max(rectified_files, key=os.path.getctime)
                    print_text(f"最新校正图像对比图: {os.path.basename(latest_rectified)}")

        except Exception as e:
            print_text(f"显示最新立体图像错误: {e}")

    def get_camera_image(camera_index):
        """获取指定相机的图像"""
        global obj_cam_operation
        try:
            if (camera_index < len(obj_cam_operation) and
                    obj_cam_operation[camera_index] != 0 and
                    obj_cam_operation[camera_index].buf_save_image is not None):

                frame_info = obj_cam_operation[camera_index].st_frame_info
                if frame_info.nHeight > 0 and frame_info.nWidth > 0:
                    image_data = np.frombuffer(
                        obj_cam_operation[camera_index].buf_save_image,
                        dtype=np.uint8
                    )

                    try:
                        mono8_format = getattr(sys.modules[__name__], 'PixelType_Gvsp_Mono8', 0x01080001)
                        rgb8_format = getattr(sys.modules[__name__], 'PixelType_Gvsp_RGB8_Packed', 0x02180014)

                        if frame_info.enPixelType == mono8_format:
                            image = image_data.reshape((frame_info.nHeight, frame_info.nWidth))
                            image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                        elif frame_info.enPixelType == rgb8_format:
                            image = image_data.reshape((frame_info.nHeight, frame_info.nWidth, 3))
                            image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
                        else:
                            image = image_data.reshape((frame_info.nHeight, frame_info.nWidth))
                            image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)

                    except Exception as e:
                        print_text(f"像素格式处理错误: {e}")
                        image = image_data.reshape((frame_info.nHeight, frame_info.nWidth))
                        image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)

                    # ✅ 调试：打印形状并保存图像
                    print(f"[DEBUG] get_camera_image({camera_index}) shape: {image.shape}")
                    cv2.imwrite(f"debug_camera{camera_index}.png", image)

                    return image

            return None
        except Exception as e:
            print_text(f"获取相机{camera_index}图像失败: {e}")
            return None

    def start_stereo_vision():
        """启动立体视觉"""
        global stereo_enabled, stereo_processor, stereo_thread, stereo_running

        if not b_is_grab:
            print_text("请先启动相机采集")
            return

        # 检查是否至少选择了两个相机
        selected_count = sum(1 for i in range(4) if i < len(obj_cam_operation) and obj_cam_operation[i] != 0)
        if selected_count < 2:
            print_text("立体视觉需要至少两个相机")
            return

        if stereo_processor is None:
            if not init_stereo_vision():
                return

        # 启动立体处理
        stereo_processor.start_processing()

        #启动图像获取线程
        # if not stereo_running:
        #     stereo_running = True
        #     stereo_thread = threading.Thread(target=stereo_image_acquisition_loop)
        #     stereo_thread.daemon = True
        #     stereo_thread.start()

        stereo_enabled = True
        print_text("立体视觉已启动")
        print_text("立体图像将自动保存到以下目录:")
        print_text("  - 原始图像: stereo_input_images/")
        print_text("  - 校正图像: stereo_rectified_images/")
        print_text("  - 深度图像: depth_images/")
        print_text("  - 点云数据: point_clouds/ (PLY格式)")
        print_text("可调用 force_save_stereo_images() 强制保存当前图像对")

    def stop_stereo_vision():
        """停止立体视觉"""
        global stereo_enabled, stereo_processor, stereo_running

        stereo_enabled = False
        stereo_running = False

        if stereo_processor:
            stereo_processor.stop_processing()

        print_text("立体视觉已停止")

    def stereo_image_acquisition_loop():
        print("[DEBUG] stereo_image_acquisition_loop() 被调用了")  # ✅ 加这一行
        """立体图像获取循环"""
        global stereo_running, stereo_processor, obj_cam_operation

        while stereo_running:
            try:
                # 获取左右相机图像（假设相机0是左相机，相机1是右相机）
                left_image = get_camera_image(0)
                right_image = get_camera_image(1)

                if left_image is not None and right_image is not None and stereo_processor is not None:
                    # 添加到立体处理队列
                    stereo_processor.add_image_pair(left_image, right_image)

                time.sleep(0.033)  # 约30FPS

            except Exception as e:
                print_text(f"立体图像获取错误: {e}")
                time.sleep(0.1)

        # ch:枚举相机 | en:enum devices
        def enum_devices():
            global deviceList
            global valid_number
            deviceList = MV_CC_DEVICE_INFO_LIST()
            n_layer_type = (MV_GIGE_DEVICE | MV_USB_DEVICE
                            | MV_GENTL_GIGE_DEVICE | MV_GENTL_CAMERALINK_DEVICE
                            | MV_GENTL_CXP_DEVICE | MV_GENTL_XOF_DEVICE)
            ret = MvCamera.MV_CC_EnumDevicesEx2(n_layer_type, deviceList, '', SortMethod_SerialNumber)
            if ret != 0:
                str_error = "Enum devices fail! ret = :" + ToHexStr(ret)
                QMessageBox.warning(mainWindow, "Error", str_error, QMessageBox.Ok)
                return ret

            if deviceList.nDeviceNum == 0:
                QMessageBox.warning(mainWindow, "Info", "Find no device", QMessageBox.Ok)
                return ret
            print_text("Find %d devices!" % deviceList.nDeviceNum)

            valid_number = 0
            for i in range(0, 4):
                if (i < deviceList.nDeviceNum) is True:
                    serial_number = ""
                    model_name = ""
                    mvcc_dev_info = cast(deviceList.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
                    if mvcc_dev_info.nTLayerType == MV_GIGE_DEVICE or mvcc_dev_info.nTLayerType == MV_GENTL_GIGE_DEVICE:
                        print("\ngige device: [%d]" % i)
                        user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chUserDefinedName)
                        model_name = decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chModelName)
                        print("device user define name: " + user_defined_name)
                        print("device model name: " + model_name)

                        nip1 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0xff000000) >> 24)
                        nip2 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x00ff0000) >> 16)
                        nip3 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x0000ff00) >> 8)
                        nip4 = (mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x000000ff)
                        print("current ip: %d.%d.%d.%d " % (nip1, nip2, nip3, nip4))

                        for per in mvcc_dev_info.SpecialInfo.stGigEInfo.chSerialNumber:
                            if per == 0:
                                break
                            serial_number = serial_number + chr(per)

                    elif mvcc_dev_info.nTLayerType == MV_USB_DEVICE:
                        print("\nu3v device: [%d]" % i)
                        user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chUserDefinedName)
                        model_name = decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chModelName)
                        print("device user define name: " + user_defined_name)
                        print("device model name: " + model_name)

                        for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                            if per == 0:
                                break
                            serial_number = serial_number + chr(per)
                        print("user serial number: " + serial_number)
                    elif mvcc_dev_info.nTLayerType == MV_GENTL_CAMERALINK_DEVICE:
                        print("\nCML device: [%d]" % i)
                        user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stCMLInfo.chUserDefinedName)
                        model_name = decoding_char(mvcc_dev_info.SpecialInfo.stCMLInfo.chModelName)
                        print("device user define name: " + user_defined_name)
                        print("device model name: " + model_name)

                        for per in mvcc_dev_info.SpecialInfo.stCMLInfo.chSerialNumber:
                            if per == 0:
                                break
                            serial_number = serial_number + chr(per)
                        print("user serial number: " + serial_number)
                    elif mvcc_dev_info.nTLayerType == MV_GENTL_CXP_DEVICE:
                        print("\nCXP device: [%d]" % i)
                        user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stCXPInfo.chUserDefinedName)
                        model_name = decoding_char(mvcc_dev_info.SpecialInfo.stCXPInfo.chModelName)
                        print("device user define name: " + user_defined_name)
                        print("device model name: " + model_name)

                        for per in mvcc_dev_info.SpecialInfo.stCXPInfo.chSerialNumber:
                            if per == 0:
                                break
                            serial_number = serial_number + chr(per)
                        print("user serial number: " + serial_number)
                    elif mvcc_dev_info.nTLayerType == MV_GENTL_XOF_DEVICE:
                        print("\nXoF device: [%d]" % i)
                        user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stXoFInfo.chUserDefinedName)
                        model_name = decoding_char(mvcc_dev_info.SpecialInfo.stXoFInfo.chModelName)
                        print("device user define name: " + user_defined_name)
                        print("device model name: " + model_name)

                        for per in mvcc_dev_info.SpecialInfo.stXoFInfo.chSerialNumber:
                            if per == 0:
                                break
                            serial_number = serial_number + chr(per)
                        print("user serial number: " + serial_number)

                    button_by_id = cam_button_group.button(i)
                    button_by_id.setText("(" + serial_number + ")" + model_name)
                    button_by_id.setEnabled(True)
                    valid_number = valid_number + 1
                else:
                    button_by_id = cam_button_group.button(i)
                    button_by_id.setEnabled(False)

        def cam_check_box_clicked():
            global cam_checked_list
            cam_checked_list = []
            for i in range(0, 4):
                button = cam_button_group.button(i)
                if button.isChecked() is True:
                    cam_checked_list.append(True)
                else:
                    cam_checked_list.append(False)

        def enable_ui_controls():
            global b_is_open
            global b_is_grab
            global b_is_trigger
            global b_is_software_trigger
            global b_is_hardware_trigger
            ui.pushButton_enum.setEnabled(not b_is_open)
            ui.pushButton_open.setEnabled(not b_is_open)
            ui.pushButton_close.setEnabled(b_is_open)
            result1 = False if b_is_grab else b_is_open
            result2 = b_is_open if b_is_grab else False
            ui.pushButton_startGrab.setEnabled(result1)
            ui.pushButton_stopGrab.setEnabled(result2)
            ui.pushButton_saveImg.setEnabled(result2)
            ui.radioButton_continuous.setEnabled(b_is_open)
            ui.radioButton_trigger.setEnabled(b_is_open)
            ui.pushButton_setParams.setEnabled(b_is_open)
            ui.lineEdit_gain.setEnabled(b_is_open)
            ui.lineEdit_frameRate.setEnabled(b_is_open)
            ui.lineEdit_exposureTime.setEnabled(b_is_open)
            result3 = b_is_open if b_is_trigger else False
            ui.pushButton_triggerOnce.setEnabled(b_is_software_trigger and result3)
            ui.checkBox_software_trigger.setEnabled(b_is_trigger)
            ui.checkBox_hardware_trigger.setEnabled(b_is_trigger)

        def open_devices():
            global deviceList
            global obj_cam_operation
            global b_is_open
            global valid_number
            global cam_checked_list
            b_checked = 0
            if b_is_open is True:
                return

            if len(cam_checked_list) <= 0:
                print_text("please select a camera !")
                return
            obj_cam_operation = []
            for i in range(0, 4):
                if cam_checked_list[i] is True:
                    b_checked = True
                    camObj = MvCamera()
                    obj_cam_operation.append(CameraOperation(camObj, deviceList, i))
                    ret = obj_cam_operation[i].open_device()
                    if 0 != ret:
                        obj_cam_operation.pop()
                        print_text("open cam %d fail ret[0x%x]" % (i, ret))
                        continue
                    else:
                        b_is_open = True
                else:
                    obj_cam_operation.append(0)
            if b_checked is False:
                print_text("please select a camera !")
                return
            if b_is_open is False:
                print_text("no camera opened successfully !")
                return
            else:
                ui.radioButton_continuous.setChecked(True)
                enable_ui_controls()

            for i in range(0, 4):
                if (i < valid_number) is True:
                    button_by_id = cam_button_group.button(i)
                    button_by_id.setEnabled(not b_is_open)

        def software_trigger_check_box_clicked():
            global obj_cam_operation
            global b_is_software_trigger
            global b_is_hardware_trigger
            global hardware_trigger_line
            global hardware_trigger_activation

            if (ui.checkBox_software_trigger.isChecked()) is True:
                b_is_software_trigger = True
                b_is_hardware_trigger = False
                ui.checkBox_hardware_trigger.setChecked(False)

                for i in range(0, 4):
                    if obj_cam_operation[i] != 0:
                        ret = obj_cam_operation[i].set_trigger_source("software")
                        if 0 != ret:
                            print_text(
                                'camera' + str(i) + ' set trigger source: software  fail! ret = ' + ToHexStr(ret))
                        else:
                            print_text('camera' + str(i) + ' set to software trigger mode')
            else:
                b_is_software_trigger = False

            enable_ui_controls()

        def hardware_trigger_check_box_clicked():
            global obj_cam_operation
            global b_is_hardware_trigger
            global b_is_software_trigger
            global hardware_trigger_line
            global hardware_trigger_activation

            if (ui.checkBox_hardware_trigger.isChecked()) is True:
                b_is_hardware_trigger = True
                b_is_software_trigger = False
                ui.checkBox_software_trigger.setChecked(False)

                for i in range(0, 4):
                    if obj_cam_operation[i] != 0:
                        # 设置硬件触发源
                        ret = obj_cam_operation[i].set_trigger_source("hardware", hardware_trigger_line)
                        if 0 != ret:
                            print_text('camera' + str(i) + ' set trigger source: hardware fail! ret = ' + ToHexStr(ret))
                            continue

                        # 设置触发极性
                        ret = obj_cam_operation[i].set_trigger_activation(hardware_trigger_activation)
                        if 0 != ret:
                            print_text('camera' + str(i) + ' set trigger activation fail! ret = ' + ToHexStr(ret))
                            continue

                        print_text('camera' + str(
                            i) + f' set to hardware trigger mode ({hardware_trigger_line}, {hardware_trigger_activation})')
            else:
                b_is_hardware_trigger = False

            enable_ui_controls()

        def radio_button_clicked(button):
            global obj_cam_operation
            global b_is_trigger
            button_id = raio_button_group.id(button)
            if (button_id == 0) is True:
                b_is_trigger = False
                for i in range(0, 4):
                    if obj_cam_operation[i] != 0:
                        ret = obj_cam_operation[i].set_trigger_mode("continuous")
                        if 0 != ret:
                            print_text('camera' + str(i) + ' set trigger mode: continuous fail! ret = ' + ToHexStr(ret))
                enable_ui_controls()

            else:
                b_is_trigger = True
                for i in range(0, 4):
                    if obj_cam_operation[i] != 0:
                        ret = obj_cam_operation[i].set_trigger_mode("triggermode")
                        if 0 != ret:
                            print_text('camera' + str(i) + ' set trigger on fail! ret = ' + ToHexStr(ret))
                enable_ui_controls()

        def close_devices():
            global b_is_open
            global obj_cam_operation
            global valid_number

            if b_is_open is False:
                return
            if b_is_grab is True:
                stop_grabbing()
            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].close_device()
                    if 0 != ret:
                        print_text('camera' + str(i) + ' close device fail! ret = ' + ToHexStr(ret))

                if i < valid_number:
                    button_by_id = cam_button_group.button(i)
                    button_by_id.setEnabled(True)
            b_is_open = False
            enable_ui_controls()

        def start_grabbing():
            global obj_cam_operation
            global win_display_handles
            global b_is_open
            global b_is_grab

            if (not b_is_open) or (b_is_grab is True):
                return

            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].start_grabbing(i, win_display_handles[i])
                    if 0 != ret:
                        print_text('camera' + str(i) + ' start grabbing fail! ret = ' + ToHexStr(ret))
                    b_is_grab = True
            enable_ui_controls()

            # 自动启动立体视觉（如果有两个或以上相机）
            selected_count = sum(1 for i in range(4) if i < len(obj_cam_operation) and obj_cam_operation[i] != 0)
            if selected_count >= 2:
                print_text("检测到多个相机，可以启动立体视觉")
                start_stereo_vision()

            # 启动自动保存
            if auto_save_enabled:
                start_auto_save()

        def stop_grabbing():
            global b_is_grab
            global obj_cam_operation
            global b_is_open

            if (not b_is_open) or (b_is_grab is False):
                return

            # 停止立体视觉
            stop_stereo_vision()

            # 停止自动保存
            stop_auto_save()

            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].stop_grabbing()
                    if 0 != ret:
                        print_text('camera' + str(i) + ' stop grabbing fail!ret = ' + ToHexStr(ret))
                    b_is_grab = False
            enable_ui_controls()

        # ch:存图 | en:save image - 增强版本支持触发图像保存
        def save_bmp():
            global b_is_grab
            global obj_cam_operation
            global last_trigger_images

            # 优先保存触发图像，如果没有则保存当前图像
            if (last_trigger_images["left"] is not None or
                    last_trigger_images["right"] is not None):
                print_text("保存触发后的图像...")
                save_trigger_images_manual()
                return

            if b_is_grab is False:
                print_text("无法保存图像：相机未在采集状态")
                return

            try:
                # 创建保存目录
                save_dir = "saved_images"
                if not os.path.exists(save_dir):
                    os.makedirs(save_dir)

                timestamp = int(time.time() * 1000)
                saved_count = 0

                for i in range(0, 4):
                    if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                        # 检查图像缓冲区是否有有效的图像数据
                        if (obj_cam_operation[i].buf_save_image is None or
                                obj_cam_operation[i].st_frame_info is None):
                            print_text(f'camera{i} has no image data to save.')
                            continue

                        frame_info = obj_cam_operation[i].st_frame_info

                        # 检查图像尺寸是否有效
                        if frame_info.nHeight <= 0 or frame_info.nWidth <= 0:
                            print_text(f'camera{i} frame info has invalid dimensions')
                            continue

                        try:
                            # 使用改进的保存方法，传递保存目录
                            ret = obj_cam_operation[i].save_bmp(save_dir)
                            if 0 == ret:
                                saved_count += 1
                                print_text(f'camera{i} image saved successfully to {save_dir}')
                            elif ret != -1:  # -1 表示没有图像数据，不是真正的错误
                                print_text('camera' + str(i) + ' save bmp fail!ret = ' + ToHexStr(ret))

                        except Exception as e:
                            print_text(f'camera{i} save error: {e}')
                            continue

                if saved_count > 0:
                    print_text(f"成功保存 {saved_count} 张图像")
                else:
                    print_text("未能保存任何图像")

            except Exception as e:
                print_text(f"保存图像错误: {e}")

        def save_trigger_images_manual():
            """手动保存触发后的图像"""
            global last_trigger_images

            try:
                save_dir = "trigger_images"
                if not os.path.exists(save_dir):
                    os.makedirs(save_dir)

                timestamp = int(time.time() * 1000)
                saved_count = 0

                if last_trigger_images["left"] is not None:
                    filename = f"trigger_left_{timestamp}.png"
                    filepath = os.path.join(save_dir, filename)
                    cv2.imwrite(filepath, last_trigger_images["left"])
                    saved_count += 1
                    print_text(f'Left trigger image saved: {filename}')

                if last_trigger_images["right"] is not None:
                    filename = f"trigger_right_{timestamp}.png"
                    filepath = os.path.join(save_dir, filename)
                    cv2.imwrite(filepath, last_trigger_images["right"])
                    saved_count += 1
                    print_text(f'Right trigger image saved: {filename}')

                if saved_count > 0:
                    print_text(f"成功保存 {saved_count} 张触发图像到 {save_dir}")
                    # 清空触发图像缓存
                    last_trigger_images = {"left": None, "right": None}
                else:
                    print_text("没有触发图像可保存")

            except Exception as e:
                print_text(f"保存触发图像错误: {e}")

        def is_float(str_value):
            try:
                float(str_value)
                return True
            except ValueError:
                return False

        def set_parameters():
            global obj_cam_operation
            global b_is_open
            if b_is_open is False:
                return

            frame_rate = ui.lineEdit_frameRate.text()
            exposure_time = ui.lineEdit_exposureTime.text()
            gain = ui.lineEdit_gain.text()

            if is_float(frame_rate) is False or is_float(exposure_time) is False or is_float(gain) is False:
                print_text("parameters is valid, please check")
                return

            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_exposure_time(exposure_time)
                    if ret != 0:
                        print_text('camera' + str(i) + ' Set exposure time failed ret:' + ToHexStr(ret))
                    ret = obj_cam_operation[i].set_gain(gain)
                    if ret != 0:
                        print_text('camera' + str(i) + ' Set gain failed ret:' + ToHexStr(ret))
                    ret = obj_cam_operation[i].set_frame_rate(frame_rate)
                    if ret != 0:
                        print_text('camera' + str(i) + ' set acquisition frame rate failed ret:' + ToHexStr(ret))

        def reset_trigger_cooldown():
            global _trigger_cooldown
            _trigger_cooldown = False
            print_text("Trigger cooldown reset")

        def software_trigger_once():
            print("[DEBUG] software_trigger_once() 被调用了")

            global last_trigger_images, stereo_processor, _trigger_processed, _trigger_cooldown

            if _trigger_processed:
                print_text("Trigger already processed, skipping...")
                return

            _trigger_processed = True
            _trigger_cooldown = True

            # 清空上次触发的图像
            last_trigger_images = {"left": None, "right": None}

            for i in range(0, 2):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].trigger_once()
                    if ret != 0:
                        print_text('camera' + str(i) + 'TriggerSoftware failed ret:' + ToHexStr(ret))
                    else:
                        print_text('camera' + str(i) + ' software trigger executed')

                    # ✅ 等待图像采集完成后，手动获取图像并处理一次
            QTimer.singleShot(500, process_single_stereo_pair)

            # 设置冷却时间
            QTimer.singleShot(int(_trigger_cooldown_time * 1000), reset_trigger_cooldown)

        # 处理单次立体匹配
        def process_single_stereo_pair():
            print("[DEBUG] process_single_stereo_pair() 被调用了，时间戳:", time.time())  # ✅ 加这一行
            global last_trigger_images, stereo_processor, _trigger_processed

            if not _trigger_processed:
                print_text("Trigger not initiated, skipping...")
                return

            # 获取左右图像
            left_image = get_camera_image(0)
            right_image = get_camera_image(1)

            if left_image is None or right_image is None:
                print_text("未能获取完整的立体图像对")
                _trigger_processed = False
                return

            # 保存用于调试
            last_trigger_images["left"] = left_image
            last_trigger_images["right"] = right_image

            # 初始化立体视觉（如果未初始化）
            if stereo_processor is None:
                if not init_stereo_vision():
                    _trigger_processed = False
                    return

            # 执行一次立体匹配
            stereo_processor.process_stereo_pair(left_image, right_image)

            # ✅ 强制保存点云和结果c
            save_stereo_results(left_image, right_image, {
                'depth': stereo_processor.latest_depth,
                # 'points_3d': stereo_processor.latest_points_3d
            })

            print_text("单次立体匹配完成，点云已保存")
            _trigger_processed = False

            # 确保清理资源
            stereo_processor.stop_processing()

        def capture_and_save_trigger_images():
            """捕获触发后的图像并自动保存"""
            global last_trigger_images, obj_cam_operation

            try:
                print_text("正在捕获触发后的图像...")

                # 创建保存目录
                save_dir = "trigger_images"
                if not os.path.exists(save_dir):
                    os.makedirs(save_dir)

                timestamp = int(time.time() * 1000)
                saved_count = 0

                # 获取并保存所有相机的图像
                for i in range(0, 4):
                    if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                        # 检查图像缓冲区是否有有效的图像数据
                        if (obj_cam_operation[i].buf_save_image is None or
                                obj_cam_operation[i].st_frame_info is None):
                            print_text(f'camera{i} has no image data to save.')
                            continue

                        frame_info = obj_cam_operation[i].st_frame_info

                        # 检查图像尺寸是否有效
                        if frame_info.nHeight <= 0 or frame_info.nWidth <= 0:
                            print_text(f'camera{i} frame info has invalid dimensions')
                            continue

                        try:
                            # 获取图像数据
                            image_data = np.frombuffer(
                                obj_cam_operation[i].buf_save_image,
                                dtype=np.uint8
                            )

                            # 根据像素格式重塑图像
                            if len(image_data) >= frame_info.nHeight * frame_info.nWidth:
                                # 简单处理为灰度图像
                                image = image_data[:frame_info.nHeight * frame_info.nWidth].reshape(
                                    (frame_info.nHeight, frame_info.nWidth)
                                )

                                # 保存图像
                                filename = f"trigger_cam{i}_{timestamp}.png"
                                filepath = os.path.join(save_dir, filename)
                                cv2.imwrite(filepath, image)

                                saved_count += 1
                                print_text(f'camera{i} image saved: {filename}')

                                # 如果是前两个相机，保存到立体匹配用的变量中
                                if i == 0:
                                    last_trigger_images["left"] = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                                elif i == 1:
                                    last_trigger_images["right"] = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)

                        except Exception as e:
                            print_text(f'camera{i} save error: {e}')
                            continue

                if saved_count > 0:
                    print_text(f"触发图像保存完成！共保存 {saved_count} 张图像到 {save_dir}")

                    # 检查是否成功获取了双目图像，如果是则可以进行立体匹配
                    if last_trigger_images["left"] is not None and last_trigger_images["right"] is not None:
                        print_text("双目图像捕获成功，可以进行立体匹配")
                        # 如果启用了自动立体匹配，则自动进行立体匹配
                        if trigger_stereo_match:
                            perform_trigger_stereo_match()
                else:
                    print_text("未能保存任何触发图像")

            except Exception as e:
                print_text(f"触发图像捕获错误: {e}")

        def capture_trigger_images():
            print("[DEBUG] capture_trigger_images() 被调用了")  # ✅ 加这一行
            """捕获触发后的图像"""
            global last_trigger_images, obj_cam_operation

            try:
                # 获取左相机图像（相机0）
                left_image = get_camera_image(0)
                if left_image is not None:
                    last_trigger_images["left"] = left_image.copy()
                    print_text("Left camera image captured")

                # 获取右相机图像（相机1）
                right_image = get_camera_image(1)
                if right_image is not None:
                    last_trigger_images["right"] = right_image.copy()
                    print_text("Right camera image captured")

                # 检查是否成功获取了双目图像
                if last_trigger_images["left"] is not None and last_trigger_images["right"] is not None:
                    print_text("Stereo image pair captured successfully")
                    # 如果启用了自动立体匹配，则自动进行立体匹配
                    if trigger_stereo_match:
                        perform_trigger_stereo_match()
                else:
                    print_text("Failed to capture stereo image pair")

            except Exception as e:
                print_text(f"Error capturing trigger images: {e}")

        def perform_trigger_stereo_match():
            print("[DEBUG] perform_trigger_stereo_match() 被调用了")
            """对触发后的图像进行立体匹配"""
            global last_trigger_images, stereo_processor

            if (last_trigger_images["left"] is None or
                    last_trigger_images["right"] is None):
                print_text("No captured images available for stereo matching")
                return

            try:
                if stereo_processor is None:
                    if not init_stereo_vision():
                        return

                print_text("Processing stereo matching...")

                # 处理立体图像对
                result = stereo_processor.process_stereo_pair(
                    last_trigger_images["left"],
                    last_trigger_images["right"]
                )

                # 保存结果
                save_stereo_results(last_trigger_images["left"], last_trigger_images["right"], result)

                print_text("Stereo matching completed")

            except Exception as e:
                print_text(f"Error in stereo matching: {e}")

        def save_stereo_results(left_img, right_img, result):
            """保存立体匹配结果"""
            try:
                save_dir = "stereo_results"
                if not os.path.exists(save_dir):
                    os.makedirs(save_dir)

                timestamp = int(time.time() * 1000)

                # 保存原始图像
                cv2.imwrite(os.path.join(save_dir, f"left_{timestamp}.png"), left_img)
                cv2.imwrite(os.path.join(save_dir, f"right_{timestamp}.png"), right_img)

                # 保存深度图和点云
                if result and 'depth' in result and result['depth'] is not None:
                    depth_norm = cv2.normalize(result['depth'], None, 0, 255, cv2.NORM_MINMAX)
                    depth_color = cv2.applyColorMap(depth_norm.astype(np.uint8), cv2.COLORMAP_JET)
                    cv2.imwrite(os.path.join(save_dir, f"depth_{timestamp}.png"), depth_color)

                    # 保存滤波后的点云（统一调用 _save_ply）
                    if 'points_3d' in result and result['points_3d'] is not None:
                        # 构造有效掩码
                        valid_mask = (result['points_3d'][:, :, 2] >= 1500) & (result['points_3d'][:, :, 2] <= 8000)
                        valid_points = result['points_3d'][valid_mask]
                        color_rgb = cv2.cvtColor(left_img, cv2.COLOR_BGR2RGB)
                        valid_colors = color_rgb[valid_mask]

                        if len(valid_points) > 0:
                            stereo_processor._save_ply(valid_points, valid_colors, save_dir, timestamp)
                        else:
                            print_text("无有效点云，跳过保存")

                print_text(f"Stereo results saved to {save_dir}")

            except Exception as e:
                print_text(f"Error saving stereo results: {e}")

        def manual_stereo_match():
            """手动触发立体匹配"""
            global trigger_stereo_match

            if (last_trigger_images["left"] is None or
                    last_trigger_images["right"] is None):
                print_text("No captured images available. Please trigger cameras first.")
                return

            print_text("Manual stereo matching triggered...")
            perform_trigger_stereo_match()

        def enable_auto_stereo_match():
            """启用自动立体匹配"""
            global trigger_stereo_match
            trigger_stereo_match = True
            print_text("自动立体匹配已启用")

        def disable_auto_stereo_match():
            """禁用自动立体匹配"""
            global trigger_stereo_match
            trigger_stereo_match = False
            print_text("自动立体匹配已禁用")

        # 自动保存功能
        def start_auto_save():
            """启动自动保存"""
            global auto_save_timer, auto_save_enabled, auto_save_interval

            if auto_save_enabled and b_is_grab:
                auto_save_timer = QTimer()
                auto_save_timer.timeout.connect(auto_save_images)
                auto_save_timer.start(auto_save_interval * 1000)  # 转换为毫秒
                print_text(f"自动保存已启动，间隔 {auto_save_interval} 秒")

        def stop_auto_save():
            """停止自动保存"""
            global auto_save_timer

            if auto_save_timer:
                auto_save_timer.stop()
                auto_save_timer = None
                print_text("自动保存已停止")

        def auto_save_images():
            """自动保存图像"""
            global b_is_grab, obj_cam_operation

            if not b_is_grab:
                stop_auto_save()
                return

            try:
                # 创建自动保存目录
                save_dir = "auto_saved_images"
                if not os.path.exists(save_dir):
                    os.makedirs(save_dir)

                timestamp = int(time.time() * 1000)
                saved_count = 0

                for i in range(0, 4):
                    if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                        # 检查图像缓冲区是否有有效的图像数据
                        if (obj_cam_operation[i].buf_save_image is None or
                                obj_cam_operation[i].st_frame_info is None):
                            continue

                        frame_info = obj_cam_operation[i].st_frame_info

                        # 检查图像尺寸是否有效
                        if frame_info.nHeight <= 0 or frame_info.nWidth <= 0:
                            continue

                        try:
                            # 获取图像数据
                            image_data = np.frombuffer(
                                obj_cam_operation[i].buf_save_image,
                                dtype=np.uint8
                            )

                            # 根据像素格式重塑图像
                            if len(image_data) >= frame_info.nHeight * frame_info.nWidth:
                                # 简单处理为灰度图像
                                image = image_data[:frame_info.nHeight * frame_info.nWidth].reshape(
                                    (frame_info.nHeight, frame_info.nWidth)
                                )

                                # 保存图像
                                filename = f"auto_cam{i}_{timestamp}.png"
                                filepath = os.path.join(save_dir, filename)
                                cv2.imwrite(filepath, image)
                                saved_count += 1

                        except Exception as e:
                            print_text(f'自动保存相机{i}图像错误: {e}')
                            continue

                if saved_count > 0:
                    print_text(f"自动保存完成：{saved_count} 张图像")

            except Exception as e:
                print_text(f"自动保存错误: {e}")

        def toggle_auto_save():
            """切换自动保存状态"""
            global auto_save_enabled

            auto_save_enabled = not auto_save_enabled
            if auto_save_enabled:
                print_text("自动保存已启用")
                if b_is_grab:
                    start_auto_save()
            else:
                print_text("自动保存已禁用")
                stop_auto_save()

        # 设置硬件触发线
        def set_hardware_trigger_line(line_name):
            global hardware_trigger_line
            global obj_cam_operation
            global b_is_open
            global b_is_software_trigger

            if not b_is_open or b_is_software_trigger:
                hardware_trigger_line = line_name
                print_text(f'Hardware trigger line set to: {line_name}')
                return

            hardware_trigger_line = line_name
            # 如果当前是硬件触发模式，立即应用设置
            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_trigger_source("hardware", hardware_trigger_line)
                    if 0 != ret:
                        print_text('camera' + str(i) + f' set trigger line to {line_name} fail! ret = ' + ToHexStr(ret))
                    else:
                        print_text('camera' + str(i) + f' trigger line set to: {line_name}')

        # 设置硬件触发极性
        def set_hardware_trigger_activation(activation):
            global hardware_trigger_activation
            global obj_cam_operation
            global b_is_open
            global b_is_software_trigger

            if not b_is_open or b_is_software_trigger:
                hardware_trigger_activation = activation
                print_text(f'Hardware trigger activation set to: {activation}')
                return

            hardware_trigger_activation = activation
            # 如果当前是硬件触发模式，立即应用设置
            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_trigger_activation(hardware_trigger_activation)
                    if 0 != ret:
                        print_text(
                            'camera' + str(i) + f' set trigger activation to {activation} fail! ret = ' + ToHexStr(ret))
                    else:
                        print_text('camera' + str(i) + f' trigger activation set to: {activation}')

        # ch: 初始化app, 绑定控件与函数 | en: Init app, bind ui and api
        app = QApplication(sys.argv)
        mainWindow = QMainWindow()
        ui = Ui_MainWindow()
        ui.setupUi(mainWindow)
        ui.pushButton_enum.clicked.connect(enum_devices)
        ui.pushButton_open.clicked.connect(open_devices)
        ui.pushButton_close.clicked.connect(close_devices)
        ui.pushButton_startGrab.clicked.connect(start_grabbing)
        ui.pushButton_stopGrab.clicked.connect(stop_grabbing)
        ui.pushButton_saveImg.clicked.connect(save_bmp)
        ui.pushButton_setParams.clicked.connect(set_parameters)
        ui.checkBox_software_trigger.clicked.connect(software_trigger_check_box_clicked)
        ui.checkBox_hardware_trigger.clicked.connect(hardware_trigger_check_box_clicked)
        ui.pushButton_triggerOnce.clicked.connect(software_trigger_once)

        # 绑定立体匹配按钮（如果UI中有的话）
        if hasattr(ui, 'pushButton_stereo_match'):
            ui.pushButton_stereo_match.clicked.connect(manual_stereo_match)
        cam_button_group = QButtonGroup(mainWindow)
        cam_button_group.addButton(ui.checkBox_1, 0)
        cam_button_group.addButton(ui.checkBox_2, 1)
        cam_button_group.addButton(ui.checkBox_3, 2)
        cam_button_group.addButton(ui.checkBox_4, 3)

        cam_button_group.setExclusive(False)
        cam_button_group.buttonClicked.connect(cam_check_box_clicked)

        raio_button_group = QButtonGroup(mainWindow)
        raio_button_group.addButton(ui.radioButton_continuous, 0)
        raio_button_group.addButton(ui.radioButton_trigger, 1)
        raio_button_group.buttonClicked.connect(radio_button_clicked)

        win_display_handles.append(ui.widget_display1.winId())
        win_display_handles.append(ui.widget_display2.winId())
        win_display_handles.append(ui.widget_display3.winId())
        win_display_handles.append(ui.widget_display4.winId())

        mainWindow.show()
        enum_devices()
        enable_ui_controls()

        # 初始化立体视觉
        init_stereo_vision()

        app.exec_()

        close_devices()

        # ch:反初始化SDK | en: finalize SDK
        MvCamera.MV_CC_Finalize()

        sys.exit()