# -*- coding: utf-8 -*-
# generated by 'xml2py'
# flags '-c -d -v C:\test_h\PixelType.xml -o PixelType_header.py'
from ctypes import *



PixelType_Gvsp_YUV411_Packed = 34340894
PixelType_Gvsp_Mono1p = 16842807
PixelType_Gvsp_YCBCR422_8 = 34603067
PixelType_Gvsp_BayerGR10 = 17825804
PixelType_Gvsp_BayerGR12_Packed = 17563690
PixelType_Gvsp_BayerGB8 = 17301514
PixelType_Gvsp_BayerGB16 = 17825840
PixelType_Gvsp_YCBCR601_422_8 = 34603070
PixelType_Gvsp_YCBCR709_8_CBYCR = 35127360
PixelType_Gvsp_BayerRG10 = 17825805
PixelType_Gvsp_RGB10_Packed = 36700184
PixelType_Gvsp_COORD3D_DEPTH_PLUS_MASK = -2112094207
PixelType_Gvsp_YCBCR601_422_8_CBYCRY = 34603076
PixelType_Gvsp_Mono14 = 17825829
PixelType_Gvsp_YCBCR601_411_8_CBYYCRYY = 34340927
PixelType_Gvsp_BayerGB10_Packed = 17563688
PixelType_Gvsp_BayerGB12 = 17825810
PixelType_Gvsp_YUV422_YUYV_Packed = 34603058
PixelType_Gvsp_Mono8 = 17301505
PixelType_Gvsp_BayerGR12 = 17825808
PixelType_Gvsp_YCBCR709_422_8 = 34603073
PixelType_Gvsp_RGB10V1_Packed = 35651612
PixelType_Gvsp_BayerBG12 = 17825811
PixelType_Gvsp_BayerRG12 = 17825809
PixelType_Gvsp_BayerBG10 = 17825807
PixelType_Gvsp_RGB8_Packed = 35127316
PixelType_Gvsp_BGR565_Packed = 34603062
PixelType_Gvsp_Coord3D_ABC32f_Planar = 39846081
PixelType_Gvsp_YCBCR601_8_CBYCR = 35127357
PixelType_Gvsp_BayerGB10 = 17825806
PixelType_Gvsp_YCBCR8_CBYCR = 35127354
PixelType_Gvsp_BayerRG8 = 17301513
PixelType_Gvsp_BayerBG12_Packed = 17563693
PixelType_Gvsp_BayerRG16 = 17825839
PixelType_Gvsp_BayerBG16 = 17825841
PixelType_Gvsp_BayerGR16 = 17825838
PixelType_Gvsp_Undefined = -1
PixelType_Gvsp_BayerBG10_Packed = 17563689
PixelType_Gvsp_BayerGB12_Packed = 17563692
PixelType_Gvsp_BayerBG8 = 17301515
PixelType_Gvsp_YCBCR422_8_CBYCRY = 34603075
PixelType_Gvsp_RGB10_Planar = 36700194
PixelType_Gvsp_BayerGR10_Packed = 17563686
PixelType_Gvsp_RGB16_Planar = 36700196
PixelType_Gvsp_RGB565_Packed = 34603061
PixelType_Gvsp_Mono4p = 17039417
PixelType_Gvsp_Coord3D_ABC32f = 39846080
PixelType_Gvsp_BGR10_Packed = 36700185
PixelType_Gvsp_YCBCR411_8_CBYYCRYY = 34340924
PixelType_Gvsp_BayerRG12_Packed = 17563691
PixelType_Gvsp_YUV422_Packed = 34603039
PixelType_Gvsp_YCBCR709_411_8_CBYYCRYY = 34340930
PixelType_Gvsp_RGB8_Planar = 35127329
PixelType_Gvsp_Mono10 = 17825795
PixelType_Gvsp_BayerGR8 = 17301512
PixelType_Gvsp_YCBCR709_422_8_CBYCRY = 34603077
PixelType_Gvsp_Jpeg = -2145910783
PixelType_Gvsp_RGB12_Packed = 36700186
PixelType_Gvsp_RGB12_Planar = 36700195
PixelType_Gvsp_Mono2p = 16908344
PixelType_Gvsp_BGR12_Packed = 36700187
PixelType_Gvsp_RGB12V1_Packed = 35913780
PixelType_Gvsp_BGR8_Packed = 35127317
PixelType_Gvsp_RGBA8_Packed = 35651606
PixelType_Gvsp_RGB16_Packed = 36700211
PixelType_Gvsp_YUV444_Packed = 35127328
PixelType_Gvsp_RGB10V2_Packed = 35651613
PixelType_Gvsp_Mono16 = 17825799
PixelType_Gvsp_Mono8_Signed = 17301506
PixelType_Gvsp_Mono10_Packed = 17563652
PixelType_Gvsp_BGRA8_Packed = 35651607
PixelType_Gvsp_Mono12_Packed = 17563654
PixelType_Gvsp_Mono12 = 17825797
PixelType_Gvsp_BayerRG10_Packed = 17563687
PixelType_Gvsp_Coord3D_AC32f = 36176066
PixelType_Gvsp_Coord3D_ABC32 = -2107625471
PixelType_Gvsp_Coord3D_AB32f = -2109722622
PixelType_Gvsp_Coord3D_AB32 = -2109722621
PixelType_Gvsp_Coord3D_ABC16 = 36700345
PixelType_Gvsp_Coord3D_C32 = -2128596986
PixelType_Gvsp_Coord3D_C32f = 18874559
PixelType_Gvsp_Coord3D_AC32f_64 = 37748930
PixelType_Gvsp_Coord3D_A32f = 18874557
PixelType_Gvsp_Coord3D_AC32 = -2109722620
PixelType_Gvsp_Coord3D_AC32f_Planar = 37748931
PixelType_Gvsp_Coord3D_A32 = -2128596987
PixelType_Gvsp_YUV420SP_NV12 = 34373633
PixelType_Gvsp_YUV420SP_NV21 = 34373634
PixelType_Gvsp_Coord3D_C16 = 17825976
PixelType_Gvsp_HB_Mono8 = -2130182143
PixelType_Gvsp_HB_Mono10 = -2129657853
PixelType_Gvsp_HB_Mono10_Packed = -2129919996
PixelType_Gvsp_HB_Mono12 = -2129657851
PixelType_Gvsp_HB_Mono12_Packed = -2129919994
PixelType_Gvsp_HB_Mono16 = -2129657849
PixelType_Gvsp_HB_BayerGR8 = -2130182136
PixelType_Gvsp_HB_BayerRG8 = -2130182135
PixelType_Gvsp_HB_BayerGB8 = -2130182134
PixelType_Gvsp_HB_BayerBG8 = -2130182133
PixelType_Gvsp_HB_BayerRBGG8 = -2130182074
PixelType_Gvsp_HB_BayerGR10 = -2129657844
PixelType_Gvsp_HB_BayerRG10 = -2129657843
PixelType_Gvsp_HB_BayerGB10 = -2129657842
PixelType_Gvsp_HB_BayerBG10 = -2129657841
PixelType_Gvsp_HB_BayerGR12 = -2129657840
PixelType_Gvsp_HB_BayerRG12 = -2129657839
PixelType_Gvsp_HB_BayerGB12 = -2129657838
PixelType_Gvsp_HB_BayerBG12 = -2129657837
PixelType_Gvsp_HB_BayerGR10_Packed = -2129919962
PixelType_Gvsp_HB_BayerRG10_Packed = -2129919961
PixelType_Gvsp_HB_BayerGB10_Packed = -2129919960
PixelType_Gvsp_HB_BayerBG10_Packed = -2129919959
PixelType_Gvsp_HB_BayerGR12_Packed = -2129919958
PixelType_Gvsp_HB_BayerRG12_Packed = -2129919957
PixelType_Gvsp_HB_BayerGB12_Packed = -2129919956
PixelType_Gvsp_HB_BayerBG12_Packed = -2129919955
PixelType_Gvsp_HB_YUV422_Packed = -2112880609
PixelType_Gvsp_HB_YUV422_YUYV_Packed = -2112880590
PixelType_Gvsp_HB_RGB8_Packed = -2112356332
PixelType_Gvsp_HB_BGR8_Packed = -2112356331
PixelType_Gvsp_HB_RGBA8_Packed = -2111832042
PixelType_Gvsp_HB_BGRA8_Packed = -2111832041
PixelType_Gvsp_HB_RGB16_Packed = -2110783437
PixelType_Gvsp_HB_BGR16_Packed = -2110783413
PixelType_Gvsp_HB_RGBA16_Packed = -2109734812
PixelType_Gvsp_HB_BGRA16_Packed = -2109734831
int8_t = c_int8
int16_t = c_int16
int32_t = c_int32
int64_t = c_int64
uint8_t = c_uint8
uint16_t = c_uint16
uint32_t = c_uint32
uint64_t = c_uint64
int_least8_t = c_byte
int_least16_t = c_short
int_least32_t = c_int
int_least64_t = c_long
uint_least8_t = c_ubyte
uint_least16_t = c_ushort
uint_least32_t = c_uint
uint_least64_t = c_ulong
int_fast8_t = c_byte
int_fast16_t = c_long
int_fast32_t = c_long
int_fast64_t = c_long
uint_fast8_t = c_ubyte
uint_fast16_t = c_ulong
uint_fast32_t = c_ulong
uint_fast64_t = c_ulong
intptr_t = c_long
uintptr_t = c_ulong
intmax_t = c_long
uintmax_t = c_ulong

# values for enumeration 'MvGvspPixelType'
MvGvspPixelType = c_int # enum
__all__ = ['PixelType_Gvsp_BayerRG8', 'int_fast32_t',
           'PixelType_Gvsp_YCBCR422_8',
           'PixelType_Gvsp_COORD3D_DEPTH_PLUS_MASK', 'uint8_t',
           'PixelType_Gvsp_RGB10V1_Packed', 'uint_least16_t',
           'PixelType_Gvsp_RGB8_Planar', 'intptr_t', 'uint_least64_t',
           'int_least32_t', 'PixelType_Gvsp_RGB16_Packed',
           'PixelType_Gvsp_RGBA8_Packed',
           'PixelType_Gvsp_RGB8_Packed',
           'PixelType_Gvsp_Mono8_Signed', 'int_least16_t',
           'uint_fast16_t', 'PixelType_Gvsp_BayerBG10_Packed',
           'PixelType_Gvsp_YCBCR709_422_8_CBYCRY',
           'PixelType_Gvsp_YUV411_Packed', 'intmax_t',
           'PixelType_Gvsp_BayerBG12_Packed', 'int16_t',
           'int_fast64_t', 'PixelType_Gvsp_BayerRG12',
           'PixelType_Gvsp_BayerRG10', 'PixelType_Gvsp_BayerRG16',
           'uint_fast32_t', 'PixelType_Gvsp_YCBCR709_411_8_CBYYCRYY',
           'int_least8_t', 'PixelType_Gvsp_BayerGB12_Packed',
           'PixelType_Gvsp_Coord3D_AC32f', 'uint_least8_t',
           'PixelType_Gvsp_BayerRG12_Packed',
           'PixelType_Gvsp_Undefined', 'PixelType_Gvsp_YUV422_Packed',
           'PixelType_Gvsp_RGB10_Packed',
           'PixelType_Gvsp_RGB12_Planar',
           'PixelType_Gvsp_YCBCR709_422_8', 'uint16_t',
           'uint_fast8_t', 'PixelType_Gvsp_BGR8_Packed',
           'PixelType_Gvsp_Jpeg', 'int32_t',
           'PixelType_Gvsp_BayerGR10_Packed',
           'PixelType_Gvsp_BayerBG12', 'PixelType_Gvsp_BayerBG10',
           'PixelType_Gvsp_BayerBG16', 'PixelType_Gvsp_Mono12',
           'PixelType_Gvsp_RGB16_Planar', 'PixelType_Gvsp_Mono4p',
           'PixelType_Gvsp_BayerRG10_Packed', 'PixelType_Gvsp_Mono8',
           'uint_least32_t', 'PixelType_Gvsp_BayerGR16',
           'PixelType_Gvsp_BayerGR10', 'PixelType_Gvsp_BGRA8_Packed',
           'PixelType_Gvsp_BayerGR12', 'uintptr_t',
           'PixelType_Gvsp_Mono12_Packed', 'int8_t',
           'PixelType_Gvsp_YCBCR709_8_CBYCR',
           'PixelType_Gvsp_YCBCR601_422_8',
           'PixelType_Gvsp_YCBCR411_8_CBYYCRYY', 'int_least64_t',
           'PixelType_Gvsp_BGR12_Packed', 'uint64_t',
           'PixelType_Gvsp_BayerGR12_Packed',
           'PixelType_Gvsp_YCBCR601_411_8_CBYYCRYY',
           'PixelType_Gvsp_RGB10_Planar', 'PixelType_Gvsp_BayerGB16',
           'PixelType_Gvsp_BayerGB10', 'uintmax_t',
           'PixelType_Gvsp_BayerGB12', 'PixelType_Gvsp_BGR565_Packed',
           'int64_t', 'int_fast16_t', 'PixelType_Gvsp_Mono1p',
           'PixelType_Gvsp_YUV444_Packed',
           'PixelType_Gvsp_YUV422_YUYV_Packed',
           'PixelType_Gvsp_BayerBG8', 'int_fast8_t',
           'PixelType_Gvsp_BGR10_Packed',
           'PixelType_Gvsp_BayerGB10_Packed',
           'PixelType_Gvsp_Coord3D_ABC32f_Planar',
           'PixelType_Gvsp_Coord3D_ABC32f',
           'PixelType_Gvsp_YCBCR422_8_CBYCRY',
           'PixelType_Gvsp_RGB12_Packed', 'PixelType_Gvsp_BayerGR8',
           'MvGvspPixelType', 'PixelType_Gvsp_Mono10',
           'PixelType_Gvsp_Mono16', 'PixelType_Gvsp_Mono2p',
           'PixelType_Gvsp_Mono14', 'PixelType_Gvsp_RGB10V2_Packed',
           'PixelType_Gvsp_RGB12V1_Packed',
           'PixelType_Gvsp_Mono10_Packed', 'uint_fast64_t',
           'PixelType_Gvsp_YCBCR601_8_CBYCR',
           'PixelType_Gvsp_BayerGB8', 'PixelType_Gvsp_YCBCR8_CBYCR',
           'PixelType_Gvsp_RGB565_Packed', 'uint32_t',
           'PixelType_Gvsp_YCBCR601_422_8_CBYCRY',
           'PixelType_Gvsp_Coord3D_ABC32','PixelType_Gvsp_Coord3D_AB32f',
           'PixelType_Gvsp_Coord3D_AB32','PixelType_Gvsp_Coord3D_ABC16',
           'PixelType_Gvsp_Coord3D_C32','PixelType_Gvsp_Coord3D_C32f',
           'PixelType_Gvsp_Coord3D_AC32f_64','PixelType_Gvsp_Coord3D_A32f',
           'PixelType_Gvsp_Coord3D_AC32','PixelType_Gvsp_Coord3D_AC32f_Planar',
           'PixelType_Gvsp_Coord3D_A32','PixelType_Gvsp_YUV420SP_NV12',
           'PixelType_Gvsp_YUV420SP_NV21','PixelType_Gvsp_Coord3D_C16',
           'PixelType_Gvsp_HB_Mono8',
           'PixelType_Gvsp_HB_Mono10',
           'PixelType_Gvsp_HB_Mono10_Packed',
           'PixelType_Gvsp_HB_Mono12',
           'PixelType_Gvsp_HB_Mono12_Packed',
           'PixelType_Gvsp_HB_Mono16',
           'PixelType_Gvsp_HB_BayerGR8',
           'PixelType_Gvsp_HB_BayerRG8',
           'PixelType_Gvsp_HB_BayerGB8',
           'PixelType_Gvsp_HB_BayerBG8',
           'PixelType_Gvsp_HB_BayerRBGG8',
           'PixelType_Gvsp_HB_BayerGR10',
           'PixelType_Gvsp_HB_BayerRG10',
           'PixelType_Gvsp_HB_BayerGB10',
           'PixelType_Gvsp_HB_BayerBG10',
           'PixelType_Gvsp_HB_BayerGR12',
           'PixelType_Gvsp_HB_BayerRG12',
           'PixelType_Gvsp_HB_BayerGB12',
           'PixelType_Gvsp_HB_BayerBG12',
           'PixelType_Gvsp_HB_BayerGR10_Packed',
           'PixelType_Gvsp_HB_BayerRG10_Packed',
           'PixelType_Gvsp_HB_BayerGB10_Packed',
           'PixelType_Gvsp_HB_BayerBG10_Packed',
           'PixelType_Gvsp_HB_BayerGR12_Packed',
           'PixelType_Gvsp_HB_BayerRG12_Packed',
           'PixelType_Gvsp_HB_BayerGB12_Packed',
           'PixelType_Gvsp_HB_BayerBG12_Packed',
           'PixelType_Gvsp_HB_YUV422_Packed',
           'PixelType_Gvsp_HB_YUV422_YUYV_Packed',
           'PixelType_Gvsp_HB_RGB8_Packed',
           'PixelType_Gvsp_HB_BGR8_Packed',
           'PixelType_Gvsp_HB_RGBA8_Packed',
           'PixelType_Gvsp_HB_BGRA8_Packed',
           'PixelType_Gvsp_HB_RGB16_Packed',
           'PixelType_Gvsp_HB_BGR16_Packed',
           'PixelType_Gvsp_HB_RGBA16_Packed',
           'PixelType_Gvsp_HB_BGRA16_Packed']
