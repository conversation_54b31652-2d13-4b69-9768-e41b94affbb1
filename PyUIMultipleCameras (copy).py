# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'PyUIMultipleCameras.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(1185, 818)
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.centralwidget)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setObjectName("verticalLayout")
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setObjectName("gridLayout")
        self.widget_display1 = QtWidgets.QWidget(self.centralwidget)
        self.widget_display1.setMinimumSize(QtCore.QSize(200, 0))
        self.widget_display1.setObjectName("widget_display1")
        self.gridLayout.addWidget(self.widget_display1, 1, 0, 1, 1)
        self.checkBox_1 = QtWidgets.QCheckBox(self.centralwidget)
        self.checkBox_1.setObjectName("checkBox_1")
        self.gridLayout.addWidget(self.checkBox_1, 0, 0, 1, 1)
        self.checkBox_3 = QtWidgets.QCheckBox(self.centralwidget)
        self.checkBox_3.setObjectName("checkBox_3")
        self.gridLayout.addWidget(self.checkBox_3, 2, 0, 1, 1)
        self.checkBox_2 = QtWidgets.QCheckBox(self.centralwidget)
        self.checkBox_2.setObjectName("checkBox_2")
        self.gridLayout.addWidget(self.checkBox_2, 0, 1, 1, 1)
        self.checkBox_4 = QtWidgets.QCheckBox(self.centralwidget)
        self.checkBox_4.setObjectName("checkBox_4")
        self.gridLayout.addWidget(self.checkBox_4, 2, 1, 1, 1)
        self.widget_display2 = QtWidgets.QWidget(self.centralwidget)
        self.widget_display2.setObjectName("widget_display2")
        self.gridLayout.addWidget(self.widget_display2, 1, 1, 1, 1)
        self.widget_display4 = QtWidgets.QWidget(self.centralwidget)
        self.widget_display4.setObjectName("widget_display4")
        self.gridLayout.addWidget(self.widget_display4, 3, 1, 1, 1)
        self.widget_display3 = QtWidgets.QWidget(self.centralwidget)
        self.widget_display3.setObjectName("widget_display3")
        self.gridLayout.addWidget(self.widget_display3, 3, 0, 1, 1)
        self.verticalLayout.addLayout(self.gridLayout)
        self.textEdit = QtWidgets.QTextEdit(self.centralwidget)
        self.textEdit.setMaximumSize(QtCore.QSize(16777215, 120))
        self.textEdit.setObjectName("textEdit")
        self.verticalLayout.addWidget(self.textEdit)
        self.horizontalLayout.addLayout(self.verticalLayout)

        # 创建右侧控制面板的垂直布局
        self.control_panel_layout = QtWidgets.QVBoxLayout()
        self.control_panel_layout.setContentsMargins(10, 10, 10, 10)
        self.control_panel_layout.setSpacing(10)
        self.control_panel_layout.setObjectName("control_panel_layout")

        # 设备控制组
        self.device_group = QtWidgets.QGroupBox(self.centralwidget)
        self.device_group.setObjectName("device_group")
        self.device_group.setTitle("设备控制")
        self.device_layout = QtWidgets.QGridLayout(self.device_group)
        self.device_layout.setSpacing(8)

        self.pushButton_enum = QtWidgets.QPushButton(self.device_group)
        self.pushButton_enum.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_enum.setObjectName("pushButton_enum")
        self.device_layout.addWidget(self.pushButton_enum, 0, 0, 1, 1)

        self.pushButton_open = QtWidgets.QPushButton(self.device_group)
        self.pushButton_open.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_open.setObjectName("pushButton_open")
        self.device_layout.addWidget(self.pushButton_open, 0, 1, 1, 1)

        self.pushButton_close = QtWidgets.QPushButton(self.device_group)
        self.pushButton_close.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_close.setObjectName("pushButton_close")
        self.device_layout.addWidget(self.pushButton_close, 1, 0, 1, 1)

        self.control_panel_layout.addWidget(self.device_group)

        # 采集控制组
        self.capture_group = QtWidgets.QGroupBox(self.centralwidget)
        self.capture_group.setObjectName("capture_group")
        self.capture_group.setTitle("采集控制")
        self.capture_layout = QtWidgets.QGridLayout(self.capture_group)
        self.capture_layout.setSpacing(8)

        self.radioButton_continuous = QtWidgets.QRadioButton(self.capture_group)
        self.radioButton_continuous.setObjectName("radioButton_continuous")
        self.capture_layout.addWidget(self.radioButton_continuous, 0, 0, 1, 1)

        self.radioButton_trigger = QtWidgets.QRadioButton(self.capture_group)
        self.radioButton_trigger.setObjectName("radioButton_trigger")
        self.capture_layout.addWidget(self.radioButton_trigger, 0, 1, 1, 1)

        self.pushButton_startGrab = QtWidgets.QPushButton(self.capture_group)
        self.pushButton_startGrab.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_startGrab.setObjectName("pushButton_startGrab")
        self.capture_layout.addWidget(self.pushButton_startGrab, 1, 0, 1, 1)

        self.pushButton_stopGrab = QtWidgets.QPushButton(self.capture_group)
        self.pushButton_stopGrab.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_stopGrab.setObjectName("pushButton_stopGrab")
        self.capture_layout.addWidget(self.pushButton_stopGrab, 1, 1, 1, 1)

        self.pushButton_saveImg = QtWidgets.QPushButton(self.capture_group)
        self.pushButton_saveImg.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_saveImg.setObjectName("pushButton_saveImg")
        self.capture_layout.addWidget(self.pushButton_saveImg, 2, 0, 1, 2)

        self.control_panel_layout.addWidget(self.capture_group)

        # 触发控制组
        self.trigger_group = QtWidgets.QGroupBox(self.centralwidget)
        self.trigger_group.setObjectName("trigger_group")
        self.trigger_group.setTitle("触发控制")
        self.trigger_layout = QtWidgets.QGridLayout(self.trigger_group)
        self.trigger_layout.setSpacing(8)

        self.checkBox_software_trigger = QtWidgets.QCheckBox(self.trigger_group)
        self.checkBox_software_trigger.setObjectName("checkBox_software_trigger")
        self.trigger_layout.addWidget(self.checkBox_software_trigger, 0, 0, 1, 1)

        self.pushButton_triggerOnce = QtWidgets.QPushButton(self.trigger_group)
        self.pushButton_triggerOnce.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_triggerOnce.setObjectName("pushButton_triggerOnce")
        self.trigger_layout.addWidget(self.pushButton_triggerOnce, 0, 1, 1, 1)

        self.checkBox_hardware_trigger = QtWidgets.QCheckBox(self.trigger_group)
        self.checkBox_hardware_trigger.setObjectName("checkBox_hardware_trigger")
        self.trigger_layout.addWidget(self.checkBox_hardware_trigger, 1, 0, 1, 1)

        # 硬件触发线选择
        self.label_trigger_line = QtWidgets.QLabel(self.trigger_group)
        self.label_trigger_line.setObjectName("label_trigger_line")
        self.trigger_layout.addWidget(self.label_trigger_line, 2, 0, 1, 1)

        self.comboBox_trigger_line = QtWidgets.QComboBox(self.trigger_group)
        self.comboBox_trigger_line.setObjectName("comboBox_trigger_line")
        self.comboBox_trigger_line.addItems(["Line0", "Line1", "Line2", "Line3"])
        self.trigger_layout.addWidget(self.comboBox_trigger_line, 2, 1, 1, 1)

        # 触发极性选择
        self.label_trigger_activation = QtWidgets.QLabel(self.trigger_group)
        self.label_trigger_activation.setObjectName("label_trigger_activation")
        self.trigger_layout.addWidget(self.label_trigger_activation, 3, 0, 1, 1)

        self.comboBox_trigger_activation = QtWidgets.QComboBox(self.trigger_group)
        self.comboBox_trigger_activation.setObjectName("comboBox_trigger_activation")
        self.comboBox_trigger_activation.addItems(["RisingEdge", "FallingEdge", "LevelHigh", "LevelLow"])
        self.trigger_layout.addWidget(self.comboBox_trigger_activation, 3, 1, 1, 1)

        self.control_panel_layout.addWidget(self.trigger_group)

        # 立体视觉组
        self.stereo_group = QtWidgets.QGroupBox(self.centralwidget)
        self.stereo_group.setObjectName("stereo_group")
        self.stereo_group.setTitle("立体视觉")
        self.stereo_layout = QtWidgets.QGridLayout(self.stereo_group)
        self.stereo_layout.setSpacing(8)

        self.pushButton_stereo_match = QtWidgets.QPushButton(self.stereo_group)
        self.pushButton_stereo_match.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_stereo_match.setObjectName("pushButton_stereo_match")
        self.stereo_layout.addWidget(self.pushButton_stereo_match, 0, 0, 1, 2)

        self.control_panel_layout.addWidget(self.stereo_group)

        # 参数设置组
        self.params_group = QtWidgets.QGroupBox(self.centralwidget)
        self.params_group.setObjectName("params_group")
        self.params_group.setTitle("参数设置")
        self.params_layout = QtWidgets.QGridLayout(self.params_group)
        self.params_layout.setSpacing(8)

        self.label_exposure = QtWidgets.QLabel(self.params_group)
        self.label_exposure.setObjectName("label_exposure")
        self.params_layout.addWidget(self.label_exposure, 0, 0, 1, 1)

        self.lineEdit_exposureTime = QtWidgets.QLineEdit(self.params_group)
        self.lineEdit_exposureTime.setObjectName("lineEdit_exposureTime")
        self.params_layout.addWidget(self.lineEdit_exposureTime, 0, 1, 1, 1)

        self.label_gain = QtWidgets.QLabel(self.params_group)
        self.label_gain.setObjectName("label_gain")
        self.params_layout.addWidget(self.label_gain, 1, 0, 1, 1)

        self.lineEdit_gain = QtWidgets.QLineEdit(self.params_group)
        self.lineEdit_gain.setObjectName("lineEdit_gain")
        self.params_layout.addWidget(self.lineEdit_gain, 1, 1, 1, 1)

        self.label_frameRate = QtWidgets.QLabel(self.params_group)
        self.label_frameRate.setObjectName("label_frameRate")
        self.params_layout.addWidget(self.label_frameRate, 2, 0, 1, 1)

        self.lineEdit_frameRate = QtWidgets.QLineEdit(self.params_group)
        self.lineEdit_frameRate.setObjectName("lineEdit_frameRate")
        self.params_layout.addWidget(self.lineEdit_frameRate, 2, 1, 1, 1)

        self.pushButton_setParams = QtWidgets.QPushButton(self.params_group)
        self.pushButton_setParams.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_setParams.setObjectName("pushButton_setParams")
        self.params_layout.addWidget(self.pushButton_setParams, 3, 0, 1, 2)

        self.control_panel_layout.addWidget(self.params_group)

        # 添加弹性空间
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.control_panel_layout.addItem(spacerItem)

        self.horizontalLayout.addLayout(self.control_panel_layout)
        self.horizontalLayout.setStretch(0, 3)
        self.horizontalLayout.setStretch(1, 1)
        self.horizontalLayout_2.addLayout(self.horizontalLayout)
        MainWindow.setCentralWidget(self.centralwidget)
        self.menubar = QtWidgets.QMenuBar(MainWindow)
        self.menubar.setGeometry(QtCore.QRect(0, 0, 1185, 26))
        self.menubar.setObjectName("menubar")
        MainWindow.setMenuBar(self.menubar)
        self.statusbar = QtWidgets.QStatusBar(MainWindow)
        self.statusbar.setObjectName("statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "多相机立体视觉系统"))

        # 相机选择框
        self.checkBox_1.setText(_translate("MainWindow", "Cam1"))
        self.checkBox_3.setText(_translate("MainWindow", "Cam3"))
        self.checkBox_2.setText(_translate("MainWindow", "Cam2"))
        self.checkBox_4.setText(_translate("MainWindow", "Cam4"))

        # 分组框标题
        self.device_group.setTitle(_translate("MainWindow", "设备控制"))
        self.capture_group.setTitle(_translate("MainWindow", "采集控制"))
        self.trigger_group.setTitle(_translate("MainWindow", "触发控制"))
        self.stereo_group.setTitle(_translate("MainWindow", "立体视觉"))
        self.params_group.setTitle(_translate("MainWindow", "参数设置"))

        # 设备控制按钮
        self.pushButton_enum.setText(_translate("MainWindow", "枚举设备"))
        self.pushButton_open.setText(_translate("MainWindow", "打开设备"))
        self.pushButton_close.setText(_translate("MainWindow", "关闭设备"))

        # 采集控制
        self.radioButton_continuous.setText(_translate("MainWindow", "连续模式"))
        self.radioButton_trigger.setText(_translate("MainWindow", "触发模式"))
        self.pushButton_startGrab.setText(_translate("MainWindow", "开始采集"))
        self.pushButton_stopGrab.setText(_translate("MainWindow", "停止采集"))
        self.pushButton_saveImg.setText(_translate("MainWindow", "保存图像"))

        # 触发控制
        self.checkBox_software_trigger.setText(_translate("MainWindow", "软件触发"))
        self.checkBox_hardware_trigger.setText(_translate("MainWindow", "硬件触发"))
        self.pushButton_triggerOnce.setText(_translate("MainWindow", "单次触发"))
        self.label_trigger_line.setText(_translate("MainWindow", "触发线"))
        self.label_trigger_activation.setText(_translate("MainWindow", "触发边沿"))

        # 立体视觉
        self.pushButton_stereo_match.setText(_translate("MainWindow", "立体匹配"))

        # 参数设置
        self.label_exposure.setText(_translate("MainWindow", "曝光时间"))
        self.label_gain.setText(_translate("MainWindow", "增益"))
        self.label_frameRate.setText(_translate("MainWindow", "帧率"))
        self.pushButton_setParams.setText(_translate("MainWindow", "设置参数"))
