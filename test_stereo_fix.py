#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试立体匹配修复的脚本
"""

import sys
import numpy as np

def test_opencv_import():
    """测试OpenCV导入"""
    try:
        import cv2
        print(f"✓ OpenCV导入成功，版本: {cv2.__version__}")
        return True
    except ImportError as e:
        print(f"✗ OpenCV导入失败: {e}")
        return False

def test_stereo_matcher_creation():
    """测试立体匹配器创建"""
    try:
        import cv2
        
        print("测试立体匹配器创建...")
        
        # 方法1：直接创建
        try:
            stereo = cv2.StereoSGBM_create(
                minDisparity=0,
                numDisparities=96,
                blockSize=7,
                P1=8 * 3 * 7 ** 2,
                P2=32 * 3 * 7 ** 2,
                disp12MaxDiff=1,
                uniquenessRatio=10,
                speckleWindowSize=100,
                speckleRange=32,
                preFilterCap=63,
                mode=cv2.STEREO_SGBM_MODE_SGBM_3WAY
            )
            
            if stereo is None:
                print("✗ 方法1: StereoSGBM_create 返回 None")
            elif not hasattr(stereo, 'compute'):
                print("✗ 方法1: 创建的对象没有compute方法")
            elif not callable(getattr(stereo, 'compute')):
                print(f"✗ 方法1: compute方法不可调用，类型: {type(getattr(stereo, 'compute'))}")
            else:
                print("✓ 方法1: 立体匹配器创建成功")
                return True
                
        except Exception as e:
            print(f"✗ 方法1失败: {e}")
        
        # 方法2：备用方法
        try:
            stereo = cv2.StereoSGBM_create()
            if stereo is not None and hasattr(stereo, 'compute') and callable(getattr(stereo, 'compute')):
                print("✓ 方法2: 备用方法创建成功")
                return True
            else:
                print("✗ 方法2: 备用方法也失败")
        except Exception as e:
            print(f"✗ 方法2失败: {e}")
        
        return False
        
    except ImportError:
        print("✗ 无法导入OpenCV")
        return False

def test_stereo_vision_processor():
    """测试StereoVisionProcessor类"""
    try:
        # 只导入我们需要的部分，避免其他依赖问题
        import cv2
        import numpy as np
        from queue import Queue
        import threading
        import time
        import os
        
        # 简化版的StereoVisionProcessor用于测试
        class TestStereoVisionProcessor:
            def __init__(self):
                self.calibration_params = None
                self.rectification_maps = None
                self.Q_matrix = None
                self.calibration_available = False
                
                # 立体匹配器
                try:
                    self.stereo_matcher = self.create_stereo_matcher()
                    if self.stereo_matcher is None:
                        print("警告: 立体匹配器创建失败，立体视觉功能将不可用")
                        self.stereo_available = False
                    else:
                        print("立体匹配器初始化成功")
                        self.stereo_available = True
                except Exception as e:
                    print(f"立体匹配器初始化错误: {e}")
                    self.stereo_matcher = None
                    self.stereo_available = False
            
            def create_stereo_matcher(self):
                """创建立体匹配器"""
                try:
                    stereo = cv2.StereoSGBM_create(
                        minDisparity=0,
                        numDisparities=96,
                        blockSize=7,
                        P1=8 * 3 * 7 ** 2,
                        P2=32 * 3 * 7 ** 2,
                        disp12MaxDiff=1,
                        uniquenessRatio=10,
                        speckleWindowSize=100,
                        speckleRange=32,
                        preFilterCap=63,
                        mode=cv2.STEREO_SGBM_MODE_SGBM_3WAY
                    )
                    
                    if stereo is None:
                        print("错误: StereoSGBM_create 返回 None")
                        raise RuntimeError("Failed to create StereoSGBM matcher")
                    
                    if not hasattr(stereo, 'compute') or not callable(getattr(stereo, 'compute')):
                        print("错误: 创建的立体匹配器没有可调用的compute方法")
                        raise RuntimeError("StereoSGBM matcher does not have callable compute method")
                    
                    return stereo
                    
                except Exception as e:
                    print(f"创建立体匹配器失败: {e}")
                    return None
            
            def check_stereo_matcher(self):
                """检查立体匹配器状态"""
                if self.stereo_matcher is None:
                    return False, "立体匹配器为None"
                
                if not hasattr(self.stereo_matcher, 'compute'):
                    return False, "立体匹配器没有compute方法"
                
                if not callable(getattr(self.stereo_matcher, 'compute')):
                    return False, "立体匹配器的compute方法不可调用"
                
                return True, "立体匹配器状态正常"
        
        print("测试StereoVisionProcessor类...")
        processor = TestStereoVisionProcessor()
        
        if processor.stereo_available:
            is_valid, status_msg = processor.check_stereo_matcher()
            if is_valid:
                print("✓ StereoVisionProcessor测试成功")
                return True
            else:
                print(f"✗ StereoVisionProcessor测试失败: {status_msg}")
                return False
        else:
            print("✗ StereoVisionProcessor测试失败: 立体视觉功能不可用")
            return False
            
    except Exception as e:
        print(f"✗ StereoVisionProcessor测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 立体匹配修复测试 ===\n")
    
    # 测试1: OpenCV导入
    print("1. 测试OpenCV导入...")
    if not test_opencv_import():
        print("请先安装OpenCV: pip install opencv-python")
        return False
    print()
    
    # 测试2: 立体匹配器创建
    print("2. 测试立体匹配器创建...")
    if not test_stereo_matcher_creation():
        print("立体匹配器创建失败")
        return False
    print()
    
    # 测试3: StereoVisionProcessor类
    print("3. 测试StereoVisionProcessor类...")
    if not test_stereo_vision_processor():
        print("StereoVisionProcessor类测试失败")
        return False
    print()
    
    print("✓ 所有测试通过！立体匹配修复成功。")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
