# -*- coding: utf-8 -*-
import sys
import time

from PyQt5.QtCore import QTimer
from PyQt5.QtWidgets import QApplication, QMainWindow, QButtonGroup, QMessageBox
from PyQt5.QtWidgets import *
from PyQt5.QtGui import QTextCursor
from CamOperation_class import CameraOperation
from MvCameraControl_class import *
from MvErrorDefine_const import *
from CameraParams_header import *
from PyUIMultipleCameras import Ui_MainWindow
from PyQt5.QtCore import QTimer
import numpy as np
import cv2
import os
import ctypes

# 将返回的错误码转换为十六进制显示
def ToHexStr(num):
    chaDic = {10: 'a', 11: 'b', 12: 'c', 13: 'd', 14: 'e', 15: 'f'}
    hexStr = ""
    if num < 0:
        num = num + 2 ** 32
    while num >= 16:
        digit = num % 16
        hexStr = chaDic.get(digit, str(digit)) + hexStr
        num //= 16
    hexStr = chaDic.get(num, str(num)) + hexStr
    return hexStr

# Decoding Characters
def decoding_char(c_ubyte_value):
    c_char_p_value = ctypes.cast(c_ubyte_value, ctypes.c_char_p)
    try:
        decode_str = c_char_p_value.value.decode('gbk')  # Chinese characters
    except UnicodeDecodeError:
        decode_str = str(c_char_p_value.value)
    return decode_str


if __name__ == "__main__":

    global deviceList
    deviceList = MV_CC_DEVICE_INFO_LIST()

    global cam_checked_list
    cam_checked_list = []

    global obj_cam_operation
    obj_cam_operation = []

    global win_display_handles
    win_display_handles = []

    global valid_number
    valid_number = 0

    global b_is_open
    b_is_open = False

    global b_is_grab
    b_is_grab = False

    global b_is_trigger
    b_is_trigger = False

    global b_is_software_trigger
    b_is_software_trigger = False

    global b_is_hardware_trigger
    b_is_hardware_trigger = False

    global hardware_trigger_line
    hardware_trigger_line = "Line0"  # 默认硬件触发线

    global hardware_trigger_activation
    hardware_trigger_activation = "RisingEdge"  # 默认上升沿触发


    # ch:初始化SDK | en: initialize SDK
    MvCamera.MV_CC_Initialize()

    # print info in ui
    def print_text(str_info):
        ui.textEdit.moveCursor(QTextCursor.Start)
        ui.textEdit.insertPlainText(str_info + "\n")

    # ch:枚举相机 | en:enum devices
    def enum_devices():
        global deviceList
        global valid_number
        deviceList = MV_CC_DEVICE_INFO_LIST()
        n_layer_type = (MV_GIGE_DEVICE | MV_USB_DEVICE
                        | MV_GENTL_GIGE_DEVICE | MV_GENTL_CAMERALINK_DEVICE
                        | MV_GENTL_CXP_DEVICE | MV_GENTL_XOF_DEVICE)
        ret = MvCamera.MV_CC_EnumDevicesEx2(n_layer_type, deviceList, '', SortMethod_SerialNumber)
        if ret != 0:
            str_error = "Enum devices fail! ret = :" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", str_error, QMessageBox.Ok)
            return ret

        if deviceList.nDeviceNum == 0:
            QMessageBox.warning(mainWindow, "Info", "Find no device", QMessageBox.Ok)
            return ret
        print_text("Find %d devices!" % deviceList.nDeviceNum)

        valid_number = 0
        for i in range(0, 4):
            if (i < deviceList.nDeviceNum) is True:
                serial_number = ""
                model_name = ""
                mvcc_dev_info = cast(deviceList.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
                if mvcc_dev_info.nTLayerType == MV_GIGE_DEVICE or mvcc_dev_info.nTLayerType == MV_GENTL_GIGE_DEVICE:
                    print("\ngige device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    nip1 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0xff000000) >> 24)
                    nip2 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x00ff0000) >> 16)
                    nip3 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x0000ff00) >> 8)
                    nip4 = (mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x000000ff)
                    print("current ip: %d.%d.%d.%d " % (nip1, nip2, nip3, nip4))

                    for per in mvcc_dev_info.SpecialInfo.stGigEInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)

                elif mvcc_dev_info.nTLayerType == MV_USB_DEVICE:
                    print("\nu3v device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_CAMERALINK_DEVICE:
                    print("\nCML device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stCMLInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stCMLInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stCMLInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_CXP_DEVICE:
                    print("\nCXP device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stCXPInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stCXPInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stCXPInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_XOF_DEVICE:
                    print("\nXoF device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stXoFInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stXoFInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stXoFInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)

                button_by_id = cam_button_group.button(i)
                button_by_id.setText("(" + serial_number + ")" + model_name)
                button_by_id.setEnabled(True)
                valid_number = valid_number + 1
            else:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(False)

    def cam_check_box_clicked():
        global cam_checked_list
        cam_checked_list = []
        for i in range(0, 4):
            button = cam_button_group.button(i)
            if button.isChecked() is True:
                cam_checked_list.append(True)
            else:
                cam_checked_list.append(False)

    def enable_ui_controls():
        global b_is_open
        global b_is_grab
        global b_is_trigger
        global b_is_software_trigger
        global b_is_hardware_trigger
        ui.pushButton_enum.setEnabled(not b_is_open)
        ui.pushButton_open.setEnabled(not b_is_open)
        ui.pushButton_close.setEnabled(b_is_open)
        result1 = False if b_is_grab else b_is_open
        result2 = b_is_open if b_is_grab else False
        ui.pushButton_startGrab.setEnabled(result1)
        ui.pushButton_stopGrab.setEnabled(result2)
        ui.pushButton_saveImg.setEnabled(result2)
        ui.radioButton_continuous.setEnabled(b_is_open)
        ui.radioButton_trigger.setEnabled(b_is_open)
        ui.pushButton_setParams.setEnabled(b_is_open)
        ui.lineEdit_gain.setEnabled(b_is_open)
        ui.lineEdit_frameRate.setEnabled(b_is_open)
        ui.lineEdit_exposureTime.setEnabled(b_is_open)
        result3 = b_is_open if b_is_trigger else False
        ui.pushButton_triggerOnce.setEnabled(b_is_software_trigger and result3)
        ui.checkBox_software_trigger.setEnabled(b_is_trigger)
        ui.checkBox_hardware_trigger.setEnabled(b_is_trigger)

    def open_devices():
        global deviceList
        global obj_cam_operation
        global b_is_open
        global valid_number
        global cam_checked_list
        b_checked = 0
        if b_is_open is True:
            return

        if len(cam_checked_list) <= 0:
            print_text("please select a camera !")
            return
        obj_cam_operation = []
        for i in range(0, 4):
            if cam_checked_list[i] is True:
                b_checked = True
                camObj = MvCamera()
                obj_cam_operation.append(CameraOperation(camObj, deviceList, i))
                ret = obj_cam_operation[i].open_device()
                if 0 != ret:
                    obj_cam_operation.pop()
                    print_text("open cam %d fail ret[0x%x]" % (i, ret))
                    continue
                else:
                    b_is_open = True
            else:
                obj_cam_operation.append(0)
        if b_checked is False:
            print_text("please select a camera !")
            return
        if b_is_open is False:
            print_text("no camera opened successfully !")
            return
        else:
            ui.radioButton_continuous.setChecked(True)
            enable_ui_controls()

        for i in range(0, 4):
            if(i < valid_number) is True:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(not b_is_open)

    def software_trigger_check_box_clicked():
        global obj_cam_operation
        global b_is_software_trigger
        global b_is_hardware_trigger
        global b_is_grab
        global hardware_trigger_line
        global hardware_trigger_activation

        if ui.checkBox_software_trigger.isChecked():
            b_is_software_trigger = True
            b_is_hardware_trigger = False
            # 防止触发硬件触发复选框的clicked信号
            ui.checkBox_hardware_trigger.blockSignals(True)
            ui.checkBox_hardware_trigger.setChecked(False)
            ui.checkBox_hardware_trigger.blockSignals(False)

            # 先停采集再改模式，避免参数不生效
            was_grabbing = b_is_grab
            if b_is_grab:
                stop_grabbing()

            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_software_trigger()
                    if ret != 0:
                        print_text('camera' + str(i) + ' set to software trigger fail! ret = ' + ToHexStr(ret))
                    else:
                        print_text('camera' + str(i) + ' set to software trigger mode')

            # 如果之前在采集，重新启动采集
            if was_grabbing:
                start_grabbing()
        else:
            b_is_software_trigger = False

        enable_ui_controls()

    def hardware_trigger_check_box_clicked():
        global obj_cam_operation
        global b_is_hardware_trigger
        global b_is_software_trigger
        global b_is_grab
        global hardware_trigger_line
        global hardware_trigger_activation

        if ui.checkBox_hardware_trigger.isChecked():
            b_is_hardware_trigger = True
            b_is_software_trigger = False
            # 防止触发软件触发复选框的clicked信号
            ui.checkBox_software_trigger.blockSignals(True)
            ui.checkBox_software_trigger.setChecked(False)
            ui.checkBox_software_trigger.blockSignals(False)

            # 先停采集再改模式，避免参数不生效
            was_grabbing = b_is_grab
            if b_is_grab:
                stop_grabbing()

            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    # 基础硬件触发模式（On + LineX + RisingEdge）
                    ret = obj_cam_operation[i].set_hardware_trigger()
                    if ret != 0:
                        print_text('camera' + str(i) + ' set to hardware trigger fail! ret = ' + ToHexStr(ret))
                        continue
                    # 根据UI参数细化触发线与极性
                    try:
                        obj_cam_operation[i].obj_cam.MV_CC_SetEnumValueByString("TriggerSource", hardware_trigger_line)
                        obj_cam_operation[i].obj_cam.MV_CC_SetEnumValueByString("TriggerActivation", hardware_trigger_activation)
                    except Exception as e:
                        print_text('camera' + str(i) + f' apply trigger options fail: {e}')
                        continue
                    print_text('camera' + str(i) + f' set to hardware trigger mode ({hardware_trigger_line}, {hardware_trigger_activation})')

            # 如果之前在采集，重新启动采集
            if was_grabbing:
                start_grabbing()
        else:
            b_is_hardware_trigger = False

        enable_ui_controls()

    def radio_button_clicked(button):
        global obj_cam_operation
        global b_is_trigger
        global b_is_grab
        button_id = raio_button_group.id(button)
        if button_id == 0:
            # 连续模式
            b_is_trigger = False
            # 若正在采集，先停止再改模式
            if b_is_grab:
                stop_grabbing()
            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_continuous_mode()
                    if ret != 0:
                        print_text('camera' + str(i) + ' set continuous mode fail! ret = ' + ToHexStr(ret))
            enable_ui_controls()
        else:
            # 触发模式：仅切换UI状态，具体选择由软件/硬件复选框决定
            b_is_trigger = True
            enable_ui_controls()

    def close_devices():
        global b_is_open
        global obj_cam_operation
        global valid_number

        if b_is_open is False:
            return
        if b_is_grab is True:
            stop_grabbing()
        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].close_device()
                if 0 != ret:
                    print_text('camera' + str(i) + ' close device fail! ret = ' + ToHexStr(ret))

            if i < valid_number:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(True)
        b_is_open = False
        enable_ui_controls()

    def start_grabbing():
        global obj_cam_operation
        global win_display_handles
        global b_is_open
        global b_is_grab

        if (not b_is_open) or (b_is_grab is True):
            return

        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].start_grabbing(i, win_display_handles[i])
                if 0 != ret:
                    print_text('camera' + str(i) + ' start grabbing fail! ret = ' + ToHexStr(ret))
                else:
                    # A. 软件触发模式下，延迟打印一次Trigger配置（只读校验）
                    if b_is_software_trigger:
                        try:
                            QTimer.singleShot(200, lambda cam=i: print_text(f"cam{cam} started in SW trigger mode"))
                        except:
                            pass
                b_is_grab = True
        enable_ui_controls()

        # B. 若当前为硬件触发模式，则启动轮询自动保存
        if b_is_hardware_trigger:
            QTimer.singleShot(100, start_hw_trigger_polling)

    def stop_grabbing():
        global b_is_grab
        global obj_cam_operation
        global b_is_open

        if (not b_is_open) or (b_is_grab is False):
            return

        # 停止硬件触发轮询
        stop_hw_trigger_polling()

        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].stop_grabbing()
                if 0 != ret:
                    print_text('camera' + str(i) + ' stop grabbing fail!ret = ' + ToHexStr(ret))
                b_is_grab = False
        enable_ui_controls()

    # ch:存图 | en:save image
    def save_bmp():
        global b_is_grab
        global obj_cam_operation

        if b_is_grab is False:
            print_text("Cannot save image: Camera is not grabbing")
            return

        saved_count = 0
        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].save_bmp()
                if 0 == ret:
                    saved_count += 1


    # ===== 立体匹配：辅助函数与处理逻辑（参考 MultipleCamera6-7-tcp3.py 的简化版） =====
    stereo_matcher = None  # 全局匹配器
    _rectify_maps_ready = False
    _lmapx, _lmapy, _rmapx, _rmapy = None, None, None, None

    def get_stereo_matcher():
        """创建/返回一个全局的SGBM立体匹配器"""
        global stereo_matcher
        if stereo_matcher is None:
            stereo_matcher = cv2.StereoSGBM_create(
                minDisparity=0,
                numDisparities=96,
                blockSize=7,
                P1=8 * 3 * 7 ** 2,
                P2=32 * 3 * 7 ** 2,
                disp12MaxDiff=1,
                uniquenessRatio=10,
                speckleWindowSize=100,
                speckleRange=32,
                preFilterCap=63,
                mode=cv2.STEREO_SGBM_MODE_SGBM_3WAY,
            )
        return stereo_matcher

    def init_stereo_rectify_if_needed(width, height):
        """初始化立体校正映射表"""
        global _rectify_maps_ready, _lmapx, _lmapy, _rmapx, _rmapy
        if _rectify_maps_ready:
            return
        try:
            # 使用内嵌的相机参数进行立体校正
            R1, R2, P1, P2, _, _, _ = cv2.stereoRectify(
                camera_matrix_left, dist_coeffs_left,
                camera_matrix_right, dist_coeffs_right,
                (width, height), R, T
            )
            _lmapx, _lmapy = cv2.initUndistortRectifyMap(
                camera_matrix_left, dist_coeffs_left, R1, P1, (width, height), cv2.CV_32FC1
            )
            _rmapx, _rmapy = cv2.initUndistortRectifyMap(
                camera_matrix_right, dist_coeffs_right, R2, P2, (width, height), cv2.CV_32FC1
            )
            _rectify_maps_ready = True
            print_text("立体校正映射表初始化完成")
        except Exception as e:
            print_text(f"立体校正初始化失败: {e}")
            _rectify_maps_ready = False

    # ===== 相机内参/外参（直接内嵌） =====
    camera_matrix_left = np.array([
        [2349.599303017811, 0.0, 1221.0886985822297],
        [0.0, 2347.04087849075, 1021.2297950652342],
        [0.0, 0.0, 1.0]
    ], dtype=np.float64)

    camera_matrix_right = np.array([
        [2347.7080632127045, 0.0, 1219.3168735048296],
        [0.0, 2347.528871737054, 1010.4282529230558],
        [0.0, 0.0, 1.0]
    ], dtype=np.float64)

    dist_coeffs_left = np.array([
        [-0.0677072434895743, 0.16840134514589222, -0.00013311325437381048,
         -0.0010946605867930416, -0.19743756744235746]
    ], dtype=np.float64).reshape(5, 1)

    dist_coeffs_right = np.array([
        [-0.07691265481784593, 0.22703604995053306, 0.00015959041360151294,
         -0.0011580170802655745, -0.3538743014783903]
    ], dtype=np.float64).reshape(5, 1)

    # ===== 立体校正映射缓存 =====
    _rectify_maps_ready = False
    _lmapx = _lmapy = _rmapx = _rmapy = None
    _Q = None

    def init_stereo_rectify_if_needed(w, h):
        """根据当前帧尺寸生成/更新校正映射（若需要）"""
        global _rectify_maps_ready, _lmapx, _lmapy, _rmapx, _rmapy, _Q
        try:
            sz = (w, h)  # 使用当前帧尺寸
            R1, R2, P1, P2, Q, _, _ = cv2.stereoRectify(
                camera_matrix_left, dist_coeffs_left,
                camera_matrix_right, dist_coeffs_right,
                sz, R, T, flags=cv2.CALIB_ZERO_DISPARITY, alpha=0)
            _lmapx, _lmapy = cv2.initUndistortRectifyMap(
                camera_matrix_left, dist_coeffs_left, R1, P1, sz, cv2.CV_32FC1)
            _rmapx, _rmapy = cv2.initUndistortRectifyMap(
                camera_matrix_right, dist_coeffs_right, R2, P2, sz, cv2.CV_32FC1)
            _Q = Q
            _rectify_maps_ready = True
        except Exception as e:
            print_text(f"init_stereo_rectify_if_needed error: {e}")
            _rectify_maps_ready = False


    # 读取相机触发模式与来源（用于一致性校验）
    def get_trigger_mode_source(i):
        try:
            if i >= len(obj_cam_operation) or obj_cam_operation[i] == 0:
                return None, None
            mode = MVCC_ENUMVALUE(); src = MVCC_ENUMVALUE()
            mret = obj_cam_operation[i].obj_cam.MV_CC_GetEnumValue("TriggerMode", mode)
            sret = obj_cam_operation[i].obj_cam.MV_CC_GetEnumValue("TriggerSource", src)
            if mret != 0 or sret != 0:
                return None, None
            return mode.nCurValue, src.nCurValue
        except Exception as e:
            print_text(f"get_trigger_mode_source({i}) error: {e}")
            return None, None

    # 在软件触发后多次尝试获取左右图像，成功后再做立体匹配
    def attempt_stereo_after_sw_trigger(attempt=0, max_attempts=10):
        print_text(f"stereo: 尝试获取软件触发后的图像 (第{attempt+1}次)")
        left = get_camera_image(0)
        right = get_camera_image(1)
        if left is not None and right is not None:
            print_text("stereo: 软件触发后图像已就绪，开始立体匹配")
            process_single_stereo_pair()
            return
        if attempt < max_attempts:
            print_text(f"stereo: 图像未就绪，100ms后重试...")
            QTimer.singleShot(100, lambda: attempt_stereo_after_sw_trigger(attempt+1, max_attempts))
        else:
            print_text("stereo: 软件触发后等待图像超时")

    R = np.array([
        [0.9998155478060632, -0.0008436404567199599, -0.01918746038540903],
        [0.0008172502236645439, 0.9999987094440285, -0.0013831891951225166],
        [0.019188602537281753, 0.0013672531065493177, 0.9998149469534894]
    ], dtype=np.float64)

    T = np.array([
        [-100.87040766250446],
        [0.06079718879422688],
        [-1.3284405860235702]
    ], dtype=np.float64)

    image_size = (2448, 2048)
    baseline_mm = 100.87917323555243


    def get_camera_image(camera_index):
        """从采集缓冲中取一帧到OpenCV图像(BGR)"""
        try:
            if (camera_index < len(obj_cam_operation) and
                obj_cam_operation[camera_index] != 0 and
                obj_cam_operation[camera_index].buf_save_image is not None):

                cam = obj_cam_operation[camera_index]
                frame_info = cam.st_frame_info
                width = frame_info.nWidth
                height = frame_info.nHeight
                frame_len = frame_info.nFrameLen

                # 从缓冲区构建numpy数组
                buf = (ctypes.c_ubyte * frame_len).from_address(ctypes.addressof(cam.buf_save_image))
                arr = np.frombuffer(buf, dtype=np.uint8)

                if frame_info.enPixelType == PixelType_Gvsp_BGR8_Packed:
                    image = arr.reshape(height, width, 3).copy()
                    return image
                elif frame_info.enPixelType == PixelType_Gvsp_Mono8:
                    gray = arr.reshape(height, width).copy()
                    return cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
                else:
                    # 其他像素格式可按需扩展
                    return None
        except Exception as e:
            print_text(f"get_camera_image({camera_index}) error: {e}")
        return None

    # 硬件触发结果轮询（增强B）：两路都有新图像则自动做一次立体匹配
    _hw_trigger_polling = False
    _last_hw_trigger_time = 0

    def poll_hardware_trigger_results():
        global _hw_trigger_polling, b_is_hardware_trigger, b_is_grab, _last_hw_trigger_time
        if not (_hw_trigger_polling and b_is_hardware_trigger and b_is_grab):
            return  # 停止轮询

        current_time = time.time()
        left = get_camera_image(0)
        right = get_camera_image(1)
        if (left is not None and right is not None and
            current_time - _last_hw_trigger_time > 1.0):  # 1秒防抖
            _last_hw_trigger_time = current_time
            process_single_stereo_pair()
        # 继续轮询
        QTimer.singleShot(100, poll_hardware_trigger_results)

    def start_hw_trigger_polling():
        global _hw_trigger_polling
        _hw_trigger_polling = True
        poll_hardware_trigger_results()

    def stop_hw_trigger_polling():
        global _hw_trigger_polling
        _hw_trigger_polling = False

    def save_stereo_results(left_img, right_img, disparity):
        """保存立体匹配结果（原图+视差伪彩）"""
        try:
            out_dir = "stereo_results"
            if not os.path.exists(out_dir):
                os.makedirs(out_dir)
            ts = int(time.time() * 1000)

            cv2.imwrite(os.path.join(out_dir, f"left_{ts}.png"), left_img)
            cv2.imwrite(os.path.join(out_dir, f"right_{ts}.png"), right_img)

            disp = np.clip(disparity, 0, None)
            disp_norm = cv2.normalize(disp, None, alpha=0, beta=255, norm_type=cv2.NORM_MINMAX)
            disp_norm = disp_norm.astype(np.uint8)
            disp_color = cv2.applyColorMap(disp_norm, cv2.COLORMAP_JET)
            cv2.imwrite(os.path.join(out_dir, f"disp_{ts}.png"), disp_color)
            print_text(f"stereo results saved to {out_dir}, ts={ts}")
        except Exception as e:
            print_text(f"save_stereo_results error: {e}")

    def process_single_stereo_pair():
        """获取相机0/1的最新帧，做一次立体匹配并保存结果（含校正）"""
        print_text("stereo: 开始处理立体匹配...")

        left = get_camera_image(0)
        right = get_camera_image(1)

        if left is None:
            print_text("stereo: 左相机图像未就绪")
            return
        if right is None:
            print_text("stereo: 右相机图像未就绪")
            return

        print_text(f"stereo: 获取到图像 - 左:{left.shape}, 右:{right.shape}")

        h, w = left.shape[:2]
        init_stereo_rectify_if_needed(w, h)
        if _rectify_maps_ready:
            print_text("stereo: 应用立体校正...")
            left = cv2.remap(left, _lmapx, _lmapy, cv2.INTER_LINEAR)
            right = cv2.remap(right, _rmapx, _rmapy, cv2.INTER_LINEAR)
        else:
            print_text("stereo: 跳过立体校正（映射表未就绪）")

        print_text("stereo: 计算视差...")
        left_gray = cv2.cvtColor(left, cv2.COLOR_BGR2GRAY)
        right_gray = cv2.cvtColor(right, cv2.COLOR_BGR2GRAY)
        matcher = get_stereo_matcher()
        disp = matcher.compute(left_gray, right_gray).astype(np.float32) / 16.0

        print_text("stereo: 保存结果...")
        save_stereo_results(left, right, disp)
        print_text("stereo: 立体匹配处理完成")


    def is_float(str_value):
        try:
            float(str_value)
            return True
        except ValueError:
            return False

    def set_parameters():
        global obj_cam_operation
        global b_is_open, b_is_grab
        if b_is_open is False:
            print_text("Please open camera(s) first")
            return

        frame_rate = ui.lineEdit_frameRate.text()
        exposure_time = ui.lineEdit_exposureTime.text()
        gain = ui.lineEdit_gain.text()

        if is_float(frame_rate) is False or is_float(exposure_time) is False or is_float(gain) is False:
            print_text("parameters is invalid, please check")
            return

        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                # 若在采集中，先停
                was_grabbing = obj_cam_operation[i].b_start_grabbing
                if was_grabbing:
                    obj_cam_operation[i].stop_grabbing()

                # 写参数：关闭自动、写入值、使能帧率
                try:
                    obj_cam_operation[i].obj_cam.MV_CC_SetEnumValueByString("ExposureAuto", "Off")
                except:
                    obj_cam_operation[i].obj_cam.MV_CC_SetEnumValue("ExposureAuto", 0)
                ret = obj_cam_operation[i].set_exposure_time(exposure_time)
                print_text(f'camera{i} set ExposureTime -> ret={ToHexStr(ret)}')

                try:
                    obj_cam_operation[i].obj_cam.MV_CC_SetEnumValueByString("GainAuto", "Off")
                except:
                    obj_cam_operation[i].obj_cam.MV_CC_SetEnumValue("GainAuto", 0)
                ret = obj_cam_operation[i].set_gain(gain)
                print_text(f'camera{i} set Gain -> ret={ToHexStr(ret)}')

                try:
                    obj_cam_operation[i].obj_cam.MV_CC_SetBoolValue("AcquisitionFrameRateEnable", True)
                except:
                    pass
                ret = obj_cam_operation[i].set_frame_rate(frame_rate)
                print_text(f'camera{i} set FrameRate -> ret={ToHexStr(ret)}')

                # 若之前在采集，重启显示
                if was_grabbing:
                    obj_cam_operation[i].start_grabbing(i, win_display_handles[i])
        print_text("Parameters applied.")

    _last_sw_trigger_time = 0

    def software_trigger_once():
        global _last_sw_trigger_time
        current_time = time.time()

        # 防抖：1秒内只允许一次软件触发
        if current_time - _last_sw_trigger_time < 1.0:
            print_text("软件触发冷却中，请稍后再试")
            return

        _last_sw_trigger_time = current_time

        # 仅对0/1（左右目）进行一次软件触发，并在500ms后做一次立体匹配与保存
        triggered = 0
        for i in range(0, 2):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].trigger_once()
                if ret != 0:
                    print_text('camera' + str(i) + ' TriggerSoftware failed ret:' + ToHexStr(ret))
                else:
                    triggered += 1
                    print_text('camera' + str(i) + ' software trigger executed')
        if triggered > 0:
            QTimer.singleShot(500, attempt_stereo_after_sw_trigger)

    # 设置硬件触发线
    def set_hardware_trigger_line(line_name):
        global hardware_trigger_line
        global obj_cam_operation
        global b_is_open
        global b_is_software_trigger

        if not b_is_open or b_is_software_trigger:
            hardware_trigger_line = line_name
            print_text(f'Hardware trigger line set to: {line_name}')
            return

        hardware_trigger_line = line_name
        # 如果当前是硬件触发模式，立即应用设置
        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].set_trigger_source("hardware", hardware_trigger_line)
                if 0 != ret:
                    print_text('camera' + str(i) + f' set trigger line to {line_name} fail! ret = ' + ToHexStr(ret))
                else:
                    print_text('camera' + str(i) + f' trigger line set to: {line_name}')

    # 设置硬件触发极性
    def set_hardware_trigger_activation(activation):
        global hardware_trigger_activation
        global obj_cam_operation
        global b_is_open
        global b_is_software_trigger

        if not b_is_open or b_is_software_trigger:
            hardware_trigger_activation = activation
            print_text(f'Hardware trigger activation set to: {activation}')
            return

        hardware_trigger_activation = activation
        # 如果当前是硬件触发模式，立即应用设置
        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].set_trigger_activation(hardware_trigger_activation)
                if 0 != ret:
                    print_text('camera' + str(i) + f' set trigger activation to {activation} fail! ret = ' + ToHexStr(ret))
                else:
                    print_text('camera' + str(i) + f' trigger activation set to: {activation}')

    # ch: 初始化app, 绑定控件与函数 | en: Init app, bind ui and api
    app = QApplication(sys.argv)
    mainWindow = QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(mainWindow)
    ui.pushButton_enum.clicked.connect(enum_devices)
    ui.pushButton_open.clicked.connect(open_devices)
    ui.pushButton_close.clicked.connect(close_devices)
    ui.pushButton_startGrab.clicked.connect(start_grabbing)
    ui.pushButton_stopGrab.clicked.connect(stop_grabbing)
    ui.pushButton_saveImg.clicked.connect(save_bmp)
    ui.pushButton_setParams.clicked.connect(set_parameters)
    ui.checkBox_software_trigger.clicked.connect(software_trigger_check_box_clicked)
    ui.checkBox_hardware_trigger.clicked.connect(hardware_trigger_check_box_clicked)
    ui.pushButton_triggerOnce.clicked.connect(software_trigger_once)
    cam_button_group = QButtonGroup(mainWindow)
    cam_button_group.addButton(ui.checkBox_1, 0)
    cam_button_group.addButton(ui.checkBox_2, 1)
    cam_button_group.addButton(ui.checkBox_3, 2)
    cam_button_group.addButton(ui.checkBox_4, 3)

    cam_button_group.setExclusive(False)
    cam_button_group.buttonClicked.connect(cam_check_box_clicked)

    raio_button_group = QButtonGroup(mainWindow)
    raio_button_group.addButton(ui.radioButton_continuous, 0)
    raio_button_group.addButton(ui.radioButton_trigger, 1)
    raio_button_group.buttonClicked.connect(radio_button_clicked)

    win_display_handles.append(ui.widget_display1.winId())
    win_display_handles.append(ui.widget_display2.winId())
    win_display_handles.append(ui.widget_display3.winId())
    win_display_handles.append(ui.widget_display4.winId())

    mainWindow.show()
    enum_devices()
    enable_ui_controls()

    app.exec_()

    close_devices()

    # ch:反初始化SDK | en: finalize SDK
    MvCamera.MV_CC_Finalize()

    sys.exit()
