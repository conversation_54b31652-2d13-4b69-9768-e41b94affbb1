# -*- coding: utf-8 -*-
import sys
import time
import os
import threading
from queue import Queue
import numpy as np
import cv2

from PyQt5.QtWidgets import *
from PyQt5.QtGui import QTextCursor, QKeySequence
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from CamOperation_class import CameraOperation
from MvCameraControl_class import *
from MvErrorDefine_const import *
from CameraParams_header import *
from PyUIMultipleCameras import Ui_MainWindow
import ctypes

# 立体视觉处理类 - 基于ceshiliti代码的立体匹配方法，增强图像质量优化
# 标定参数: 2448x2048分辨率, 基线100.88mm, 焦距~2348px
# 立体匹配方法已与ceshiliti代码保持一致，并添加了多种图像质量优化算法
class StereoVisionProcessor(QObject):
    # 定义信号用于更新UI
    depth_result_signal = pyqtSignal(np.ndarray, np.ndarray, np.ndarray)  # disparity, depth, depth_color

    def __init__(self):
        super().__init__()
        self.calibration_params = None
        self.rectification_maps = None
        self.Q_matrix = None
        self.calibration_available = False

        # 立体匹配器
        self.stereo_matcher = self.create_stereo_matcher()

        # WLS滤波器（用于视差图后处理）
        self.wls_filter = None
        self.right_matcher = None
        self.init_wls_filter()

        # 图像队列用于同步
        self.left_image_queue = Queue(maxsize=2)
        self.right_image_queue = Queue(maxsize=2)

        # 处理标志
        self.processing = False
        self.process_thread = None

        # 存储最新的深度图
        self.latest_depth = None
        self.latest_disparity = None
        self.depth_lock = threading.Lock()

        # 图像保存控制参数
        self.save_input_images = True      # 是否保存原始输入图像
        self.save_rectified_images = True  # 是否保存校正后图像
        self.save_comparison_images = True # 是否保存对比图像
        self.image_save_interval = 5       # 图像保存间隔（秒）
        self.last_save_time = 0           # 上次保存时间

        # 图像质量优化参数
        self.enable_disparity_filtering = True    # 启用视差图滤波
        self.enable_depth_hole_filling = True     # 启用深度图孔洞填充
        self.enable_point_cloud_filtering = True  # 启用点云滤波
        self.enable_point_cloud_smoothing = True  # 启用点云平滑
        self.enable_surface_reconstruction = False # 启用表面重建（计算密集）

        # 尝试初始化标定参数
        try:
            self.init_calibration_params()
        except Exception as e:
            print(f"Warning: Calibration initialization failed: {e}")
            print("Using simplified stereo processing without calibration")
            self.calibration_available = False

        # 尝试加载配置文件
        self.load_config_from_file()

    def create_stereo_matcher(self):
        """创建立体匹配器 - 基于ceshiliti代码的方法，优化参数"""
        # 使用与ceshiliti代码相同的参数设置，但进行了优化
        stereo = cv2.StereoSGBM_create(
            minDisparity=0,
            numDisparities=96,  # 增加视差范围
            blockSize=7,  # 增加块大小
            P1=8 * 3 * 7 ** 2,
            P2=32 * 3 * 7 ** 2,
            disp12MaxDiff=1,
            uniquenessRatio=10,
            speckleWindowSize=100,
            speckleRange=32,
            preFilterCap=63,
            mode=cv2.STEREO_SGBM_MODE_SGBM_3WAY
        )
        return stereo

    def init_wls_filter(self):
        """初始化WLS滤波器用于视差图后处理"""
        try:
            # 创建WLS滤波器
            self.wls_filter = cv2.ximgproc.createDisparityWLSFilter(self.stereo_matcher)

            # 创建右视图匹配器
            self.right_matcher = cv2.ximgproc.createRightMatcher(self.stereo_matcher)

            # 设置WLS滤波器参数
            self.wls_filter.setLambda(8000.0)  # 平滑参数
            self.wls_filter.setSigmaColor(1.5)  # 颜色敏感度

            print("WLS滤波器初始化成功")
        except Exception as e:
            print(f"WLS滤波器初始化失败: {e}")
            print("将使用基础的视差图滤波方法")
            self.wls_filter = None
            self.right_matcher = None

    def init_calibration_params(self):
        """初始化相机标定参数"""
        try:
            print("Initializing stereo calibration parameters...")

            # 尝试从JSON文件加载真实标定参数
            try:
                import json
                with open('stereo_calibration.json', 'r') as f:
                    calib_data = json.load(f)

                print("Loading calibration parameters from stereo_calibration.json")

                # 加载真实的标定参数
                self.camera_matrix_left = np.array(calib_data['camera_matrix_left'], dtype=np.float64)
                self.camera_matrix_right = np.array(calib_data['camera_matrix_right'], dtype=np.float64)

                # 畸变系数
                self.dist_coeffs_left = np.array(calib_data['dist_coeffs_left'], dtype=np.float64)
                self.dist_coeffs_right = np.array(calib_data['dist_coeffs_right'], dtype=np.float64)

                # 立体标定参数
                self.R = np.array(calib_data['R'], dtype=np.float64)
                self.T = np.array(calib_data['T'], dtype=np.float64)

                # 图像尺寸
                self.image_size = tuple(calib_data['image_size'])  # (2448, 2048)
                self.baseline_mm = calib_data['baseline_mm']

                print(f"Loaded calibration: Image size {self.image_size}, Baseline {self.baseline_mm:.2f}mm")

            except (FileNotFoundError, KeyError, json.JSONDecodeError) as e:
                print(f"Failed to load calibration file: {e}")
                print("Using provided calibration parameters...")

                # 使用您提供的真实标定参数
                self.camera_matrix_left = np.array([
                    [2349.599303017811, 0.0, 1221.0886985822297],
                    [0.0, 2347.04087849075, 1021.2297950652342],
                    [0.0, 0.0, 1.0]
                ], dtype=np.float64)

                self.camera_matrix_right = np.array([
                    [2347.7080632127045, 0.0, 1219.3168735048296],
                    [0.0, 2347.528871737054, 1010.4282529230558],
                    [0.0, 0.0, 1.0]
                ], dtype=np.float64)

                # 畸变系数 - 转换为正确的形状
                self.dist_coeffs_left = np.array([
                    [-0.0677072434895743, 0.16840134514589222, -0.00013311325437381048,
                     -0.0010946605867930416, -0.19743756744235746]
                ], dtype=np.float64).reshape(5, 1)

                self.dist_coeffs_right = np.array([
                    [-0.07691265481784593, 0.22703604995053306, 0.00015959041360151294,
                     -0.0011580170802655745, -0.3538743014783903]
                ], dtype=np.float64).reshape(5, 1)

                # 立体标定参数
                self.R = np.array([
                    [0.9998155478060632, -0.0008436404567199599, -0.01918746038540903],
                    [0.0008172502236645439, 0.9999987094440285, -0.0013831891951225166],
                    [0.019188602537281753, 0.0013672531065493177, 0.9998149469534894]
                ], dtype=np.float64)

                self.T = np.array([
                    [-100.87040766250446],
                    [0.06079718879422688],
                    [-1.3284405860235702]
                ], dtype=np.float64)

                # 图像尺寸和基线距离
                self.image_size = (2448, 2048)  # 使用您提供的真实图像尺寸
                self.baseline_mm = 100.87917323555243  # 使用您提供的真实基线距离

                print(f"Using provided calibration: Image size {self.image_size}, Baseline {self.baseline_mm:.2f}mm")

            # 尝试计算校正映射，如果失败则跳过
            try:
                self.compute_rectification_maps()
                self.calibration_available = True
                print("Calibration parameters initialized successfully")
            except Exception as calib_error:
                print(f"Calibration mapping failed: {calib_error}")
                print("Will use simplified stereo processing")
                self.calibration_available = False
                # 设置基于真实标定参数的简化Q矩阵
                # 使用真实的主点坐标和焦距
                cx = 1220.0  # 主点x坐标的平均值
                cy = 1015.0  # 主点y坐标的平均值
                fx = 2348.0  # 焦距的平均值
                baseline = 100.88  # 真实基线距离(mm)

                self.Q_matrix = np.array([
                    [1.0, 0.0, 0.0, -cx],
                    [0.0, 1.0, 0.0, -cy],
                    [0.0, 0.0, 0.0, fx],
                    [0.0, 0.0, -1.0/baseline, 0.0]
                ], dtype=np.float64)

        except Exception as e:
            print(f"Error initializing calibration parameters: {e}")
            # 设置默认值避免崩溃
            self.camera_matrix_left = None
            self.camera_matrix_right = None
            self.dist_coeffs_left = None
            self.dist_coeffs_right = None
            self.R = None
            self.T = None
            self.Q_matrix = None
            self.calibration_available = False

    def compute_rectification_maps(self):
        """计算校正映射"""
        try:
            # 检查参数是否有效
            if (self.camera_matrix_left is None or self.camera_matrix_right is None or
                self.dist_coeffs_left is None or self.dist_coeffs_right is None or
                self.R is None or self.T is None):
                print("Calibration parameters not properly initialized")
                return

            # 使用真实的图像尺寸
            image_size = getattr(self, 'image_size', (640, 480))

            # 确保所有参数都是正确的数据类型
            R1, R2, P1, P2, self.Q_matrix, _, _ = cv2.stereoRectify(
                self.camera_matrix_left.astype(np.float64),
                self.dist_coeffs_left.astype(np.float64),
                self.camera_matrix_right.astype(np.float64),
                self.dist_coeffs_right.astype(np.float64),
                image_size,
                self.R.astype(np.float64),
                self.T.astype(np.float64)
            )

            self.map1_left, self.map2_left = cv2.initUndistortRectifyMap(
                self.camera_matrix_left.astype(np.float64),
                self.dist_coeffs_left.astype(np.float64),
                R1, P1, image_size, cv2.CV_16SC2
            )

            self.map1_right, self.map2_right = cv2.initUndistortRectifyMap(
                self.camera_matrix_right.astype(np.float64),
                self.dist_coeffs_right.astype(np.float64),
                R2, P2, image_size, cv2.CV_16SC2
            )

        except Exception as e:
            print(f"Error computing rectification maps: {e}")
            # 设置为None避免后续错误
            self.map1_left = None
            self.map2_left = None
            self.map1_right = None
            self.map2_right = None
            self.Q_matrix = None

    def rectify_images(self, left_img, right_img):
        """校正图像"""
        try:
            # 如果没有标定参数，直接返回原图像
            if not self.calibration_available:
                return left_img, right_img

            # 检查映射是否有效
            if (self.map1_left is None or self.map2_left is None or
                self.map1_right is None or self.map2_right is None):
                print("Rectification maps not available, returning original images")
                return left_img, right_img

            left_rectified = cv2.remap(left_img, self.map1_left, self.map2_left, cv2.INTER_LINEAR)
            right_rectified = cv2.remap(right_img, self.map1_right, self.map2_right, cv2.INTER_LINEAR)
            return left_rectified, right_rectified
        except Exception as e:
            print(f"Error rectifying images: {e}")
            return left_img, right_img

    def compute_disparity(self, left_img, right_img):
        """计算视差图 - 基于ceshiliti代码的方法，增加滤波优化"""
        # 转换为灰度图
        if len(left_img.shape) == 3:
            left_gray = cv2.cvtColor(left_img, cv2.COLOR_BGR2GRAY)
            right_gray = cv2.cvtColor(right_img, cv2.COLOR_BGR2GRAY)
        else:
            left_gray = left_img
            right_gray = right_img

        # 校正图像
        left_rect, right_rect = self.rectify_images(left_gray, right_gray)

        # 计算左视差图
        disparity_left = self.stereo_matcher.compute(left_rect, right_rect)

        # 如果启用了视差图滤波
        if self.enable_disparity_filtering:
            disparity = self.filter_disparity(disparity_left, left_rect, right_rect)
        else:
            # 转换为浮点数并归一化
            disparity = disparity_left.astype(np.float32) / 16.0

        return disparity, left_rect, right_rect

    def filter_disparity(self, disparity_left, left_img, right_img):
        """视差图滤波 - 多种滤波方法组合"""
        try:
            # 转换为浮点数
            disparity = disparity_left.astype(np.float32) / 16.0

            # 方法1: WLS滤波（如果可用）
            if self.wls_filter is not None and self.right_matcher is not None:
                try:
                    # 计算右视差图
                    disparity_right = self.right_matcher.compute(right_img, left_img)

                    # 应用WLS滤波
                    disparity = self.wls_filter.filter(disparity_left, left_img, None, disparity_right)
                    disparity = disparity.astype(np.float32) / 16.0
                    print("应用WLS滤波")
                except Exception as e:
                    print(f"WLS滤波失败，使用备用方法: {e}")
                    disparity = self.apply_basic_disparity_filters(disparity)
            else:
                # 方法2: 基础滤波方法组合
                disparity = self.apply_basic_disparity_filters(disparity)

            return disparity

        except Exception as e:
            print(f"视差图滤波错误: {e}")
            # 返回基础处理结果
            return disparity_left.astype(np.float32) / 16.0

    def apply_basic_disparity_filters(self, disparity):
        """应用基础视差图滤波方法"""
        try:
            # 1. 中值滤波去除噪声
            disparity_filtered = cv2.medianBlur(disparity.astype(np.uint8), 5)
            disparity_filtered = disparity_filtered.astype(np.float32)

            # 2. 双边滤波保持边缘
            disparity_filtered = cv2.bilateralFilter(
                disparity_filtered.astype(np.uint8), 9, 75, 75
            ).astype(np.float32)

            # 3. 形态学操作去除小的噪声点
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            disparity_filtered = cv2.morphologyEx(
                disparity_filtered.astype(np.uint8), cv2.MORPH_CLOSE, kernel
            ).astype(np.float32)

            print("应用基础视差图滤波（中值+双边+形态学）")
            return disparity_filtered

        except Exception as e:
            print(f"基础滤波错误: {e}")
            return disparity

    def disparity_to_depth(self, disparity):
        """将视差转换为深度 - 基于ceshiliti代码的方法，增加孔洞填充"""
        if self.Q_matrix is None:
            return None

        # 使用Q矩阵重投影到3D
        points_3d = cv2.reprojectImageTo3D(disparity, self.Q_matrix)

        # 提取深度信息（Z坐标）
        depth = points_3d[:, :, 2]

        # 过滤无效深度值
        depth[depth <= 0] = 0
        depth[depth > 5000] = 0  # 限制最大深度为5米

        # 如果启用了深度图孔洞填充
        if self.enable_depth_hole_filling:
            depth = self.fill_depth_holes(depth)

        return depth

    def fill_depth_holes(self, depth):
        """深度图孔洞填充"""
        try:
            # 创建掩码：标识有效深度值的区域
            valid_mask = (depth > 0).astype(np.uint8)

            # 方法1: 使用图像修复（inpainting）填充孔洞
            depth_normalized = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)

            # 创建孔洞掩码（无效区域）
            hole_mask = (1 - valid_mask) * 255
            hole_mask = hole_mask.astype(np.uint8)

            # 使用快速行进法（Fast Marching Method）进行图像修复
            depth_inpainted = cv2.inpaint(depth_normalized, hole_mask, 3, cv2.INPAINT_TELEA)

            # 转换回原始深度范围
            depth_filled = depth_inpainted.astype(np.float32)
            depth_filled = depth_filled * (depth.max() / 255.0) if depth.max() > 0 else depth_filled

            # 方法2: 邻域填充作为补充
            depth_filled = self.neighbor_fill_holes(depth_filled, valid_mask)

            print("应用深度图孔洞填充（图像修复+邻域填充）")
            return depth_filled

        except Exception as e:
            print(f"深度图孔洞填充错误: {e}")
            return depth

    def neighbor_fill_holes(self, depth, valid_mask):
        """邻域填充方法"""
        try:
            # 使用形态学操作扩展有效区域
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))

            # 多次迭代填充
            for _ in range(3):
                # 膨胀有效掩码
                expanded_mask = cv2.dilate(valid_mask, kernel, iterations=1)

                # 找到新扩展的区域
                new_region = expanded_mask - valid_mask

                if np.sum(new_region) == 0:
                    break

                # 对新区域进行均值滤波填充
                depth_blurred = cv2.GaussianBlur(depth, (5, 5), 0)
                depth[new_region > 0] = depth_blurred[new_region > 0]

                # 更新有效掩码
                valid_mask = expanded_mask

            return depth

        except Exception as e:
            print(f"邻域填充错误: {e}")
            return depth

    def create_depth_colormap(self, depth):
        """创建深度图的彩色可视化 - 基于ceshiliti代码的方法"""
        # 归一化深度值到0-255
        depth_norm = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX)
        depth_norm = depth_norm.astype(np.uint8)

        # 应用彩色映射
        depth_color = cv2.applyColorMap(depth_norm, cv2.COLORMAP_JET)

        # 将无效区域设为黑色
        mask = depth <= 0
        depth_color[mask] = [0, 0, 0]

        return depth_color

    def compute_depth(self, disparity):
        """从视差计算深度 - 根据真实标定参数优化"""
        try:
            if self.Q_matrix is None:
                print("Q matrix not available for depth computation")
                return None, None

            # 重投影到3D
            points_3d = cv2.reprojectImageTo3D(disparity, self.Q_matrix)
            depth = points_3d[:, :, 2]

            # 根据真实标定参数过滤深度值
            # 基线: 100.88mm, 焦距: ~2348像素
            # 理论最小深度 = (基线 * 焦距) / 最大视差
            # 理论最大深度 = (基线 * 焦距) / 最小视差
            depth[depth <= 0] = 0
            depth[depth < 1500] = 0    # 最小深度1.5米 (考虑实际应用场景)
            depth[depth > 8000] = 0    # 最大深度8米 (根据视差精度限制)

            # 过滤异常值 (深度变化过大的点)
            depth_median = np.median(depth[depth > 0])
            if depth_median > 0:
                depth_std = np.std(depth[depth > 0])
                depth[np.abs(depth - depth_median) > 3 * depth_std] = 0

            return depth, points_3d
        except Exception as e:
            print(f"Error computing depth: {e}")
            return None, None

    def add_image_pair(self, left_img, right_img):
        """添加图像对到处理队列"""
        try:
            if not self.left_image_queue.full():
                self.left_image_queue.put(left_img, block=False)
            if not self.right_image_queue.full():
                self.right_image_queue.put(right_img, block=False)
        except:
            pass

    def start_processing(self):
        """启动处理线程"""
        if not self.processing:
            self.processing = True
            self.process_thread = threading.Thread(target=self.processing_loop)
            self.process_thread.daemon = True
            self.process_thread.start()

    def stop_processing(self):
        """停止处理"""
        self.processing = False
        if self.process_thread:
            self.process_thread.join()

    def processing_loop(self):
        """处理循环"""
        while self.processing:
            try:
                if not self.left_image_queue.empty() and not self.right_image_queue.empty():
                    left_img = self.left_image_queue.get()
                    right_img = self.right_image_queue.get()
                    self.process_stereo_pair(left_img, right_img)
                else:
                    time.sleep(0.01)
            except Exception as e:
                print(f"Stereo processing error: {e}")
                time.sleep(0.1)

    def process_stereo_pair(self, left_img, right_img):
        """处理立体图像对 - 基于ceshiliti代码的方法，增强图像质量优化"""
        try:
            current_time = time.time()

            # 控制图像保存频率，避免过于频繁保存
            should_save_images = (current_time - self.last_save_time) >= self.image_save_interval

            if should_save_images:
                # 保存原始立体图像对（立体匹配前）
                if self.save_input_images:
                    self.save_stereo_input_images(left_img, right_img)

                self.last_save_time = current_time

            print("开始处理立体图像对（增强质量优化）...")

            # 计算视差（包含图像校正和滤波）
            disparity, left_rect, right_rect = self.compute_disparity(left_img, right_img)
            print(f"视差图计算完成，尺寸: {disparity.shape}")

            if should_save_images:
                # 保存校正后的立体图像对
                if self.save_rectified_images:
                    self.save_rectified_images(left_rect, right_rect)

            # 计算深度（包含孔洞填充）
            depth = self.disparity_to_depth(disparity)

            if depth is not None:
                print(f"深度图计算完成，深度范围: {np.min(depth):.2f} - {np.max(depth):.2f}")

                # 创建深度彩色图
                depth_color = self.create_depth_colormap(depth)

                # 更新最新结果
                with self.depth_lock:
                    self.latest_disparity = disparity.copy()
                    self.latest_depth = depth.copy()

                # 保存深度图和视差图到文件
                self.save_depth_image(depth_color)

                # 生成并保存点云（包含滤波和优化）
                points_3d = cv2.reprojectImageTo3D(disparity, self.Q_matrix) if self.Q_matrix is not None else None
                if points_3d is not None:
                    # 使用增强的点云保存方法
                    self.save_enhanced_point_cloud(points_3d, left_rect)

                # 发射信号更新UI
                self.depth_result_signal.emit(disparity, depth, depth_color)

                # 返回处理结果
                return {
                    'disparity': disparity,
                    'depth': depth,
                    'depth_color': depth_color,
                    'points_3d': points_3d,
                    'left_rectified': left_rect,
                    'right_rectified': right_rect
                }

        except Exception as e:
            print(f"立体处理错误: {e}")

    def save_enhanced_point_cloud(self, points_3d, color_image=None):
        """保存增强的点云数据，包含所有优化步骤"""
        try:
            save_dir = "enhanced_point_clouds"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 提取有效的3D点
            height, width = points_3d.shape[:2]
            total_pixels = height * width

            # 过滤有效点（深度值在1.5m-8m范围内）
            valid_mask = (points_3d[:, :, 2] >= 1500) & (points_3d[:, :, 2] <= 8000)
            valid_count = np.sum(valid_mask)

            # 如果严格范围内没有有效点，尝试更宽松的范围
            if not np.any(valid_mask):
                print("严格深度范围内无有效点，尝试宽松范围...")
                valid_mask = (points_3d[:, :, 2] >= 500) & (points_3d[:, :, 2] <= 15000)
                valid_count = np.sum(valid_mask)

                if not np.any(valid_mask):
                    print("No valid 3D points found for enhanced point cloud generation")
                    print(f"深度统计: min={points_3d[:,:,2].min():.1f}, max={points_3d[:,:,2].max():.1f}")
                    return
                else:
                    print(f"使用宽松深度范围: 500-15000mm，找到 {valid_count} 个有效点")

            # 提取有效的XYZ坐标
            valid_points = points_3d[valid_mask]

            # 提取对应的颜色信息
            if color_image is not None and color_image.shape[:2] == (height, width):
                if len(color_image.shape) == 3:
                    color_rgb = cv2.cvtColor(color_image, cv2.COLOR_BGR2RGB)
                    valid_colors = color_rgb[valid_mask]
                else:
                    gray_rgb = np.stack([color_image[valid_mask]] * 3, axis=1)
                    valid_colors = gray_rgb
            else:
                valid_colors = np.full((len(valid_points), 3), 255, dtype=np.uint8)

            print(f"=== 增强点云处理开始 ===")
            print(f"原始有效点数: {len(valid_points):,}")

            # 应用点云滤波和优化
            if self.enable_point_cloud_filtering:
                valid_points, valid_colors = self.filter_point_cloud(valid_points, valid_colors)

            # 应用点云平滑
            if self.enable_point_cloud_smoothing:
                valid_points = self.smooth_point_cloud(valid_points)

            # 点云分割（可选）
            segments = self.segment_point_cloud(valid_points, valid_colors, method='plane')

            # 保存主要点云
            self._save_ply(valid_points, valid_colors, save_dir, timestamp)

            # 保存分割后的点云段
            for i, (seg_points, seg_colors) in enumerate(segments):
                if len(seg_points) > 100:  # 只保存足够大的分段
                    seg_filename = f"segment_{i}_{timestamp}.ply"
                    seg_filepath = os.path.join(save_dir, seg_filename)

                    with open(seg_filepath, 'w') as f:
                        f.write("ply\n")
                        f.write("format ascii 1.0\n")
                        f.write(f"element vertex {len(seg_points)}\n")
                        f.write("property float x\n")
                        f.write("property float y\n")
                        f.write("property float z\n")
                        f.write("property uchar red\n")
                        f.write("property uchar green\n")
                        f.write("property uchar blue\n")
                        f.write("end_header\n")

                        for j in range(len(seg_points)):
                            x, y, z = seg_points[j]
                            r, g, b = seg_colors[j]
                            f.write(f"{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n")

                    print(f"分段点云已保存: {seg_filename} ({len(seg_points)} 点)")

            # 表面重建（如果启用）
            if self.enable_surface_reconstruction:
                self.perform_surface_reconstruction(valid_points, valid_colors, save_dir, timestamp)

            print(f"=== 增强点云处理完成 ===")
            print(f"最终点云数量: {len(valid_points):,}")
            print(f"分割段数: {len(segments)}")

        except Exception as e:
            print(f"Error saving enhanced point cloud: {e}")



    def set_image_save_settings(self, save_input=True, save_rectified=True, save_comparison=True, interval=5):
        """设置图像保存参数
        Args:
            save_input: 是否保存原始输入图像
            save_rectified: 是否保存校正后图像
            save_comparison: 是否保存对比图像
            interval: 保存间隔（秒）
        """
        self.save_input_images = save_input
        self.save_rectified_images = save_rectified
        self.save_comparison_images = save_comparison
        self.image_save_interval = interval
        print(f"图像保存设置已更新: 输入图像={save_input}, 校正图像={save_rectified}, 对比图像={save_comparison}, 间隔={interval}秒")

    def force_save_current_pair(self, left_img, right_img):
        """强制保存当前图像对（忽略时间间隔限制）"""
        try:
            print("强制保存当前立体图像对...")

            # 保存原始图像
            if self.save_input_images:
                self.save_stereo_input_images(left_img, right_img)

            # 校正并保存校正图像
            left_rectified, right_rectified = self.rectify_images(left_img, right_img)
            if self.save_rectified_images:
                self.save_rectified_images(left_rectified, right_rectified)

            print("强制保存完成")

        except Exception as e:
            print(f"强制保存图像对错误: {e}")

    def save_depth_image(self, depth_color):
        """保存深度图像"""
        try:
            save_dir = "depth_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            filename = f"depth_{timestamp}.png"
            filepath = os.path.join(save_dir, filename)
            cv2.imwrite(filepath, depth_color)
        except Exception as e:
            print(f"Error saving depth image: {e}")

    def save_point_cloud(self, points_3d, color_image=None, format='ply'):
        """保存点云数据 - 增强版本包含滤波和优化
        Args:
            points_3d: 3D点云数据 (H, W, 3)
            color_image: 彩色图像用于纹理 (H, W, 3)
            format: 保存格式 (仅支持 'ply')
        """
        try:
            save_dir = "point_clouds"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 提取有效的3D点
            height, width = points_3d.shape[:2]
            total_pixels = height * width

            # 过滤有效点（深度值在1.5m-8m范围内，根据真实标定参数调整）
            valid_mask = (points_3d[:, :, 2] >= 1500) & (points_3d[:, :, 2] <= 8000)
            valid_count = np.sum(valid_mask)

            # 计算点云覆盖率
            coverage_percentage = (valid_count / total_pixels) * 100

            if not np.any(valid_mask):
                print("No valid 3D points found for point cloud generation")
                return

            # 提取有效的XYZ坐标
            valid_points = points_3d[valid_mask]

            # 提取对应的颜色信息
            if color_image is not None and color_image.shape[:2] == (height, width):
                if len(color_image.shape) == 3:
                    # BGR转RGB
                    color_rgb = cv2.cvtColor(color_image, cv2.COLOR_BGR2RGB)
                    valid_colors = color_rgb[valid_mask]
                else:
                    # 灰度图转RGB
                    gray_rgb = np.stack([color_image[valid_mask]] * 3, axis=1)
                    valid_colors = gray_rgb
            else:
                # 默认白色
                valid_colors = np.full((len(valid_points), 3), 255, dtype=np.uint8)

            # 应用点云滤波和优化
            if self.enable_point_cloud_filtering:
                valid_points, valid_colors = self.filter_point_cloud(valid_points, valid_colors)

            # 应用点云平滑
            if self.enable_point_cloud_smoothing:
                valid_points = self.smooth_point_cloud(valid_points)

            # 统计处理后的深度范围
            depth_values = valid_points[:, 2]
            min_depth = np.min(depth_values)
            max_depth = np.max(depth_values)
            mean_depth = np.mean(depth_values)

            # 输出详细统计信息
            print(f"=== 点云生成统计 ===")
            print(f"图像分辨率: {width} x {height} ({total_pixels:,} 像素)")
            print(f"有效点云数量: {len(valid_points):,} 点")
            print(f"点云覆盖率: {coverage_percentage:.2f}%")
            print(f"深度范围: {min_depth:.1f}mm - {max_depth:.1f}mm (有效范围: 1500-8000mm)")
            print(f"平均深度: {mean_depth:.1f}mm")
            print(f"基线距离: {getattr(self, 'baseline_mm', 'Unknown')}mm")
            print(f"相机内参 - 左相机焦距: fx={getattr(self, 'camera_matrix_left', [[0]])[0][0]:.1f}px")
            print(f"相机内参 - 右相机焦距: fx={getattr(self, 'camera_matrix_right', [[0]])[0][0]:.1f}px")

            # 深度范围检查提示
            if min_depth < 1500 or max_depth > 8000:
                print(f"警告: 检测到超出预期范围的深度值，建议检查标定参数或场景设置")

            # 保存为PLY格式
            self._save_ply(valid_points, valid_colors, save_dir, timestamp)

        except Exception as e:
            print(f"Error saving point cloud: {e}")

    def filter_point_cloud(self, points, colors):
        """点云滤波 - 统计学滤波和半径滤波"""
        try:
            print("开始点云滤波...")

            # 1. 统计学离群点去除
            points_filtered, colors_filtered = self.statistical_outlier_removal(points, colors)

            # 2. 半径滤波
            points_filtered, colors_filtered = self.radius_outlier_removal(points_filtered, colors_filtered)

            # 3. 体素下采样（减少点云密度）
            points_filtered, colors_filtered = self.voxel_downsample(points_filtered, colors_filtered)

            print(f"点云滤波完成: {len(points)} -> {len(points_filtered)} 点")
            return points_filtered, colors_filtered

        except Exception as e:
            print(f"点云滤波错误: {e}")
            return points, colors

    def statistical_outlier_removal(self, points, colors, nb_neighbors=20, std_ratio=2.0):
        """统计学离群点去除"""
        try:
            if len(points) < nb_neighbors:
                return points, colors

            # 计算每个点到其邻近点的平均距离
            from scipy.spatial import cKDTree
            tree = cKDTree(points)

            distances = []
            for i, point in enumerate(points):
                # 找到最近的邻居点
                dists, _ = tree.query(point, k=min(nb_neighbors + 1, len(points)))
                # 排除自身（距离为0的点）
                mean_dist = np.mean(dists[1:])
                distances.append(mean_dist)

            distances = np.array(distances)

            # 计算距离的统计信息
            mean_distance = np.mean(distances)
            std_distance = np.std(distances)

            # 过滤离群点
            threshold = mean_distance + std_ratio * std_distance
            inlier_mask = distances < threshold

            return points[inlier_mask], colors[inlier_mask]

        except Exception as e:
            print(f"统计学滤波错误: {e}")
            return points, colors

    def radius_outlier_removal(self, points, colors, radius=50.0, min_neighbors=5):
        """半径滤波"""
        try:
            if len(points) < min_neighbors:
                return points, colors

            from scipy.spatial import cKDTree
            tree = cKDTree(points)

            # 找到每个点在指定半径内的邻居数量
            inlier_mask = []
            for point in points:
                neighbors = tree.query_ball_point(point, radius)
                # 排除自身
                neighbor_count = len(neighbors) - 1
                inlier_mask.append(neighbor_count >= min_neighbors)

            inlier_mask = np.array(inlier_mask)
            return points[inlier_mask], colors[inlier_mask]

        except Exception as e:
            print(f"半径滤波错误: {e}")
            return points, colors

    def voxel_downsample(self, points, colors, voxel_size=10.0):
        """体素下采样"""
        try:
            if len(points) == 0:
                return points, colors

            # 将点云坐标量化到体素网格
            voxel_coords = np.floor(points / voxel_size).astype(int)

            # 使用字典存储每个体素中的点
            voxel_dict = {}
            for i, voxel_coord in enumerate(voxel_coords):
                key = tuple(voxel_coord)
                if key not in voxel_dict:
                    voxel_dict[key] = []
                voxel_dict[key].append(i)

            # 对每个体素中的点进行平均
            downsampled_points = []
            downsampled_colors = []

            for indices in voxel_dict.values():
                # 计算体素内点的平均位置
                avg_point = np.mean(points[indices], axis=0)
                avg_color = np.mean(colors[indices], axis=0).astype(np.uint8)

                downsampled_points.append(avg_point)
                downsampled_colors.append(avg_color)

            return np.array(downsampled_points), np.array(downsampled_colors)

        except Exception as e:
            print(f"体素下采样错误: {e}")
            return points, colors

    def smooth_point_cloud(self, points, k_neighbors=10, iterations=1):
        """点云平滑"""
        try:
            if len(points) < k_neighbors:
                return points

            from scipy.spatial import cKDTree

            smoothed_points = points.copy()

            for _ in range(iterations):
                tree = cKDTree(smoothed_points)

                new_points = []
                for point in smoothed_points:
                    # 找到k个最近邻
                    distances, indices = tree.query(point, k=min(k_neighbors + 1, len(smoothed_points)))

                    # 排除自身，计算邻居点的加权平均
                    neighbor_points = smoothed_points[indices[1:]]
                    neighbor_distances = distances[1:]

                    # 使用距离的倒数作为权重
                    weights = 1.0 / (neighbor_distances + 1e-8)
                    weights = weights / np.sum(weights)

                    # 计算加权平均位置
                    smoothed_point = np.average(neighbor_points, axis=0, weights=weights)
                    new_points.append(smoothed_point)

                smoothed_points = np.array(new_points)

            print(f"点云平滑完成 ({iterations} 次迭代)")
            return smoothed_points

        except Exception as e:
            print(f"点云平滑错误: {e}")
            return points

    def save_stereo_input_images(self, left_img, right_img):
        """保存立体匹配前的原始图像对"""
        try:
            save_dir = "stereo_input_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 保存左图像
            left_filename = f"stereo_left_input_{timestamp}.png"
            left_filepath = os.path.join(save_dir, left_filename)
            cv2.imwrite(left_filepath, left_img)

            # 保存右图像
            right_filename = f"stereo_right_input_{timestamp}.png"
            right_filepath = os.path.join(save_dir, right_filename)
            cv2.imwrite(right_filepath, right_img)

            print(f"立体输入图像已保存: {left_filename}, {right_filename}")

            # 创建并保存拼接图像用于对比显示
            self.save_stereo_comparison_image(left_img, right_img, save_dir, timestamp, "input")

        except Exception as e:
            print(f"保存立体输入图像错误: {e}")

    def save_rectified_images(self, left_rectified, right_rectified):
        """保存校正后的立体图像对"""
        try:
            save_dir = "stereo_rectified_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 保存左校正图像
            left_filename = f"stereo_left_rectified_{timestamp}.png"
            left_filepath = os.path.join(save_dir, left_filename)
            cv2.imwrite(left_filepath, left_rectified)

            # 保存右校正图像
            right_filename = f"stereo_right_rectified_{timestamp}.png"
            right_filepath = os.path.join(save_dir, right_filename)
            cv2.imwrite(right_filepath, right_rectified)

            print(f"立体校正图像已保存: {left_filename}, {right_filename}")

            # 创建并保存拼接图像用于对比显示
            self.save_stereo_comparison_image(left_rectified, right_rectified, save_dir, timestamp, "rectified")

        except Exception as e:
            print(f"保存立体校正图像错误: {e}")

    def save_stereo_comparison_image(self, left_img, right_img, save_dir, timestamp, image_type):
        """创建并保存立体图像对比图（左右拼接+水平线）"""
        try:
            # 确保两个图像尺寸一致
            if left_img.shape != right_img.shape:
                print(f"警告: 左右图像尺寸不一致 - 左: {left_img.shape}, 右: {right_img.shape}")
                # 调整到相同尺寸
                min_height = min(left_img.shape[0], right_img.shape[0])
                min_width = min(left_img.shape[1], right_img.shape[1])
                left_img = left_img[:min_height, :min_width]
                right_img = right_img[:min_height, :min_width]

            # 水平拼接左右图像
            stereo_pair = np.hstack((left_img, right_img))

            # 添加水平参考线（用于检查校正效果）
            height = stereo_pair.shape[0]
            width = stereo_pair.shape[1]

            # 在图像上绘制水平参考线
            line_color = (0, 255, 0) if len(stereo_pair.shape) == 3 else 255  # 绿色或白色
            line_thickness = 2

            # 绘制多条水平线
            for y in range(height // 10, height, height // 10):
                cv2.line(stereo_pair, (0, y), (width, y), line_color, line_thickness)

            # 在中间添加分割线
            middle_x = width // 2
            cv2.line(stereo_pair, (middle_x, 0), (middle_x, height), (0, 0, 255), 3)  # 红色分割线

            # 添加文字标注
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 2.0
            font_thickness = 3
            text_color = (255, 255, 255) if len(stereo_pair.shape) == 3 else 255

            # 左图标注
            cv2.putText(stereo_pair, "LEFT", (50, 80), font, font_scale, text_color, font_thickness)
            # 右图标注
            cv2.putText(stereo_pair, "RIGHT", (middle_x + 50, 80), font, font_scale, text_color, font_thickness)

            # 保存对比图像
            comparison_filename = f"stereo_comparison_{image_type}_{timestamp}.png"
            comparison_filepath = os.path.join(save_dir, comparison_filename)
            cv2.imwrite(comparison_filepath, stereo_pair)

            print(f"立体对比图像已保存: {comparison_filename}")

        except Exception as e:
            print(f"保存立体对比图像错误: {e}")

    def _save_ply(self, points, colors, save_dir, timestamp):
        """保存PLY格式点云"""
        filename = f"pointcloud_{timestamp}.ply"
        filepath = os.path.join(save_dir, filename)

        with open(filepath, 'w') as f:
            # PLY文件头
            f.write("ply\n")
            f.write("format ascii 1.0\n")
            f.write(f"element vertex {len(points)}\n")
            f.write("property float x\n")
            f.write("property float y\n")
            f.write("property float z\n")
            f.write("property uchar red\n")
            f.write("property uchar green\n")
            f.write("property uchar blue\n")
            f.write("end_header\n")

            # 写入点云数据
            for i in range(len(points)):
                x, y, z = points[i]
                r, g, b = colors[i]
                f.write(f"{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n")

        print(f"Point cloud saved as PLY: {filepath}")

    def set_quality_optimization_settings(self,
                                        enable_disparity_filtering=True,
                                        enable_depth_hole_filling=True,
                                        enable_point_cloud_filtering=True,
                                        enable_point_cloud_smoothing=True,
                                        enable_surface_reconstruction=False):
        """设置图像质量优化参数
        Args:
            enable_disparity_filtering: 启用视差图滤波
            enable_depth_hole_filling: 启用深度图孔洞填充
            enable_point_cloud_filtering: 启用点云滤波
            enable_point_cloud_smoothing: 启用点云平滑
            enable_surface_reconstruction: 启用表面重建（计算密集）
        """
        self.enable_disparity_filtering = enable_disparity_filtering
        self.enable_depth_hole_filling = enable_depth_hole_filling
        self.enable_point_cloud_filtering = enable_point_cloud_filtering
        self.enable_point_cloud_smoothing = enable_point_cloud_smoothing
        self.enable_surface_reconstruction = enable_surface_reconstruction

        print("=== 图像质量优化设置 ===")
        print(f"视差图滤波: {'启用' if enable_disparity_filtering else '禁用'}")
        print(f"深度图孔洞填充: {'启用' if enable_depth_hole_filling else '禁用'}")
        print(f"点云滤波: {'启用' if enable_point_cloud_filtering else '禁用'}")
        print(f"点云平滑: {'启用' if enable_point_cloud_smoothing else '禁用'}")
        print(f"表面重建: {'启用' if enable_surface_reconstruction else '禁用'}")

    def perform_surface_reconstruction(self, points, colors, save_dir, timestamp):
        """表面重建 - 泊松重建"""
        try:
            if not self.enable_surface_reconstruction:
                return

            print("开始表面重建...")

            # 这里可以集成Open3D或其他库进行表面重建
            # 由于Open3D可能不是标准依赖，我们提供一个简化的实现

            # 简化的表面重建：生成三角网格
            mesh_vertices, mesh_faces = self.simple_mesh_reconstruction(points)

            if mesh_vertices is not None and mesh_faces is not None:
                # 保存网格为OBJ格式
                self.save_mesh_obj(mesh_vertices, mesh_faces, colors, save_dir, timestamp)
                print("表面重建完成")
            else:
                print("表面重建失败")

        except Exception as e:
            print(f"表面重建错误: {e}")

    def simple_mesh_reconstruction(self, points):
        """简化的网格重建"""
        try:
            # 这是一个简化的实现，实际应用中建议使用Open3D的泊松重建
            # 这里我们创建一个基于Delaunay三角剖分的简单网格

            if len(points) < 3:
                return None, None

            # 投影到XY平面进行2D三角剖分
            from scipy.spatial import Delaunay

            points_2d = points[:, :2]  # 只使用X,Y坐标
            tri = Delaunay(points_2d)

            # 过滤掉过大的三角形（可能是噪声）
            valid_faces = []
            max_edge_length = 100.0  # 最大边长阈值

            for simplex in tri.simplices:
                # 计算三角形的边长
                p1, p2, p3 = points[simplex]
                edge1 = np.linalg.norm(p2 - p1)
                edge2 = np.linalg.norm(p3 - p2)
                edge3 = np.linalg.norm(p1 - p3)

                if max(edge1, edge2, edge3) < max_edge_length:
                    valid_faces.append(simplex)

            if len(valid_faces) > 0:
                return points, np.array(valid_faces)
            else:
                return None, None

        except Exception as e:
            print(f"简化网格重建错误: {e}")
            return None, None

    def save_mesh_obj(self, vertices, faces, colors, save_dir, timestamp):
        """保存网格为OBJ格式"""
        try:
            filename = f"mesh_{timestamp}.obj"
            filepath = os.path.join(save_dir, filename)

            with open(filepath, 'w') as f:
                # 写入顶点
                for i, vertex in enumerate(vertices):
                    if i < len(colors):
                        # 归一化颜色值到0-1范围
                        r, g, b = colors[i] / 255.0
                        f.write(f"v {vertex[0]:.6f} {vertex[1]:.6f} {vertex[2]:.6f} {r:.3f} {g:.3f} {b:.3f}\n")
                    else:
                        f.write(f"v {vertex[0]:.6f} {vertex[1]:.6f} {vertex[2]:.6f}\n")

                # 写入面（OBJ格式索引从1开始）
                for face in faces:
                    f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")

            print(f"网格已保存为OBJ: {filename}")

        except Exception as e:
            print(f"保存OBJ网格错误: {e}")

    def segment_point_cloud(self, points, colors, method='plane'):
        """点云分割"""
        try:
            if method == 'plane':
                return self.plane_segmentation(points, colors)
            elif method == 'cluster':
                return self.cluster_segmentation(points, colors)
            else:
                return [(points, colors)]  # 返回原始点云

        except Exception as e:
            print(f"点云分割错误: {e}")
            return [(points, colors)]

    def plane_segmentation(self, points, colors, distance_threshold=20.0):
        """平面分割"""
        try:
            # 简化的平面分割实现
            # 实际应用中建议使用RANSAC算法

            if len(points) < 3:
                return [(points, colors)]

            # 计算点云的主平面（使用PCA）
            centered_points = points - np.mean(points, axis=0)
            _, _, vh = np.linalg.svd(centered_points)
            normal = vh[-1]  # 最小特征值对应的特征向量

            # 计算每个点到平面的距离
            plane_point = np.mean(points, axis=0)
            distances = np.abs(np.dot(points - plane_point, normal))

            # 分割为平面内和平面外的点
            plane_mask = distances < distance_threshold

            plane_points = points[plane_mask]
            plane_colors = colors[plane_mask]

            non_plane_points = points[~plane_mask]
            non_plane_colors = colors[~plane_mask]

            segments = []
            if len(plane_points) > 0:
                segments.append((plane_points, plane_colors))
            if len(non_plane_points) > 0:
                segments.append((non_plane_points, non_plane_colors))

            print(f"平面分割完成: {len(segments)} 个分段")
            return segments

        except Exception as e:
            print(f"平面分割错误: {e}")
            return [(points, colors)]

    def cluster_segmentation(self, points, colors, eps=30.0, min_samples=10):
        """聚类分割"""
        try:
            from sklearn.cluster import DBSCAN

            # 使用DBSCAN进行聚类
            clustering = DBSCAN(eps=eps, min_samples=min_samples).fit(points)
            labels = clustering.labels_

            # 分组点云
            segments = []
            unique_labels = np.unique(labels)

            for label in unique_labels:
                if label == -1:  # 噪声点
                    continue

                mask = labels == label
                cluster_points = points[mask]
                cluster_colors = colors[mask]

                if len(cluster_points) > 0:
                    segments.append((cluster_points, cluster_colors))

            print(f"聚类分割完成: {len(segments)} 个聚类")
            return segments

        except Exception as e:
            print(f"聚类分割错误: {e}")
            return [(points, colors)]

    def load_config_from_file(self, config_file='quality_optimization_config.json'):
        """从JSON配置文件加载参数"""
        try:
            import json

            if not os.path.exists(config_file):
                print(f"配置文件 {config_file} 不存在，使用默认参数")
                return

            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 加载默认质量模式（平衡模式）
            if 'quality_modes' in config and 'balanced' in config['quality_modes']:
                balanced_mode = config['quality_modes']['balanced']
                self.enable_disparity_filtering = balanced_mode.get('enable_disparity_filtering', True)
                self.enable_depth_hole_filling = balanced_mode.get('enable_depth_hole_filling', True)
                self.enable_point_cloud_filtering = balanced_mode.get('enable_point_cloud_filtering', True)
                self.enable_point_cloud_smoothing = balanced_mode.get('enable_point_cloud_smoothing', True)
                self.enable_surface_reconstruction = balanced_mode.get('enable_surface_reconstruction', False)

            # 加载立体匹配参数
            if 'stereo_matching_parameters' in config:
                stereo_params = config['stereo_matching_parameters']
                # 重新创建立体匹配器
                self.stereo_matcher = cv2.StereoSGBM_create(
                    minDisparity=stereo_params.get('minDisparity', 0),
                    numDisparities=stereo_params.get('numDisparities', 96),
                    blockSize=stereo_params.get('blockSize', 7),
                    P1=stereo_params.get('P1_multiplier', 8) * 3 * stereo_params.get('blockSize', 7) ** 2,
                    P2=stereo_params.get('P2_multiplier', 32) * 3 * stereo_params.get('blockSize', 7) ** 2,
                    disp12MaxDiff=stereo_params.get('disp12MaxDiff', 1),
                    uniquenessRatio=stereo_params.get('uniquenessRatio', 10),
                    speckleWindowSize=stereo_params.get('speckleWindowSize', 100),
                    speckleRange=stereo_params.get('speckleRange', 32),
                    preFilterCap=stereo_params.get('preFilterCap', 63),
                    mode=cv2.STEREO_SGBM_MODE_SGBM_3WAY
                )

            # 加载WLS滤波参数
            if 'wls_filter_parameters' in config and self.wls_filter is not None:
                wls_params = config['wls_filter_parameters']
                self.wls_filter.setLambda(wls_params.get('lambda', 8000.0))
                self.wls_filter.setSigmaColor(wls_params.get('sigma_color', 1.5))

            # 存储其他配置参数供后续使用
            self.config = config

            print(f"成功加载配置文件: {config_file}")

        except Exception as e:
            print(f"加载配置文件失败: {e}")
            print("使用默认参数")

    def apply_config_mode(self, mode_name):
        """应用指定的配置模式"""
        try:
            if not hasattr(self, 'config') or 'quality_modes' not in self.config:
                print("配置文件未加载或不包含质量模式设置")
                return False

            if mode_name not in self.config['quality_modes']:
                print(f"未找到模式: {mode_name}")
                available_modes = list(self.config['quality_modes'].keys())
                print(f"可用模式: {available_modes}")
                return False

            mode_config = self.config['quality_modes'][mode_name]

            self.enable_disparity_filtering = mode_config.get('enable_disparity_filtering', True)
            self.enable_depth_hole_filling = mode_config.get('enable_depth_hole_filling', True)
            self.enable_point_cloud_filtering = mode_config.get('enable_point_cloud_filtering', True)
            self.enable_point_cloud_smoothing = mode_config.get('enable_point_cloud_smoothing', True)
            self.enable_surface_reconstruction = mode_config.get('enable_surface_reconstruction', False)

            print(f"已应用配置模式: {mode_name}")
            print(f"描述: {mode_config.get('description', '无描述')}")

            return True

        except Exception as e:
            print(f"应用配置模式失败: {e}")
            return False

    def get_config_parameter(self, section, parameter, default_value=None):
        """获取配置参数"""
        try:
            if hasattr(self, 'config') and section in self.config:
                return self.config[section].get(parameter, default_value)
            return default_value
        except:
            return default_value

# 将返回的错误码转换为十六进制显示
def ToHexStr(num):
    chaDic = {10: 'a', 11: 'b', 12: 'c', 13: 'd', 14: 'e', 15: 'f'}
    hexStr = ""
    if num < 0:
        num = num + 2 ** 32
    while num >= 16:
        digit = num % 16
        hexStr = chaDic.get(digit, str(digit)) + hexStr
        num //= 16
    hexStr = chaDic.get(num, str(num)) + hexStr
    return hexStr

# Decoding Characters
def decoding_char(c_ubyte_value):
    c_char_p_value = ctypes.cast(c_ubyte_value, ctypes.c_char_p)
    try:
        decode_str = c_char_p_value.value.decode('gbk')  # Chinese characters
    except UnicodeDecodeError:
        decode_str = str(c_char_p_value.value)
    return decode_str


if __name__ == "__main__":

    global deviceList
    deviceList = MV_CC_DEVICE_INFO_LIST()

    global cam_checked_list
    cam_checked_list = []

    global obj_cam_operation
    obj_cam_operation = []

    global win_display_handles
    win_display_handles = []

    global valid_number
    valid_number = 0

    global b_is_open
    b_is_open = False

    global b_is_grab
    b_is_grab = False

    global b_is_trigger
    b_is_trigger = False

    global b_is_software_trigger
    b_is_software_trigger = False

    global b_is_hardware_trigger
    b_is_hardware_trigger = False

    global hardware_trigger_line
    hardware_trigger_line = "Line0"  # 默认硬件触发线

    global hardware_trigger_activation
    hardware_trigger_activation = "RisingEdge"  # 默认上升沿触发

    # 立体视觉相关变量
    global stereo_processor
    stereo_processor = None

    global stereo_enabled
    stereo_enabled = False

    global stereo_thread
    stereo_thread = None

    global stereo_running
    stereo_running = False

    # 触发后立体匹配相关变量
    global trigger_stereo_match
    trigger_stereo_match = False

    global last_trigger_images
    last_trigger_images = {"left": None, "right": None}

    # 自动保存相关变量
    global auto_save_enabled
    auto_save_enabled = True

    global auto_save_interval
    auto_save_interval = 30  # 30秒自动保存一次

    global auto_save_timer
    auto_save_timer = None

    # ch:初始化SDK | en: initialize SDK
    MvCamera.MV_CC_Initialize()

    # print info in ui
    def print_text(str_info):
        ui.textEdit.append(str_info)  # 使用 append 代替手动操作光标

    # 初始化立体视觉
    def init_stereo_vision():
        global stereo_processor
        try:
            print_text("正在初始化增强立体视觉系统...")
            stereo_processor = StereoVisionProcessor()

            # 检查立体视觉处理器是否正确初始化
            if (stereo_processor.camera_matrix_left is None or
                stereo_processor.camera_matrix_right is None):
                print_text("立体视觉初始化失败: 标定参数无效")
                stereo_processor = None
                return False

            # 连接信号
            stereo_processor.depth_result_signal.connect(update_depth_display)

            # 设置默认的图像保存参数
            stereo_processor.set_image_save_settings(
                save_input=True,      # 保存原始输入图像
                save_rectified=True,  # 保存校正后图像
                save_comparison=True, # 保存对比图像
                interval=10           # 每10秒保存一次
            )

            # 设置默认的质量优化参数
            stereo_processor.set_quality_optimization_settings(
                enable_disparity_filtering=True,     # 启用视差图滤波
                enable_depth_hole_filling=True,      # 启用深度图孔洞填充
                enable_point_cloud_filtering=True,   # 启用点云滤波
                enable_point_cloud_smoothing=True,   # 启用点云平滑
                enable_surface_reconstruction=False  # 表面重建（计算密集，默认关闭）
            )

            print_text("增强立体视觉系统初始化完成")
            print_text("图像保存设置: 输入图像=开启, 校正图像=开启, 保存间隔=10秒")
            print_text("质量优化: 视差滤波=开启, 孔洞填充=开启, 点云滤波=开启, 点云平滑=开启")
            return True
        except Exception as e:
            print_text(f"立体视觉初始化失败: {e}")
            print_text("将使用简化的立体视觉模式")
            stereo_processor = None
            return False

    def update_depth_display(disparity, depth, depth_color):
        """更新深度显示"""
        try:
            print_text(f"深度图更新: 尺寸 {depth.shape}, 深度范围 {depth.min():.1f}-{depth.max():.1f}mm")
        except Exception as e:
            print_text(f"深度显示更新错误: {e}")

    def set_stereo_image_save_settings(save_input=True, save_rectified=True, interval=5):
        """设置立体图像保存参数"""
        global stereo_processor
        if stereo_processor is not None:
            stereo_processor.set_image_save_settings(save_input, save_rectified, True, interval)
            print_text(f"立体图像保存设置已更新: 输入图像={save_input}, 校正图像={save_rectified}, 间隔={interval}秒")
        else:
            print_text("立体视觉处理器未初始化")

    def force_save_stereo_images():
        """强制保存当前立体图像对"""
        global stereo_processor, obj_cam_operation

        if stereo_processor is None:
            print_text("立体视觉处理器未初始化")
            return

        try:
            # 获取当前左右相机图像
            left_image = get_camera_image(0)
            right_image = get_camera_image(1)

            if left_image is not None and right_image is not None:
                stereo_processor.force_save_current_pair(left_image, right_image)
                print_text("已强制保存当前立体图像对")
            else:
                print_text("无法获取有效的立体图像对")

        except Exception as e:
            print_text(f"强制保存立体图像错误: {e}")

    def display_latest_stereo_images():
        """显示最新保存的立体图像（用于调试和验证）"""
        try:
            import glob

            # 查找最新的输入图像
            input_dir = "stereo_input_images"
            if os.path.exists(input_dir):
                input_files = glob.glob(os.path.join(input_dir, "stereo_comparison_input_*.png"))
                if input_files:
                    latest_input = max(input_files, key=os.path.getctime)
                    print_text(f"最新输入图像对比图: {os.path.basename(latest_input)}")

            # 查找最新的校正图像
            rectified_dir = "stereo_rectified_images"
            if os.path.exists(rectified_dir):
                rectified_files = glob.glob(os.path.join(rectified_dir, "stereo_comparison_rectified_*.png"))
                if rectified_files:
                    latest_rectified = max(rectified_files, key=os.path.getctime)
                    print_text(f"最新校正图像对比图: {os.path.basename(latest_rectified)}")

        except Exception as e:
            print_text(f"显示最新立体图像错误: {e}")

    def get_camera_image(camera_index):
        """获取指定相机的图像"""
        global obj_cam_operation
        try:
            if (camera_index < len(obj_cam_operation) and
                obj_cam_operation[camera_index] != 0 and
                obj_cam_operation[camera_index].buf_save_image is not None):

                frame_info = obj_cam_operation[camera_index].st_frame_info
                if frame_info.nHeight > 0 and frame_info.nWidth > 0:
                    # 转换图像数据为numpy数组
                    image_data = np.frombuffer(
                        obj_cam_operation[camera_index].buf_save_image,
                        dtype=np.uint8
                    )

                    # 根据像素格式重塑图像
                    # 使用数值常量代替可能未定义的常量
                    try:
                        # 尝试获取像素格式常量，如果失败则使用数值
                        mono8_format = getattr(sys.modules[__name__], 'PixelType_Gvsp_Mono8', 0x01080001)
                        rgb8_format = getattr(sys.modules[__name__], 'PixelType_Gvsp_RGB8_Packed', 0x02180014)

                        if frame_info.enPixelType == mono8_format:
                            image = image_data.reshape((frame_info.nHeight, frame_info.nWidth))
                            return cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                        elif frame_info.enPixelType == rgb8_format:
                            image = image_data.reshape((frame_info.nHeight, frame_info.nWidth, 3))
                            return cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
                        else:
                            # 默认处理为单通道
                            image = image_data.reshape((frame_info.nHeight, frame_info.nWidth))
                            return cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                    except Exception as e:
                        print_text(f"像素格式处理错误: {e}")
                        # 简单的默认处理
                        try:
                            image = image_data.reshape((frame_info.nHeight, frame_info.nWidth))
                            return cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                        except:
                            return None
            return None
        except Exception as e:
            print_text(f"获取相机{camera_index}图像失败: {e}")
            return None

    def start_stereo_vision():
        """启动立体视觉"""
        global stereo_enabled, stereo_processor, stereo_thread, stereo_running

        if not b_is_grab:
            print_text("请先启动相机采集")
            return

        # 检查是否至少选择了两个相机
        selected_count = sum(1 for i in range(4) if i < len(obj_cam_operation) and obj_cam_operation[i] != 0)
        if selected_count < 2:
            print_text("立体视觉需要至少两个相机")
            return

        if stereo_processor is None:
            if not init_stereo_vision():
                return

        # 启动立体处理
        stereo_processor.start_processing()

        # 启动图像获取线程
        if not stereo_running:
            stereo_running = True
            stereo_thread = threading.Thread(target=stereo_image_acquisition_loop)
            stereo_thread.daemon = True
            stereo_thread.start()

        stereo_enabled = True
        print_text("立体视觉已启动")
        print_text("立体图像将自动保存到以下目录:")
        print_text("  - 原始图像: stereo_input_images/")
        print_text("  - 校正图像: stereo_rectified_images/")
        print_text("  - 深度图像: depth_images/")
        print_text("  - 点云数据: point_clouds/ (PLY格式)")
        print_text("可调用 force_save_stereo_images() 强制保存当前图像对")

    def stop_stereo_vision():
        """停止立体视觉"""
        global stereo_enabled, stereo_processor, stereo_running

        stereo_enabled = False
        stereo_running = False

        if stereo_processor:
            stereo_processor.stop_processing()

        print_text("立体视觉已停止")

    def stereo_image_acquisition_loop():
        """立体图像获取循环"""
        global stereo_running, stereo_processor, obj_cam_operation

        while stereo_running:
            try:
                # 获取左右相机图像（假设相机0是左相机，相机1是右相机）
                left_image = get_camera_image(0)
                right_image = get_camera_image(1)

                if left_image is not None and right_image is not None and stereo_processor is not None:
                    # 添加到立体处理队列
                    stereo_processor.add_image_pair(left_image, right_image)

                time.sleep(0.033)  # 约30FPS

            except Exception as e:
                print_text(f"立体图像获取错误: {e}")
                time.sleep(0.1)

    # ch:枚举相机 | en:enum devices
    def enum_devices():
        global deviceList
        global valid_number
        deviceList = MV_CC_DEVICE_INFO_LIST()
        n_layer_type = (MV_GIGE_DEVICE | MV_USB_DEVICE
                        | MV_GENTL_GIGE_DEVICE | MV_GENTL_CAMERALINK_DEVICE
                        | MV_GENTL_CXP_DEVICE | MV_GENTL_XOF_DEVICE)
        ret = MvCamera.MV_CC_EnumDevicesEx2(n_layer_type, deviceList, '', SortMethod_SerialNumber)
        if ret != 0:
            str_error = "Enum devices fail! ret = :" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", str_error, QMessageBox.Ok)
            return ret

        if deviceList.nDeviceNum == 0:
            QMessageBox.warning(mainWindow, "Info", "Find no device", QMessageBox.Ok)
            return ret
        print_text("Find %d devices!" % deviceList.nDeviceNum)

        valid_number = 0
        for i in range(0, 4):
            if (i < deviceList.nDeviceNum) is True:
                serial_number = ""
                model_name = ""
                mvcc_dev_info = cast(deviceList.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
                if mvcc_dev_info.nTLayerType == MV_GIGE_DEVICE or mvcc_dev_info.nTLayerType == MV_GENTL_GIGE_DEVICE:
                    print("\ngige device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    nip1 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0xff000000) >> 24)
                    nip2 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x00ff0000) >> 16)
                    nip3 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x0000ff00) >> 8)
                    nip4 = (mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x000000ff)
                    print("current ip: %d.%d.%d.%d " % (nip1, nip2, nip3, nip4))

                    for per in mvcc_dev_info.SpecialInfo.stGigEInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)

                elif mvcc_dev_info.nTLayerType == MV_USB_DEVICE:
                    print("\nu3v device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_CAMERALINK_DEVICE:
                    print("\nCML device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stCMLInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stCMLInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stCMLInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_CXP_DEVICE:
                    print("\nCXP device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stCXPInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stCXPInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stCXPInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_XOF_DEVICE:
                    print("\nXoF device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stXoFInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stXoFInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stXoFInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)

                button_by_id = cam_button_group.button(i)
                button_by_id.setText("(" + serial_number + ")" + model_name)
                button_by_id.setEnabled(True)
                valid_number = valid_number + 1
            else:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(False)

    def cam_check_box_clicked():
        global cam_checked_list
        cam_checked_list = []
        for i in range(0, 4):
            button = cam_button_group.button(i)
            if button.isChecked() is True:
                cam_checked_list.append(True)
            else:
                cam_checked_list.append(False)

    def enable_ui_controls():
        global b_is_open
        global b_is_grab
        global b_is_trigger
        global b_is_software_trigger
        global b_is_hardware_trigger
        ui.pushButton_enum.setEnabled(not b_is_open)
        ui.pushButton_open.setEnabled(not b_is_open)
        ui.pushButton_close.setEnabled(b_is_open)
        result1 = False if b_is_grab else b_is_open
        result2 = b_is_open if b_is_grab else False
        ui.pushButton_startGrab.setEnabled(result1)
        ui.pushButton_stopGrab.setEnabled(result2)
        ui.pushButton_saveImg.setEnabled(result2)
        ui.radioButton_continuous.setEnabled(b_is_open)
        ui.radioButton_trigger.setEnabled(b_is_open)
        ui.pushButton_setParams.setEnabled(b_is_open)
        ui.lineEdit_gain.setEnabled(b_is_open)
        ui.lineEdit_frameRate.setEnabled(b_is_open)
        ui.lineEdit_exposureTime.setEnabled(b_is_open)
        result3 = b_is_open if b_is_trigger else False
        ui.pushButton_triggerOnce.setEnabled(b_is_software_trigger and result3)
        ui.checkBox_software_trigger.setEnabled(b_is_trigger)
        ui.checkBox_hardware_trigger.setEnabled(b_is_trigger)

    def open_devices():
        global deviceList
        global obj_cam_operation
        global b_is_open
        global valid_number
        global cam_checked_list
        b_checked = 0
        if b_is_open is True:
            return

        if len(cam_checked_list) <= 0:
            print_text("please select a camera !")
            return
        obj_cam_operation = []
        for i in range(0, 4):
            if cam_checked_list[i] is True:
                b_checked = True
                camObj = MvCamera()
                obj_cam_operation.append(CameraOperation(camObj, deviceList, i))
                ret = obj_cam_operation[i].open_device()
                if 0 != ret:
                    obj_cam_operation.pop()
                    print_text("open cam %d fail ret[0x%x]" % (i, ret))
                    continue
                else:
                    b_is_open = True
            else:
                obj_cam_operation.append(0)
        if b_checked is False:
            print_text("please select a camera !")
            return
        if b_is_open is False:
            print_text("no camera opened successfully !")
            return
        else:
            ui.radioButton_continuous.setChecked(True)
            enable_ui_controls()

        for i in range(0, 4):
            if(i < valid_number) is True:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(not b_is_open)

    def software_trigger_check_box_clicked():
        global obj_cam_operation
        global b_is_software_trigger
        global b_is_hardware_trigger
        global hardware_trigger_line
        global hardware_trigger_activation

        if (ui.checkBox_software_trigger.isChecked()) is True:
            b_is_software_trigger = True
            b_is_hardware_trigger = False
            ui.checkBox_hardware_trigger.setChecked(False)

            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_trigger_source("software")
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger source: software  fail! ret = ' + ToHexStr(ret))
                    else:
                        print_text('camera' + str(i) + ' set to software trigger mode')
        else:
            b_is_software_trigger = False

        enable_ui_controls()

    def hardware_trigger_check_box_clicked():
        global obj_cam_operation
        global b_is_hardware_trigger
        global b_is_software_trigger
        global hardware_trigger_line
        global hardware_trigger_activation

        if (ui.checkBox_hardware_trigger.isChecked()) is True:
            b_is_hardware_trigger = True
            b_is_software_trigger = False
            ui.checkBox_software_trigger.setChecked(False)

            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    # 设置硬件触发源
                    ret = obj_cam_operation[i].set_trigger_source("hardware", hardware_trigger_line)
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger source: hardware fail! ret = ' + ToHexStr(ret))
                        continue

                    # 设置触发极性
                    ret = obj_cam_operation[i].set_trigger_activation(hardware_trigger_activation)
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger activation fail! ret = ' + ToHexStr(ret))
                        continue

                    print_text('camera' + str(i) + f' set to hardware trigger mode ({hardware_trigger_line}, {hardware_trigger_activation})')
        else:
            b_is_hardware_trigger = False

        enable_ui_controls()

    def radio_button_clicked(button):
        global obj_cam_operation
        global b_is_trigger
        button_id = raio_button_group.id(button)
        if (button_id == 0) is True:
            b_is_trigger = False
            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_trigger_mode("continuous")
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger mode: continuous fail! ret = ' + ToHexStr(ret))
            enable_ui_controls()

        else:
            b_is_trigger = True
            for i in range(0, 4):
                if obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_trigger_mode("triggermode")
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger on fail! ret = ' + ToHexStr(ret))
            enable_ui_controls()

    def close_devices():
        global b_is_open
        global obj_cam_operation
        global valid_number

        if b_is_open is False:
            return
        if b_is_grab is True:
            stop_grabbing()
        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].close_device()
                if 0 != ret:
                    print_text('camera' + str(i) + ' close device fail! ret = ' + ToHexStr(ret))

            if i < valid_number:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(True)
        b_is_open = False
        enable_ui_controls()

    def start_grabbing():
        global obj_cam_operation
        global win_display_handles
        global b_is_open
        global b_is_grab

        if (not b_is_open) or (b_is_grab is True):
            return

        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].start_grabbing(i, win_display_handles[i])
                if 0 != ret:
                    print_text('camera' + str(i) + ' start grabbing fail! ret = ' + ToHexStr(ret))
                b_is_grab = True
        enable_ui_controls()

        # 自动启动立体视觉（如果有两个或以上相机）
        selected_count = sum(1 for i in range(4) if i < len(obj_cam_operation) and obj_cam_operation[i] != 0)
        if selected_count >= 2:
            print_text("检测到多个相机，可以启动立体视觉")
            start_stereo_vision()

        # 启动自动保存
        if auto_save_enabled:
            start_auto_save()

    def stop_grabbing():
        global b_is_grab
        global obj_cam_operation
        global b_is_open

        if (not b_is_open) or (b_is_grab is False):
            return

        # 停止立体视觉
        stop_stereo_vision()

        # 停止自动保存
        stop_auto_save()

        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].stop_grabbing()
                if 0 != ret:
                    print_text('camera' + str(i) + ' stop grabbing fail!ret = ' + ToHexStr(ret))
                b_is_grab = False
        enable_ui_controls()

    # ch:存图 | en:save image - 增强版本支持触发图像保存
    def save_bmp():
        global b_is_grab
        global obj_cam_operation
        global last_trigger_images

        # 优先保存触发图像，如果没有则保存当前图像
        if (last_trigger_images["left"] is not None or
            last_trigger_images["right"] is not None):
            print_text("保存触发后的图像...")
            save_trigger_images_manual()
            return

        if b_is_grab is False:
            print_text("无法保存图像：相机未在采集状态")
            return

        try:
            # 创建保存目录
            save_dir = "saved_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            saved_count = 0

            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    # 检查图像缓冲区是否有有效的图像数据
                    if (obj_cam_operation[i].buf_save_image is None or
                        obj_cam_operation[i].st_frame_info is None):
                        print_text(f'camera{i} has no image data to save.')
                        continue

                    frame_info = obj_cam_operation[i].st_frame_info

                    # 检查图像尺寸是否有效
                    if frame_info.nHeight <= 0 or frame_info.nWidth <= 0:
                        print_text(f'camera{i} frame info has invalid dimensions')
                        continue

                    try:
                        # 使用改进的保存方法，传递保存目录
                        ret = obj_cam_operation[i].save_bmp(save_dir)
                        if 0 == ret:
                            saved_count += 1
                            print_text(f'camera{i} image saved successfully to {save_dir}')
                        elif ret != -1:  # -1 表示没有图像数据，不是真正的错误
                            print_text('camera' + str(i) + ' save bmp fail!ret = ' + ToHexStr(ret))

                    except Exception as e:
                        print_text(f'camera{i} save error: {e}')
                        continue

            if saved_count > 0:
                print_text(f"成功保存 {saved_count} 张图像")
            else:
                print_text("未能保存任何图像")

        except Exception as e:
            print_text(f"保存图像错误: {e}")

    def save_trigger_images_manual():
        """手动保存触发后的图像"""
        global last_trigger_images

        try:
            save_dir = "trigger_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            saved_count = 0

            if last_trigger_images["left"] is not None:
                filename = f"trigger_left_{timestamp}.png"
                filepath = os.path.join(save_dir, filename)
                cv2.imwrite(filepath, last_trigger_images["left"])
                saved_count += 1
                print_text(f'Left trigger image saved: {filename}')

            if last_trigger_images["right"] is not None:
                filename = f"trigger_right_{timestamp}.png"
                filepath = os.path.join(save_dir, filename)
                cv2.imwrite(filepath, last_trigger_images["right"])
                saved_count += 1
                print_text(f'Right trigger image saved: {filename}')

            if saved_count > 0:
                print_text(f"成功保存 {saved_count} 张触发图像到 {save_dir}")
                # 清空触发图像缓存
                last_trigger_images = {"left": None, "right": None}
            else:
                print_text("没有触发图像可保存")

        except Exception as e:
            print_text(f"保存触发图像错误: {e}")

    def is_float(str_value):
        try:
            float(str_value)
            return True
        except ValueError:
            return False

    def set_parameters():
        global obj_cam_operation
        global b_is_open
        if b_is_open is False:
            return

        frame_rate = ui.lineEdit_frameRate.text()
        exposure_time = ui.lineEdit_exposureTime.text()
        gain = ui.lineEdit_gain.text()

        if is_float(frame_rate) is False or is_float(exposure_time) is False or is_float(gain) is False:
            print_text("parameters is valid, please check")
            return

        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].set_exposure_time(exposure_time)
                if ret != 0:
                    print_text('camera' + str(i) + ' Set exposure time failed ret:' + ToHexStr(ret))
                ret = obj_cam_operation[i].set_gain(gain)
                if ret != 0:
                    print_text('camera' + str(i) + ' Set gain failed ret:' + ToHexStr(ret))
                ret = obj_cam_operation[i].set_frame_rate(frame_rate)
                if ret != 0:
                    print_text('camera' + str(i) + ' set acquisition frame rate failed ret:' + ToHexStr(ret))

    def software_trigger_once():
        global last_trigger_images

        # 清空上次触发的图像
        last_trigger_images = {"left": None, "right": None}

        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].trigger_once()
                if ret != 0:
                    print_text('camera' + str(i) + 'TriggerSoftware failed ret:' + ToHexStr(ret))
                else:
                    print_text('camera' + str(i) + ' software trigger executed')

        # 等待一段时间让图像采集完成，然后自动保存图像
        QTimer.singleShot(500, capture_and_save_trigger_images)  # 500ms后获取并保存图像

    def capture_and_save_trigger_images():
        """捕获触发后的图像并自动保存"""
        global last_trigger_images, obj_cam_operation

        try:
            print_text("正在捕获触发后的图像...")

            # 创建保存目录
            save_dir = "trigger_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            saved_count = 0

            # 获取并保存所有相机的图像
            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    # 检查图像缓冲区是否有有效的图像数据
                    if (obj_cam_operation[i].buf_save_image is None or
                        obj_cam_operation[i].st_frame_info is None):
                        print_text(f'camera{i} has no image data to save.')
                        continue

                    frame_info = obj_cam_operation[i].st_frame_info

                    # 检查图像尺寸是否有效
                    if frame_info.nHeight <= 0 or frame_info.nWidth <= 0:
                        print_text(f'camera{i} frame info has invalid dimensions')
                        continue

                    try:
                        # 获取图像数据
                        image_data = np.frombuffer(
                            obj_cam_operation[i].buf_save_image,
                            dtype=np.uint8
                        )

                        # 根据像素格式重塑图像
                        if len(image_data) >= frame_info.nHeight * frame_info.nWidth:
                            # 简单处理为灰度图像
                            image = image_data[:frame_info.nHeight * frame_info.nWidth].reshape(
                                (frame_info.nHeight, frame_info.nWidth)
                            )

                            # 保存图像
                            filename = f"trigger_cam{i}_{timestamp}.png"
                            filepath = os.path.join(save_dir, filename)
                            cv2.imwrite(filepath, image)

                            saved_count += 1
                            print_text(f'camera{i} image saved: {filename}')

                            # 如果是前两个相机，保存到立体匹配用的变量中
                            if i == 0:
                                last_trigger_images["left"] = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                            elif i == 1:
                                last_trigger_images["right"] = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)

                    except Exception as e:
                        print_text(f'camera{i} save error: {e}')
                        continue

            if saved_count > 0:
                print_text(f"触发图像保存完成！共保存 {saved_count} 张图像到 {save_dir}")

                # 检查是否成功获取了双目图像，如果是则可以进行立体匹配
                if last_trigger_images["left"] is not None and last_trigger_images["right"] is not None:
                    print_text("双目图像捕获成功，可以进行立体匹配")
                    # 如果启用了自动立体匹配，则自动进行立体匹配
                    if trigger_stereo_match:
                        perform_trigger_stereo_match()
            else:
                print_text("未能保存任何触发图像")

        except Exception as e:
            print_text(f"触发图像捕获错误: {e}")

    def capture_trigger_images():
        """捕获触发后的图像"""
        global last_trigger_images, obj_cam_operation

        try:
            # 获取左相机图像（相机0）
            left_image = get_camera_image(0)
            if left_image is not None:
                last_trigger_images["left"] = left_image.copy()
                print_text("Left camera image captured")

            # 获取右相机图像（相机1）
            right_image = get_camera_image(1)
            if right_image is not None:
                last_trigger_images["right"] = right_image.copy()
                print_text("Right camera image captured")

            # 检查是否成功获取了双目图像
            if last_trigger_images["left"] is not None and last_trigger_images["right"] is not None:
                print_text("Stereo image pair captured successfully")
                # 如果启用了自动立体匹配，则自动进行立体匹配
                if trigger_stereo_match:
                    perform_trigger_stereo_match()
            else:
                print_text("Failed to capture stereo image pair")

        except Exception as e:
            print_text(f"Error capturing trigger images: {e}")

    def perform_trigger_stereo_match():
        """对触发后的图像进行立体匹配"""
        global last_trigger_images, stereo_processor

        if (last_trigger_images["left"] is None or
            last_trigger_images["right"] is None):
            print_text("No captured images available for stereo matching")
            return

        try:
            if stereo_processor is None:
                if not init_stereo_vision():
                    return

            print_text("Processing stereo matching...")

            # 处理立体图像对
            result = stereo_processor.process_stereo_pair(
                last_trigger_images["left"],
                last_trigger_images["right"]
            )

            # 保存结果
            save_stereo_results(last_trigger_images["left"], last_trigger_images["right"], result)

            print_text("Stereo matching completed")

        except Exception as e:
            print_text(f"Error in stereo matching: {e}")

    def save_stereo_results(left_img, right_img, result):
        """保存立体匹配结果"""
        try:
            save_dir = "stereo_results"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 保存原始图像
            cv2.imwrite(os.path.join(save_dir, f"left_{timestamp}.png"), left_img)
            cv2.imwrite(os.path.join(save_dir, f"right_{timestamp}.png"), right_img)

            # 保存深度图和点云
            if result and 'depth' in result and result['depth'] is not None:
                depth_norm = cv2.normalize(result['depth'], None, 0, 255, cv2.NORM_MINMAX)
                depth_color = cv2.applyColorMap(depth_norm.astype(np.uint8), cv2.COLORMAP_JET)
                cv2.imwrite(os.path.join(save_dir, f"depth_{timestamp}.png"), depth_color)

                # 保存点云数据（如果有3D点云数据）
                if 'points_3d' in result and result['points_3d'] is not None:
                    print_text("Saving point cloud data...")
                    save_trigger_point_cloud(result['points_3d'], left_img, save_dir, timestamp)

            print_text(f"Stereo results saved to {save_dir}")

        except Exception as e:
            print_text(f"Error saving stereo results: {e}")

    def save_trigger_point_cloud(points_3d, color_image, save_dir, timestamp):
        """保存触发模式下的点云数据"""
        try:
            # 提取有效的3D点
            height, width = points_3d.shape[:2]
            total_pixels = height * width

            # 过滤有效点（深度值在2m-6m范围内）
            valid_mask = (points_3d[:, :, 2] >= 2000) & (points_3d[:, :, 2] <= 6000)
            valid_count = np.sum(valid_mask)

            # 计算点云覆盖率
            coverage_percentage = (valid_count / total_pixels) * 100

            if not np.any(valid_mask):
                print_text("No valid 3D points found for point cloud generation")
                return

            # 提取有效的XYZ坐标
            valid_points = points_3d[valid_mask]

            # 统计深度范围
            depth_values = valid_points[:, 2]
            min_depth = np.min(depth_values)
            max_depth = np.max(depth_values)
            mean_depth = np.mean(depth_values)

            # 提取对应的颜色信息
            if color_image is not None and color_image.shape[:2] == (height, width):
                if len(color_image.shape) == 3:
                    # BGR转RGB
                    color_rgb = cv2.cvtColor(color_image, cv2.COLOR_BGR2RGB)
                    valid_colors = color_rgb[valid_mask]
                else:
                    # 灰度图转RGB
                    gray_rgb = np.stack([color_image[valid_mask]] * 3, axis=1)
                    valid_colors = gray_rgb
            else:
                # 默认白色
                valid_colors = np.full((len(valid_points), 3), 255, dtype=np.uint8)

            # 输出详细统计信息
            print_text("=== 触发模式点云生成统计 ===")
            print_text(f"图像分辨率: {width} x {height} ({total_pixels:,} 像素)")
            print_text(f"有效点云数量: {valid_count:,} 点")
            print_text(f"点云覆盖率: {coverage_percentage:.2f}%")
            print_text(f"深度范围: {min_depth:.1f}mm - {max_depth:.1f}mm (有效范围: 2000-6000mm)")
            print_text(f"平均深度: {mean_depth:.1f}mm")
            print_text(f"基线距离: 100.88mm")

            # 深度范围检查提示
            if min_depth < 2000 or max_depth > 6000:
                print_text("警告: 检测到超出预期范围的深度值，建议检查标定参数或场景设置")

            # 保存多种格式的点云
            save_point_cloud_file(valid_points, valid_colors, save_dir, timestamp, 'ply')
            save_point_cloud_file(valid_points, valid_colors, save_dir, timestamp, 'pcd')
            save_point_cloud_file(valid_points, valid_colors, save_dir, timestamp, 'txt')

        except Exception as e:
            print_text(f"Error saving trigger point cloud: {e}")

    def save_point_cloud_file(points, colors, save_dir, timestamp, format):
        """保存点云文件的通用函数"""
        try:
            if format.lower() == 'ply':
                filename = f"pointcloud_{timestamp}.ply"
                filepath = os.path.join(save_dir, filename)

                with open(filepath, 'w') as f:
                    # PLY文件头
                    f.write("ply\n")
                    f.write("format ascii 1.0\n")
                    f.write(f"element vertex {len(points)}\n")
                    f.write("property float x\n")
                    f.write("property float y\n")
                    f.write("property float z\n")
                    f.write("property uchar red\n")
                    f.write("property uchar green\n")
                    f.write("property uchar blue\n")
                    f.write("end_header\n")

                    # 写入点云数据
                    for i in range(len(points)):
                        x, y, z = points[i]
                        r, g, b = colors[i]
                        f.write(f"{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n")

                print_text(f"Point cloud saved as PLY: {filename}")

            elif format.lower() == 'pcd':
                filename = f"pointcloud_{timestamp}.pcd"
                filepath = os.path.join(save_dir, filename)

                with open(filepath, 'w') as f:
                    # PCD文件头
                    f.write("# .PCD v0.7 - Point Cloud Data file format\n")
                    f.write("VERSION 0.7\n")
                    f.write("FIELDS x y z rgb\n")
                    f.write("SIZE 4 4 4 4\n")
                    f.write("TYPE F F F U\n")
                    f.write("COUNT 1 1 1 1\n")
                    f.write(f"WIDTH {len(points)}\n")
                    f.write("HEIGHT 1\n")
                    f.write("VIEWPOINT 0 0 0 1 0 0 0\n")
                    f.write(f"POINTS {len(points)}\n")
                    f.write("DATA ascii\n")

                    # 写入点云数据
                    for i in range(len(points)):
                        x, y, z = points[i]
                        r, g, b = colors[i]
                        # 将RGB打包为单个32位整数
                        rgb_packed = (int(r) << 16) | (int(g) << 8) | int(b)
                        f.write(f"{x:.6f} {y:.6f} {z:.6f} {rgb_packed}\n")

                print_text(f"Point cloud saved as PCD: {filename}")

            elif format.lower() == 'txt':
                filename = f"pointcloud_{timestamp}.txt"
                filepath = os.path.join(save_dir, filename)

                with open(filepath, 'w') as f:
                    # 写入文件头注释
                    f.write("# Point Cloud Data\n")
                    f.write("# Format: X Y Z R G B\n")
                    f.write(f"# Points: {len(points)}\n")

                    # 写入点云数据
                    for i in range(len(points)):
                        x, y, z = points[i]
                        r, g, b = colors[i]
                        f.write(f"{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n")

                print_text(f"Point cloud saved as TXT: {filename}")

        except Exception as e:
            print_text(f"Error saving {format} point cloud: {e}")

    def manual_stereo_match():
        """手动触发立体匹配"""
        global trigger_stereo_match

        if (last_trigger_images["left"] is None or
            last_trigger_images["right"] is None):
            print_text("No captured images available. Please trigger cameras first.")
            return

        print_text("Manual stereo matching triggered...")
        perform_trigger_stereo_match()

    def enable_auto_stereo_match():
        """启用自动立体匹配"""
        global trigger_stereo_match
        trigger_stereo_match = True
        print_text("自动立体匹配已启用")

    def disable_auto_stereo_match():
        """禁用自动立体匹配"""
        global trigger_stereo_match
        trigger_stereo_match = False
        print_text("自动立体匹配已禁用")

    # 图像质量优化控制函数
    def enable_high_quality_mode():
        """启用高质量模式（所有优化功能）"""
        global stereo_processor
        if stereo_processor is not None:
            stereo_processor.set_quality_optimization_settings(
                enable_disparity_filtering=True,
                enable_depth_hole_filling=True,
                enable_point_cloud_filtering=True,
                enable_point_cloud_smoothing=True,
                enable_surface_reconstruction=True  # 启用表面重建
            )
            print_text("已启用高质量模式（包含表面重建）")
        else:
            print_text("立体视觉处理器未初始化")

    def enable_fast_mode():
        """启用快速模式（禁用部分优化功能）"""
        global stereo_processor
        if stereo_processor is not None:
            stereo_processor.set_quality_optimization_settings(
                enable_disparity_filtering=True,   # 保留基础滤波
                enable_depth_hole_filling=False,   # 禁用孔洞填充
                enable_point_cloud_filtering=False, # 禁用点云滤波
                enable_point_cloud_smoothing=False, # 禁用点云平滑
                enable_surface_reconstruction=False # 禁用表面重建
            )
            print_text("已启用快速模式（减少处理时间）")
        else:
            print_text("立体视觉处理器未初始化")

    def enable_balanced_mode():
        """启用平衡模式（默认设置）"""
        global stereo_processor
        if stereo_processor is not None:
            stereo_processor.set_quality_optimization_settings(
                enable_disparity_filtering=True,
                enable_depth_hole_filling=True,
                enable_point_cloud_filtering=True,
                enable_point_cloud_smoothing=True,
                enable_surface_reconstruction=False
            )
            print_text("已启用平衡模式（质量与速度平衡）")
        else:
            print_text("立体视觉处理器未初始化")

    def toggle_disparity_filtering():
        """切换视差图滤波"""
        global stereo_processor
        if stereo_processor is not None:
            stereo_processor.enable_disparity_filtering = not stereo_processor.enable_disparity_filtering
            status = "启用" if stereo_processor.enable_disparity_filtering else "禁用"
            print_text(f"视差图滤波已{status}")
        else:
            print_text("立体视觉处理器未初始化")

    def toggle_depth_hole_filling():
        """切换深度图孔洞填充"""
        global stereo_processor
        if stereo_processor is not None:
            stereo_processor.enable_depth_hole_filling = not stereo_processor.enable_depth_hole_filling
            status = "启用" if stereo_processor.enable_depth_hole_filling else "禁用"
            print_text(f"深度图孔洞填充已{status}")
        else:
            print_text("立体视觉处理器未初始化")

    def toggle_point_cloud_filtering():
        """切换点云滤波"""
        global stereo_processor
        if stereo_processor is not None:
            stereo_processor.enable_point_cloud_filtering = not stereo_processor.enable_point_cloud_filtering
            status = "启用" if stereo_processor.enable_point_cloud_filtering else "禁用"
            print_text(f"点云滤波已{status}")
        else:
            print_text("立体视觉处理器未初始化")

    def toggle_surface_reconstruction():
        """切换表面重建"""
        global stereo_processor
        if stereo_processor is not None:
            stereo_processor.enable_surface_reconstruction = not stereo_processor.enable_surface_reconstruction
            status = "启用" if stereo_processor.enable_surface_reconstruction else "禁用"
            print_text(f"表面重建已{status}")
            if stereo_processor.enable_surface_reconstruction:
                print_text("警告: 表面重建会显著增加处理时间")
        else:
            print_text("立体视觉处理器未初始化")

    def show_quality_settings():
        """显示当前质量设置"""
        global stereo_processor
        if stereo_processor is not None:
            print_text("=== 当前图像质量设置 ===")
            print_text(f"视差图滤波: {'启用' if stereo_processor.enable_disparity_filtering else '禁用'}")
            print_text(f"深度图孔洞填充: {'启用' if stereo_processor.enable_depth_hole_filling else '禁用'}")
            print_text(f"点云滤波: {'启用' if stereo_processor.enable_point_cloud_filtering else '禁用'}")
            print_text(f"点云平滑: {'启用' if stereo_processor.enable_point_cloud_smoothing else '禁用'}")
            print_text(f"表面重建: {'启用' if stereo_processor.enable_surface_reconstruction else '禁用'}")
        else:
            print_text("立体视觉处理器未初始化")

    # 配置管理函数
    def load_quality_config(config_file='quality_optimization_config.json'):
        """加载质量优化配置"""
        global stereo_processor
        if stereo_processor is not None:
            stereo_processor.load_config_from_file(config_file)
            print_text(f"已加载配置文件: {config_file}")
        else:
            print_text("立体视觉处理器未初始化")

    def apply_quality_mode(mode_name):
        """应用指定的质量模式"""
        global stereo_processor
        if stereo_processor is not None:
            success = stereo_processor.apply_config_mode(mode_name)
            if success:
                print_text(f"已应用质量模式: {mode_name}")
            else:
                print_text(f"应用质量模式失败: {mode_name}")
        else:
            print_text("立体视觉处理器未初始化")

    def list_available_modes():
        """列出可用的质量模式"""
        global stereo_processor
        if stereo_processor is not None and hasattr(stereo_processor, 'config'):
            if 'quality_modes' in stereo_processor.config:
                print_text("=== 可用的质量模式 ===")
                for mode_name, mode_config in stereo_processor.config['quality_modes'].items():
                    description = mode_config.get('description', '无描述')
                    print_text(f"{mode_name}: {description}")
            else:
                print_text("配置文件中未找到质量模式设置")
        else:
            print_text("立体视觉处理器未初始化或配置文件未加载")

    def save_current_config(output_file='current_config.json'):
        """保存当前配置到文件"""
        global stereo_processor
        if stereo_processor is not None:
            try:
                import json

                current_config = {
                    'current_settings': {
                        'enable_disparity_filtering': stereo_processor.enable_disparity_filtering,
                        'enable_depth_hole_filling': stereo_processor.enable_depth_hole_filling,
                        'enable_point_cloud_filtering': stereo_processor.enable_point_cloud_filtering,
                        'enable_point_cloud_smoothing': stereo_processor.enable_point_cloud_smoothing,
                        'enable_surface_reconstruction': stereo_processor.enable_surface_reconstruction
                    },
                    'timestamp': time.time()
                }

                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(current_config, f, indent=2, ensure_ascii=False)

                print_text(f"当前配置已保存到: {output_file}")

            except Exception as e:
                print_text(f"保存配置失败: {e}")
        else:
            print_text("立体视觉处理器未初始化")

    def check_point_cloud_status():
        """检查点云生成状态"""
        global stereo_processor
        if stereo_processor is not None:
            print_text("=== 点云生成状态检查 ===")

            # 检查Q矩阵
            if stereo_processor.Q_matrix is not None:
                print_text("✓ Q矩阵已初始化")
                print_text(f"Q矩阵形状: {stereo_processor.Q_matrix.shape}")
            else:
                print_text("✗ Q矩阵未初始化 - 这是点云无法生成的主要原因")

            # 检查标定状态
            print_text(f"标定状态: {'可用' if stereo_processor.calibration_available else '不可用'}")

            # 检查点云相关设置
            print_text(f"点云滤波: {'启用' if stereo_processor.enable_point_cloud_filtering else '禁用'}")
            print_text(f"点云平滑: {'启用' if stereo_processor.enable_point_cloud_smoothing else '禁用'}")

            # 检查输出目录
            enhanced_dir = "enhanced_point_clouds"
            basic_dir = "point_clouds"
            print_text(f"增强点云目录: {enhanced_dir} ({'存在' if os.path.exists(enhanced_dir) else '不存在'})")
            print_text(f"基础点云目录: {basic_dir} ({'存在' if os.path.exists(basic_dir) else '不存在'})")

            # 列出已生成的点云文件
            for directory in [enhanced_dir, basic_dir]:
                if os.path.exists(directory):
                    ply_files = [f for f in os.listdir(directory) if f.endswith('.ply')]
                    if ply_files:
                        print_text(f"{directory} 中的点云文件:")
                        for f in ply_files[-5:]:  # 显示最近的5个文件
                            print_text(f"  - {f}")
                    else:
                        print_text(f"{directory} 中没有点云文件")
        else:
            print_text("立体视觉处理器未初始化")

    def force_generate_point_cloud():
        """强制生成点云（用于调试）"""
        global stereo_processor, obj_cam_operation

        if stereo_processor is None:
            print_text("立体视觉处理器未初始化")
            return

        try:
            print_text("开始强制生成点云...")

            # 获取当前左右相机图像
            left_image = get_camera_image(0)
            right_image = get_camera_image(1)

            if left_image is None or right_image is None:
                print_text("无法获取有效的立体图像对")
                return

            print_text("成功获取立体图像对")

            # 计算视差图
            disparity, left_rect, right_rect = stereo_processor.compute_disparity(left_image, right_image)
            print_text(f"视差图计算完成，尺寸: {disparity.shape}")

            # 检查Q矩阵
            if stereo_processor.Q_matrix is None:
                print_text("Q矩阵为空，尝试使用简化Q矩阵...")
                # 使用简化的Q矩阵
                cx = 1220.0  # 主点x坐标
                cy = 1015.0  # 主点y坐标
                fx = 2348.0  # 焦距
                baseline = 100.88  # 基线距离(mm)

                stereo_processor.Q_matrix = np.array([
                    [1.0, 0.0, 0.0, -cx],
                    [0.0, 1.0, 0.0, -cy],
                    [0.0, 0.0, 0.0, fx],
                    [0.0, 0.0, -1.0/baseline, 0.0]
                ], dtype=np.float64)
                print_text("简化Q矩阵已设置")

            # 生成3D点云
            points_3d = cv2.reprojectImageTo3D(disparity, stereo_processor.Q_matrix)
            print_text(f"3D点云生成完成，尺寸: {points_3d.shape}")

            # 保存点云
            stereo_processor.save_enhanced_point_cloud(points_3d, left_rect)
            print_text("点云保存完成")

        except Exception as e:
            print_text(f"强制生成点云错误: {e}")

    def list_point_cloud_files():
        """列出所有点云文件"""
        directories = ["enhanced_point_clouds", "point_clouds", "stereo_results"]

        print_text("=== 点云文件列表 ===")
        total_files = 0

        for directory in directories:
            if os.path.exists(directory):
                files = [f for f in os.listdir(directory) if f.endswith(('.ply', '.pcd', '.obj', '.txt'))]
                if files:
                    print_text(f"\n{directory}/ ({len(files)} 个文件):")
                    for f in sorted(files):
                        file_path = os.path.join(directory, f)
                        file_size = os.path.getsize(file_path)
                        print_text(f"  - {f} ({file_size} bytes)")
                        total_files += 1
                else:
                    print_text(f"\n{directory}/ (空)")
            else:
                print_text(f"\n{directory}/ (不存在)")

        if total_files == 0:
            print_text("\n没有找到任何点云文件")
            print_text("建议:")
            print_text("1. 检查立体视觉是否正常工作")
            print_text("2. 调用 force_generate_point_cloud() 强制生成")
            print_text("3. 调用 check_point_cloud_status() 检查状态")
        else:
            print_text(f"\n总共找到 {total_files} 个点云文件")

    # 自动保存功能
    def start_auto_save():
        """启动自动保存"""
        global auto_save_timer, auto_save_enabled, auto_save_interval

        if auto_save_enabled and b_is_grab:
            auto_save_timer = QTimer()
            auto_save_timer.timeout.connect(auto_save_images)
            auto_save_timer.start(auto_save_interval * 1000)  # 转换为毫秒
            print_text(f"自动保存已启动，间隔 {auto_save_interval} 秒")

    def stop_auto_save():
        """停止自动保存"""
        global auto_save_timer

        if auto_save_timer:
            auto_save_timer.stop()
            auto_save_timer = None
            print_text("自动保存已停止")

    def auto_save_images():
        """自动保存图像"""
        global b_is_grab, obj_cam_operation

        if not b_is_grab:
            stop_auto_save()
            return

        try:
            # 创建自动保存目录
            save_dir = "auto_saved_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)
            saved_count = 0

            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    # 检查图像缓冲区是否有有效的图像数据
                    if (obj_cam_operation[i].buf_save_image is None or
                        obj_cam_operation[i].st_frame_info is None):
                        continue

                    frame_info = obj_cam_operation[i].st_frame_info

                    # 检查图像尺寸是否有效
                    if frame_info.nHeight <= 0 or frame_info.nWidth <= 0:
                        continue

                    try:
                        # 获取图像数据
                        image_data = np.frombuffer(
                            obj_cam_operation[i].buf_save_image,
                            dtype=np.uint8
                        )

                        # 根据像素格式重塑图像
                        if len(image_data) >= frame_info.nHeight * frame_info.nWidth:
                            # 简单处理为灰度图像
                            image = image_data[:frame_info.nHeight * frame_info.nWidth].reshape(
                                (frame_info.nHeight, frame_info.nWidth)
                            )

                            # 保存图像
                            filename = f"auto_cam{i}_{timestamp}.png"
                            filepath = os.path.join(save_dir, filename)
                            cv2.imwrite(filepath, image)
                            saved_count += 1

                    except Exception as e:
                        print_text(f'自动保存相机{i}图像错误: {e}')
                        continue

            if saved_count > 0:
                print_text(f"自动保存完成：{saved_count} 张图像")

        except Exception as e:
            print_text(f"自动保存错误: {e}")

    def toggle_auto_save():
        """切换自动保存状态"""
        global auto_save_enabled

        auto_save_enabled = not auto_save_enabled
        if auto_save_enabled:
            print_text("自动保存已启用")
            if b_is_grab:
                start_auto_save()
        else:
            print_text("自动保存已禁用")
            stop_auto_save()

    # 设置硬件触发线
    def set_hardware_trigger_line(line_name):
        global hardware_trigger_line
        global obj_cam_operation
        global b_is_open
        global b_is_software_trigger

        if not b_is_open or b_is_software_trigger:
            hardware_trigger_line = line_name
            print_text(f'Hardware trigger line set to: {line_name}')
            return

        hardware_trigger_line = line_name
        # 如果当前是硬件触发模式，立即应用设置
        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].set_trigger_source("hardware", hardware_trigger_line)
                if 0 != ret:
                    print_text('camera' + str(i) + f' set trigger line to {line_name} fail! ret = ' + ToHexStr(ret))
                else:
                    print_text('camera' + str(i) + f' trigger line set to: {line_name}')

    # 设置硬件触发极性
    def set_hardware_trigger_activation(activation):
        global hardware_trigger_activation
        global obj_cam_operation
        global b_is_open
        global b_is_software_trigger

        if not b_is_open or b_is_software_trigger:
            hardware_trigger_activation = activation
            print_text(f'Hardware trigger activation set to: {activation}')
            return

        hardware_trigger_activation = activation
        # 如果当前是硬件触发模式，立即应用设置
        for i in range(0, 4):
            if obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].set_trigger_activation(hardware_trigger_activation)
                if 0 != ret:
                    print_text('camera' + str(i) + f' set trigger activation to {activation} fail! ret = ' + ToHexStr(ret))
                else:
                    print_text('camera' + str(i) + f' trigger activation set to: {activation}')

    # ch: 初始化app, 绑定控件与函数 | en: Init app, bind ui and api
    app = QApplication(sys.argv)
    mainWindow = QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(mainWindow)
    ui.pushButton_enum.clicked.connect(enum_devices)
    ui.pushButton_open.clicked.connect(open_devices)
    ui.pushButton_close.clicked.connect(close_devices)
    ui.pushButton_startGrab.clicked.connect(start_grabbing)
    ui.pushButton_stopGrab.clicked.connect(stop_grabbing)
    ui.pushButton_saveImg.clicked.connect(save_bmp)
    ui.pushButton_setParams.clicked.connect(set_parameters)
    ui.checkBox_software_trigger.clicked.connect(software_trigger_check_box_clicked)
    ui.checkBox_hardware_trigger.clicked.connect(hardware_trigger_check_box_clicked)
    ui.pushButton_triggerOnce.clicked.connect(software_trigger_once)

    # 绑定立体匹配按钮（如果UI中有的话）
    if hasattr(ui, 'pushButton_stereo_match'):
        ui.pushButton_stereo_match.clicked.connect(manual_stereo_match)
    cam_button_group = QButtonGroup(mainWindow)
    cam_button_group.addButton(ui.checkBox_1, 0)
    cam_button_group.addButton(ui.checkBox_2, 1)
    cam_button_group.addButton(ui.checkBox_3, 2)
    cam_button_group.addButton(ui.checkBox_4, 3) 

    cam_button_group.setExclusive(False)
    cam_button_group.buttonClicked.connect(cam_check_box_clicked)

    raio_button_group = QButtonGroup(mainWindow)
    raio_button_group.addButton(ui.radioButton_continuous, 0)
    raio_button_group.addButton(ui.radioButton_trigger, 1)
    raio_button_group.buttonClicked.connect(radio_button_clicked)

    win_display_handles.append(ui.widget_display1.winId())
    win_display_handles.append(ui.widget_display2.winId())
    win_display_handles.append(ui.widget_display3.winId())
    win_display_handles.append(ui.widget_display4.winId())

    mainWindow.show()
    enum_devices()
    enable_ui_controls()

    # 初始化立体视觉
    init_stereo_vision()

    app.exec_()

    close_devices()

    # ch:反初始化SDK | en: finalize SDK
    MvCamera.MV_CC_Finalize()

    sys.exit()
