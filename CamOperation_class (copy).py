# -- coding: utf-8 --
import threading
import time
import sys
import ctypes
import os
from ctypes import *

import platform

if platform.system() == "Windows":
    libc = ctypes.cdll.msvcrt
else:
    libc = ctypes.cdll.LoadLibrary("libc.so.6")

sys.path.append(os.getenv('MVCAM_COMMON_RUNENV') + "/Samples/Python/MvImport")
from MvCameraControl_class import *

class CameraOperation():

    def __init__(self,obj_cam,st_device_list,n_connect_num=0,b_open_device=False,b_start_grabbing = False,h_thread_handle=None,\
                b_thread_opened=False,st_frame_info=None,b_save_bmp=False,b_save_jpg=False,buf_save_image=None):
        self.cam = None  # 确保 cam 属性被初始化

        self.obj_cam = obj_cam
        self.st_device_list = st_device_list
        self.n_connect_num = n_connect_num
        self.b_open_device = b_open_device
        self.b_start_grabbing = b_start_grabbing
        self.b_thread_opened = b_thread_opened
        self.st_frame_info = MV_FRAME_OUT_INFO_EX()
        self.b_save_bmp = b_save_bmp
        self.b_save_jpg = b_save_jpg
        self.buf_save_image = buf_save_image
        self.buf_save_image_len = 0
        self.h_thread_handle = h_thread_handle
        self.buf_lock = threading.Lock()  # 取图和存图的buffer锁
        self.exit_flag = 0
        self.frame_count = 0
        self.lost_frame_count = 0

        # 触发相关的新增属性
        self.trigger_frame_count = 0  # 触发后的帧计数
        self.last_trigger_frame_count = 0  # 上次触发时的帧计数
        self.trigger_image_captured = False  # 触发图像是否已捕获
        self.trigger_buf_save_image = None  # 专用于保存触发图像的缓冲区
        self.trigger_st_frame_info = MV_FRAME_OUT_INFO_EX()  # 触发图像的帧信息
        self.trigger_buf_lock = threading.Lock()  # 触发图像缓冲区锁
        self.waiting_for_trigger_frame = False  # 是否正在等待触发帧
        self.is_hardware_trigger_mode = False  # 是否为硬件触发模式
        self.hardware_trigger_enabled = False  # 硬件触发是否启用


    

    # 转为16进制字符串
    def to_hex_str(self, num):
        chaDic = {10: 'a', 11: 'b', 12: 'c', 13: 'd', 14: 'e', 15: 'f'}
        hexStr = ""
        if num < 0:
            num = num + 2**32
        while num >= 16:
            digit = num % 16
            hexStr = chaDic.get(digit, str(digit)) + hexStr
            num //= 16
        hexStr = chaDic.get(num, str(num)) + hexStr
        return hexStr

    # 打开相机
    def open_device(self):
        if self.b_open_device is False:
            # ch:选择设备并创建句柄 | en:Select device and create handle
            nConnectionNum = int(self.n_connect_num)
            stDeviceList = cast(self.st_device_list.pDeviceInfo[int(nConnectionNum)], POINTER(MV_CC_DEVICE_INFO)).contents
            self.obj_cam = MvCamera()
            ret = self.obj_cam.MV_CC_CreateHandle(stDeviceList)
            if ret != 0:
                self.obj_cam.MV_CC_DestroyHandle()
                return ret

            ret = self.obj_cam.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)
            if ret != 0:
                self.b_open_device = False
                self.b_thread_opened = False
                return ret
            self.b_open_device = True
            self.b_thread_opened = False

            # ch:探测网络最佳包大小(只对GigE相机有效) | en:Detection network optimal package size(It only works for the GigE camera)
            if stDeviceList.nTLayerType == MV_GIGE_DEVICE:
                nPacketSize = self.obj_cam.MV_CC_GetOptimalPacketSize()
                if int(nPacketSize) > 0:
                    ret = self.obj_cam.MV_CC_SetIntValue("GevSCPSPacketSize",nPacketSize)
                    if ret != 0:
                        print("warning: set packet size fail! ret[0x%x]" % ret)
                else:
                    print("warning: packet size is invalid[%d]" % nPacketSize)

            stBool = c_bool(False)
            ret =self.obj_cam.MV_CC_GetBoolValue("AcquisitionFrameRateEnable", stBool)
            if ret != 0:
                print("warning: get acquisition frame rate enable fail! ret[0x%x]" % ret)

            # ch:设置触发模式为off | en:Set trigger mode as off
            ret = self.obj_cam.MV_CC_SetEnumValueByString("TriggerMode", "Off")
            if ret != 0:
                print("warning: set trigger mode off fail! ret[0x%x]" % ret)
            return 0
        return 0

    # 开始取图
    def start_grabbing(self, n_index,  win_handle):
        if not self.b_start_grabbing and self.b_open_device:
            ret = self.obj_cam.MV_CC_StartGrabbing()
            if ret != 0:
                self.b_start_grabbing = False
                return ret
            self.b_start_grabbing = True
            print("start grabbing " + str(n_index) + "successfully!")
            try:
                self.exit_flag = threading.Event()
                self.h_thread_handle = threading.Thread(target=CameraOperation.work_thread, args=(self, n_index, win_handle, self.exit_flag))
                self.h_thread_handle.start()
                self.b_thread_opened = True
            except TypeError:
                print('error: unable to start thread')
                self.b_start_grabbing = False
            return 0
        return MV_E_CALLORDER

    # 停止取图
    def stop_grabbing(self):
        if (self.b_start_grabbing is True) and (self.b_open_device is True):
            # 退出线程
            if self.b_thread_opened:
                self.exit_flag.set()
                self.h_thread_handle.join()
                self.b_thread_opened = False
            ret = self.obj_cam.MV_CC_StopGrabbing()
            if ret != 0:
                return ret
            self.b_start_grabbing = False
            return 0
        return MV_E_CALLORDER

    # 关闭相机
    def close_device(self):
        if self.b_open_device:
            #退出线程
            if self.b_thread_opened:
                self.exit_flag.set()
                self.h_thread_handle.join()
                self.b_thread_opened = False
            if self.b_start_grabbing:
                ret = self.obj_cam.MV_CC_StopGrabbing()
                if ret != 0:
                    return ret
                self.b_start_grabbing = False
            ret = self.obj_cam.MV_CC_CloseDevice()
            if ret != 0:
                return ret
                
        # ch:销毁句柄 | Destroy handle
        self.obj_cam.MV_CC_DestroyHandle()
        self.b_open_device = False
        return 0

    # 设置触发模式
    def set_trigger_mode(self, trigger_mode):
        if True == self.b_open_device:
            if "continuous" == trigger_mode:
                ret = self.obj_cam.MV_CC_SetEnumValueByString("TriggerMode","Off")
                if ret != 0:
                    return ret
                return 0
            if "triggermode" == trigger_mode:
                ret = self.obj_cam.MV_CC_SetEnumValueByString("TriggerMode","On")
                if ret != 0:
                    return ret
                return 0

    def start_trigger_grabbing(self):
        """启动触发模式的采集（等待触发信号）"""
        if not self.b_open_device or self.obj_cam is None:
            print(f"Camera {self.n_connect_num}: 设备未打开或相机对象为空")
            return -1
        try:
            if not self.b_start_grabbing:
                ret = self.obj_cam.MV_CC_StartGrabbing()
                if ret == 0:
                    self.b_start_grabbing = True
                    print(f"Camera {self.n_connect_num}: 触发采集已启动，等待触发信号")
                else:
                    print(f"Camera {self.n_connect_num}: 启动采集失败 ret=0x{ret:x}")
                return ret
            else:
                print(f"Camera {self.n_connect_num}: 采集已经在运行")
                return 0
        except Exception as e:
            print(f"Camera {self.n_connect_num}: start_trigger_grabbing error: {e}")
            return -1

    def start_continuous_grabbing(self):
        """启动连续模式的采集"""
        if not self.b_open_device or self.obj_cam is None:
            return -1
        try:
            if not self.b_start_grabbing:
                ret = self.obj_cam.MV_CC_StartGrabbing()
                if ret == 0:
                    self.b_start_grabbing = True
                    print(f"Camera {self.n_connect_num}: 连续采集已启动")
                return ret
            return 0
        except Exception as e:
            print(f"Camera {self.n_connect_num}: start_continuous_grabbing error: {e}")
            return -1

    def wait_for_trigger_frame(self, timeout_ms=5000):
        """
        等待硬件触发图像
        """
        if self.obj_cam is None:
            print("相机对象未初始化")
            return None

        stOutFrame = MV_FRAME_OUT()
        memset(byref(stOutFrame), 0, sizeof(stOutFrame))

        ret = self.obj_cam.MV_CC_GetImageBuffer(stOutFrame, timeout_ms)
        if ret == 0:
            # 成功获取图像
            return stOutFrame
        else:
            print("未收到触发图像")
            return None

    # 设置触发源
    def set_trigger_source(self, trigger_source, line_source="Line0"):
        if self.b_open_device is True:
            if "software" == trigger_source:
                ret = self.obj_cam.MV_CC_SetEnumValueByString("TriggerSource", "Software")
                if ret != 0:
                    return ret
                return 0
            else:
                # 硬件触发，支持多种输入线
                ret = self.obj_cam.MV_CC_SetEnumValueByString("TriggerSource", line_source)
                if ret != 0:
                    return ret
                return 0
        return -1



    # 设置触发极性
    def set_trigger_activation(self, activation="RisingEdge"):
        if self.b_open_device is True:
            # activation 可以是 "RisingEdge", "FallingEdge", "LevelHigh", "LevelLow"
            ret = self.obj_cam.MV_CC_SetEnumValueByString("TriggerActivation", activation)
            return ret
        return -1

    # 设置触发延时（微秒）
    def set_trigger_delay(self, delay_us):
        if self.b_open_device is True:
            ret = self.obj_cam.MV_CC_SetFloatValue("TriggerDelay", float(delay_us))
            return ret
        return -1

    # 获取触发状态
    def get_trigger_status(self):
        if self.b_open_device is True:
            # 获取当前触发源
            stParam = MVCC_ENUMVALUE()
            ret = self.obj_cam.MV_CC_GetEnumValue("TriggerSource", stParam)
            if ret == 0:
                return stParam.nCurValue
        return -1

    # 软触发一次 - 增强版本，确保只捕获一帧
    # def trigger_once(self):
    #     if self.b_open_device is True:
    #         # 获取触发缓冲区锁
    #         self.trigger_buf_lock.acquire()
    #         try:
    #             # 重置触发状态
    #             self.trigger_image_captured = False
    #             self.waiting_for_trigger_frame = True
    #             self.last_trigger_frame_count = self.frame_count
    #
    #             # 执行软件触发
    #             ret = self.obj_cam.MV_CC_SetCommandValue("TriggerSoftware")
    #
    #             if ret == 0:
    #                 print(f"Camera {self.n_connect_num}: Software trigger executed, waiting for frame...")
    #             else:
    #                 self.waiting_for_trigger_frame = False
    #
    #                 print(f"Camera {self.n_connect_num}: Trigger failed with error code {self.to_hex_str(ret)}")
    #
    #             return ret
    #         finally:
    #             self.trigger_buf_lock.release()
    #     return -1

    def trigger_once(self):
        if not self.b_open_device:
            return -1

        self.trigger_buf_lock.acquire()
        try:
            self.trigger_image_captured = False
            self.waiting_for_trigger_frame = True
            self.last_trigger_frame_count = self.frame_count
            ret = self.obj_cam.MV_CC_SetCommandValue("TriggerSoftware")
            if ret == 0:
                print(f"Camera {self.n_connect_num}: Software trigger sent")
            else:
                self.waiting_for_trigger_frame = False
                print(f"Camera {self.n_connect_num}: Trigger failed {self.to_hex_str(ret)}")
            return ret
        finally:
            self.trigger_buf_lock.release()

    def is_trigger_image_ready(self):
        """检查触发图像是否已准备好"""
        return self.trigger_image_captured

    def get_trigger_image_data(self):
        """获取触发图像数据"""
        if not self.trigger_image_captured:
            return None, None

        self.trigger_buf_lock.acquire()
        try:
            if self.trigger_buf_save_image is None:
                return None, None

            # 返回图像数据的副本和帧信息
            frame_info_copy = MV_FRAME_OUT_INFO_EX()
            libc.memcpy(byref(frame_info_copy), byref(self.trigger_st_frame_info), sizeof(MV_FRAME_OUT_INFO_EX))

            # 创建图像数据的副本
            image_data_copy = (c_ubyte * self.trigger_st_frame_info.nFrameLen)()
            libc.memcpy(byref(image_data_copy), byref(self.trigger_buf_save_image), self.trigger_st_frame_info.nFrameLen)

            return image_data_copy, frame_info_copy
        finally:
            self.trigger_buf_lock.release()

    def clear_trigger_image(self):
        """清空触发图像缓存"""
        self.trigger_buf_lock.acquire()
        try:
            self.trigger_image_captured = False
            self.waiting_for_trigger_frame = False
            if self.trigger_buf_save_image is not None:
                del self.trigger_buf_save_image
                self.trigger_buf_save_image = None
            print(f"Camera {self.n_connect_num}: Trigger image cache cleared")
        finally:
            self.trigger_buf_lock.release()

    def wait_for_trigger_image(self, timeout_ms=2000):
        """等待触发图像准备就绪"""
        import time
        start_time = time.time() * 1000

        while not self.trigger_image_captured:
            current_time = time.time() * 1000
            if current_time - start_time > timeout_ms:
                print(f"Camera {self.n_connect_num}: Timeout waiting for trigger image")
                return False
            time.sleep(0.01)  # 10ms检查间隔

        print(f"Camera {self.n_connect_num}: Trigger image ready after {current_time - start_time:.1f}ms")
        return True

    def enable_hardware_trigger_detection(self):
        """启用硬件触发检测"""
        self.hardware_trigger_enabled = True
        self.is_hardware_trigger_mode = True
        print(f"Camera {self.n_connect_num}: Hardware trigger detection enabled")

    def disable_hardware_trigger_detection(self):
        """禁用硬件触发检测"""
        self.hardware_trigger_enabled = False
        self.is_hardware_trigger_mode = False
        print(f"Camera {self.n_connect_num}: Hardware trigger detection disabled")

    def prepare_for_hardware_trigger(self):
        """准备接收硬件触发"""
        if self.is_hardware_trigger_mode:
            self.trigger_buf_lock.acquire()
            try:
                self.trigger_image_captured = False
                self.waiting_for_trigger_frame = True
                self.last_trigger_frame_count = self.frame_count
                print(f"Camera {self.n_connect_num}: Ready for hardware trigger")
            finally:
                self.trigger_buf_lock.release()

    def set_exposure_time(self, str_value):
        if self.b_open_device is True:
            self.obj_cam.MV_CC_SetEnumValue("ExposureAuto", 0)
            time.sleep(0.2)
            ret = self.obj_cam.MV_CC_SetFloatValue("ExposureTime", float(str_value))
            if ret != 0:
                print('show error', 'set exposure time fail! ret = ' + self.to_hex_str(ret))
                return ret
        return 0

    def set_gain(self, str_value):
        if self.b_open_device is True:
            self.obj_cam.MV_CC_SetEnumValue("GainAuto", 0)
            time.sleep(0.2)
            ret = self.obj_cam.MV_CC_SetFloatValue("Gain", float(str_value))
            if ret != 0:
                print('show error', 'set gain fail! ret = ' + self.to_hex_str(ret))
                return ret
        return 0

    def set_frame_rate(self, str_value):
        if self.b_open_device is True:
            ret = self.obj_cam.MV_CC_SetFloatValue("AcquisitionFrameRate", float(str_value))
            return ret
        return 0

    # 取图线程函数
    def work_thread(self, n_index, win_handle, exit_flag):
        stOutFrame = MV_FRAME_OUT()
        memset(byref(stOutFrame), 0, sizeof(stOutFrame))

        while not exit_flag.is_set():
            ret = self.obj_cam.MV_CC_GetImageBuffer(stOutFrame, 1000)

            # 采集线程里拿到帧后
            if self.waiting_for_trigger_frame:
                self.trigger_image_captured = True
                self.waiting_for_trigger_frame = False
                print(f"Camera {self.n_connect_num}: Trigger frame received")

            if 0 == ret:
                # 增加帧计数
                self.frame_count += 1

                # 检查是否是触发后的第一帧
                is_trigger_frame = False

                # 软件触发模式：检查是否是触发后的第一帧
                if self.waiting_for_trigger_frame and self.frame_count > self.last_trigger_frame_count:
                    is_trigger_frame = True

                # 硬件触发模式：检测新帧到达（在硬件触发模式下，每个新帧都可能是触发帧）
                elif (self.is_hardware_trigger_mode and self.hardware_trigger_enabled and
                      not self.trigger_image_captured and self.frame_count > self.last_trigger_frame_count):
                    # 在硬件触发模式下，如果有新帧到达且还没有捕获触发图像，则认为是触发帧
                    is_trigger_frame = True
                    self.waiting_for_trigger_frame = True

                # 拷贝图像和图像信息到常规缓冲区
                self.buf_lock.acquire()
                if self.buf_save_image_len < stOutFrame.stFrameInfo.nFrameLen:
                    if self.buf_save_image is not None:
                        del self.buf_save_image
                        self.buf_save_image = None
                    self.buf_save_image = (c_ubyte * stOutFrame.stFrameInfo.nFrameLen)()
                    self.buf_save_image_len = stOutFrame.stFrameInfo.nFrameLen

                libc.memcpy(byref(self.st_frame_info), byref(stOutFrame.stFrameInfo), sizeof(MV_FRAME_OUT_INFO_EX))
                libc.memcpy(byref(self.buf_save_image), stOutFrame.pBufAddr, self.st_frame_info.nFrameLen)
                self.buf_lock.release()

                # 如果是触发帧，同时保存到触发专用缓冲区
                if is_trigger_frame and not self.trigger_image_captured:
                    self.trigger_buf_lock.acquire()
                    try:
                        # 为触发图像分配缓冲区
                        if self.trigger_buf_save_image is None or len(self.trigger_buf_save_image) < stOutFrame.stFrameInfo.nFrameLen:
                            if self.trigger_buf_save_image is not None:
                                del self.trigger_buf_save_image
                            self.trigger_buf_save_image = (c_ubyte * stOutFrame.stFrameInfo.nFrameLen)()

                        # 复制触发图像数据
                        libc.memcpy(byref(self.trigger_st_frame_info), byref(stOutFrame.stFrameInfo), sizeof(MV_FRAME_OUT_INFO_EX))
                        libc.memcpy(byref(self.trigger_buf_save_image), stOutFrame.pBufAddr, stOutFrame.stFrameInfo.nFrameLen)

                        # 标记触发图像已捕获
                        self.trigger_image_captured = True
                        self.waiting_for_trigger_frame = False
                        self.trigger_frame_count = self.frame_count

                        print(f"Camera {self.n_connect_num}: Trigger frame captured (frame #{self.frame_count})")

                    finally:
                        self.trigger_buf_lock.release()

                        # 如果已经捕获了触发帧，停止进一步的图像处理
                if self.trigger_image_captured:
                    self.obj_cam.MV_CC_StopGrabbing()
                    self.b_start_grabbing = False
                    break

                # 使用Display接口显示图像
                stDisplayParam = MV_DISPLAY_FRAME_INFO()
                memset(byref(stDisplayParam), 0, sizeof(stDisplayParam))
                stDisplayParam.hWnd = int(win_handle)
                stDisplayParam.nWidth = stOutFrame.stFrameInfo.nWidth
                stDisplayParam.nHeight = stOutFrame.stFrameInfo.nHeight
                stDisplayParam.enPixelType = stOutFrame.stFrameInfo.enPixelType
                stDisplayParam.pData = stOutFrame.pBufAddr
                stDisplayParam.nDataLen = stOutFrame.stFrameInfo.nFrameLen
                self.obj_cam.MV_CC_DisplayOneFrame(stDisplayParam)

                # 释放缓存
                self.obj_cam.MV_CC_FreeImageBuffer(stOutFrame)
            else:
                #print("Camera[" + str(n_index) + "]:no data, ret = "+self.to_hex_str(ret))
                continue

    # 存BMP图像
    def save_bmp(self, save_dir="saved_images"):
        # 检查图像缓冲区是否有效
        if self.buf_save_image is None:
            print("Error: No image data available for saving")
            return -1

        # 获取缓存锁
        self.buf_lock.acquire()

        # 确保保存目录存在
        import os
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        # 生成带时间戳的文件名
        import time
        timestamp = int(time.time() * 1000)
        filename = f"cam{self.n_connect_num}_{timestamp}_{self.st_frame_info.nFrameNum}.bmp"
        file_path = os.path.join(save_dir, filename)
        c_file_path = file_path.encode('ascii')

        stSaveParam = MV_SAVE_IMAGE_TO_FILE_PARAM_EX()
        stSaveParam.enPixelType = self.st_frame_info.enPixelType  # ch:相机对应的像素格式 | en:Camera pixel type
        stSaveParam.nWidth = self.st_frame_info.nWidth  # ch:相机对应的宽 | en:Width
        stSaveParam.nHeight = self.st_frame_info.nHeight  # ch:相机对应的高 | en:Height
        stSaveParam.nDataLen = self.st_frame_info.nFrameLen
        stSaveParam.pData = cast(self.buf_save_image, POINTER(c_ubyte))
        stSaveParam.enImageType = MV_Image_Bmp  # ch:需要保存的图像类型 | en:Image format to save
        stSaveParam.pcImagePath = ctypes.create_string_buffer(c_file_path)
        stSaveParam.iMethodValue = 1
        ret = self.obj_cam.MV_CC_SaveImageToFileEx(stSaveParam)

        self.buf_lock.release()

        if ret == 0:
            print(f"Image saved successfully: {file_path}")
        else:
            print(f"Failed to save image: {file_path}, error code: {self.to_hex_str(ret)}")

        return ret
