# -*- coding: utf-8 -*-
import threading
import time
import sys
import ctypes
import os
from ctypes import *
import platform
import numpy as np
import cv2

if platform.system() == "Windows":
    libc = ctypes.cdll.msvcrt
else:
    libc = ctypes.cdll.LoadLibrary("libc.so.6")

sys.path.append(os.getenv('MVCAM_COMMON_RUNENV') + "/Samples/Python/MvImport")
from MvCameraControl_class import *

class CameraOperation:
    def __init__(self, obj_cam, st_device_list, n_connect_num=0):
        self.obj_cam            = obj_cam
        self.st_device_list     = st_device_list
        self.n_connect_num      = n_connect_num
        self.b_open_device      = False
        self.b_start_grabbing   = False
        self.h_thread_handle    = None
        self.exit_flag          = threading.Event()
        self.buf_save_image     = None
        self.buf_save_image_len = 0
        self.st_frame_info      = MV_FRAME_OUT_INFO_EX()
        self.buf_lock           = threading.Lock()
        self.frame_count        = 0

    # ------------------------------------------------------------------
    # 基础功能：打开 / 关闭 / 开始 / 停止
    # ------------------------------------------------------------------
    def open_device(self):
        if self.b_open_device:
            return 0
        nConnectionNum = int(self.n_connect_num)
        stDeviceList   = cast(self.st_device_list.pDeviceInfo[nConnectionNum],
                              POINTER(MV_CC_DEVICE_INFO)).contents
        self.obj_cam   = MvCamera()
        ret = self.obj_cam.MV_CC_CreateHandle(stDeviceList)
        if ret != 0:
            self.obj_cam.MV_CC_DestroyHandle()
            return ret

        ret = self.obj_cam.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)
        if ret != 0:
            return ret
        self.b_open_device = True

        # GigE 最优包大小
        if stDeviceList.nTLayerType == MV_GIGE_DEVICE:
            nPacket = self.obj_cam.MV_CC_GetOptimalPacketSize()
            if nPacket > 0:
                self.obj_cam.MV_CC_SetIntValue("GevSCPSPacketSize", nPacket)

        # 默认先关触发，连续流
        self.set_continuous_mode()
        return 0

    def close_device(self):
        if self.b_open_device:
            self.stop_grabbing()
            self.obj_cam.MV_CC_CloseDevice()
            self.obj_cam.MV_CC_DestroyHandle()
            self.b_open_device = False
        return 0

    def start_grabbing(self, n_index, win_handle):
        if self.b_start_grabbing or not self.b_open_device:
            return MV_E_CALLORDER
        ret = self.obj_cam.MV_CC_StartGrabbing()
        if ret != 0:
            return ret
        self.b_start_grabbing = True
        self.exit_flag.clear()
        self.h_thread_handle = threading.Thread(
            target=self._work_thread, args=(n_index, win_handle))
        self.h_thread_handle.start()
        return 0

    def stop_grabbing(self):
        if not self.b_start_grabbing:
            return MV_E_CALLORDER
        self.exit_flag.set()
        if self.h_thread_handle and self.h_thread_handle.is_alive():
            self.h_thread_handle.join()
        self.obj_cam.MV_CC_StopGrabbing()
        self.b_start_grabbing = False
        return 0

    # ------------------------------------------------------------------
    # 触发模式切换（一键调用）
    # ------------------------------------------------------------------
    def set_continuous_mode(self):
        """设置连续采集模式（仅设置模式，不启动采集）"""
        if not self.b_open_device or self.obj_cam is None:
            return -1
        try:
            # 停止采集
            if self.b_start_grabbing:
                self.obj_cam.MV_CC_StopGrabbing()
                self.b_start_grabbing = False

            # 统一使用字符串接口并补全参数
            self.obj_cam.MV_CC_SetEnumValueByString("TriggerSelector", "FrameStart")
            self.obj_cam.MV_CC_SetEnumValueByString("AcquisitionMode", "Continuous")
            self.obj_cam.MV_CC_SetEnumValueByString("TriggerMode", "Off")
            try:
                self.obj_cam.MV_CC_SetBoolValue("TriggerCacheEnable", False)
            except:
                pass
            try:
                self.obj_cam.MV_CC_SetIntValue("TriggerDelay", 0)
            except:
                pass

            print(f"Camera {self.n_connect_num}: 连续采集模式已设置（等待开始采集）")
            return 0
        except Exception as e:
            print(f"Camera {self.n_connect_num}: set_continuous_mode error: {e}")
            return -1

    def set_software_trigger(self):
        """设置软件触发模式（仅设置模式，不启动采集）"""
        if not self.b_open_device or self.obj_cam is None:
            return -1
        try:
            # 停止采集
            if self.b_start_grabbing:
                self.obj_cam.MV_CC_StopGrabbing()
                self.b_start_grabbing = False

            # 统一使用字符串接口并补全参数
            self.obj_cam.MV_CC_SetEnumValueByString("TriggerSelector", "FrameStart")
            self.obj_cam.MV_CC_SetEnumValueByString("AcquisitionMode", "Continuous")
            self.obj_cam.MV_CC_SetEnumValueByString("TriggerMode", "On")
            self.obj_cam.MV_CC_SetEnumValueByString("TriggerSource", "Software")
            try:
                self.obj_cam.MV_CC_SetBoolValue("TriggerCacheEnable", False)
            except:
                pass
            try:
                self.obj_cam.MV_CC_SetIntValue("TriggerDelay", 0)
            except:
                pass

            print(f"Camera {self.n_connect_num}: 软件触发模式已设置（等待开始采集）")
            return 0
        except Exception as e:
            print(f"Camera {self.n_connect_num}: set_software_trigger error: {e}")
            return -1

    def verify_trigger_config(self):
        """读取并打印当前相机的触发关键参数，便于确认设置是否生效"""
        try:
            def get_enum_str(key):
                try:
                    val = MVCC_ENUMVALUE()
                    if self.obj_cam.MV_CC_GetEnumValue(key, val) == 0:
                        return val.nCurValue
                except:
                    return None
            mode = get_enum_str("TriggerMode")
            src  = get_enum_str("TriggerSource")
            print(f"Camera {self.n_connect_num}: TriggerMode={mode}, TriggerSource={src}")
        except Exception as e:
            print(f"Camera {self.n_connect_num}: verify_trigger_config error: {e}")


    def set_hardware_trigger(self):
        """设置硬件触发模式（仅设置模式，不启动采集）"""
        if not self.b_open_device or self.obj_cam is None:
            print(f"Camera {self.n_connect_num}: 设备未打开或相机对象为空")
            return -1
        try:
            # 停止采集
            if self.b_start_grabbing:
                ret = self.obj_cam.MV_CC_StopGrabbing()
                if ret != 0:
                    print(f"Camera {self.n_connect_num}: 停止采集失败 ret=0x{ret:x}")
                self.b_start_grabbing = False

            # 设置硬件触发模式 - 使用字符串方式更兼容
            ret = self.obj_cam.MV_CC_SetEnumValueByString("TriggerMode", "On")
            if ret != 0:
                print(f"Camera {self.n_connect_num}: 设置触发模式失败 ret=0x{ret:x}")
                return ret

            ret = self.obj_cam.MV_CC_SetEnumValueByString("TriggerSource", "Line0")
            if ret != 0:
                print(f"Camera {self.n_connect_num}: 设置硬件触发源失败 ret=0x{ret:x}")
                return ret

            # 设置触发极性为上升沿
            ret = self.obj_cam.MV_CC_SetEnumValueByString("TriggerActivation", "RisingEdge")
            if ret != 0:
                print(f"Camera {self.n_connect_num}: 设置触发极性失败 ret=0x{ret:x}")
                # 不返回错误，某些相机可能不支持此参数

            # 设置防抖时间（可选）
            try:
                self.obj_cam.MV_CC_SetIntValue("LineDebouncerTime", 50)
            except:
                pass  # 某些相机可能不支持此参数

            print(f"Camera {self.n_connect_num}: 硬件触发模式已设置 (Line0 RisingEdge，等待开始采集)")
            return 0  # 返回成功
        except Exception as e:
            print(f"Camera {self.n_connect_num}: set_hardware_trigger error: {e}")
            return -1

    def start_trigger_grabbing(self):
        """启动触发模式的采集（等待触发信号）"""
        if not self.b_open_device or self.obj_cam is None:
            print(f"Camera {self.n_connect_num}: 设备未打开或相机对象为空")
            return -1
        try:
            if not self.b_start_grabbing:
                ret = self.obj_cam.MV_CC_StartGrabbing()
                if ret == 0:
                    self.b_start_grabbing = True
                    print(f"Camera {self.n_connect_num}: 触发采集已启动，等待触发信号")
                else:
                    print(f"Camera {self.n_connect_num}: 启动采集失败 ret=0x{ret:x}")
                return ret
            else:
                print(f"Camera {self.n_connect_num}: 采集已经在运行")
                return 0
        except Exception as e:
            print(f"Camera {self.n_connect_num}: start_trigger_grabbing error: {e}")
            return -1

    def start_continuous_grabbing(self):
        """启动连续模式的采集"""
        if not self.b_open_device or self.obj_cam is None:
            return -1
        try:
            if not self.b_start_grabbing:
                ret = self.obj_cam.MV_CC_StartGrabbing()
                if ret == 0:
                    self.b_start_grabbing = True
                    print(f"Camera {self.n_connect_num}: 连续采集已启动")
                return ret
            return 0
        except Exception as e:
            print(f"Camera {self.n_connect_num}: start_continuous_grabbing error: {e}")
            return -1

    # ------------------------------------------------------------------
    # 软触发一次
    # ------------------------------------------------------------------
    def trigger_once(self):
        """执行一次软件触发"""
        if not self.b_open_device or self.obj_cam is None:
            return -1
        try:
            ret = self.obj_cam.MV_CC_SetCommandValue("TriggerSoftware")
            if ret != 0:
                print(f"Camera {self.n_connect_num}: 软件触发失败 ret=0x{ret:x}")
            return ret
        except Exception as e:
            print(f"Camera {self.n_connect_num}: trigger_once error: {e}")
            return -1

    # ------------------------------------------------------------------
    # 参数设置
    # ------------------------------------------------------------------
    def set_exposure_time(self, val):
        if self.b_open_device:
            self.obj_cam.MV_CC_SetEnumValue("ExposureAuto", 0)
            return self.obj_cam.MV_CC_SetFloatValue("ExposureTime", float(val))
        return -1

    def set_gain(self, val):
        if self.b_open_device:
            self.obj_cam.MV_CC_SetEnumValue("GainAuto", 0)
            return self.obj_cam.MV_CC_SetFloatValue("Gain", float(val))
        return -1

    def set_frame_rate(self, val):
        if self.b_open_device:
            return self.obj_cam.MV_CC_SetFloatValue("AcquisitionFrameRate", float(val))
        return -1

    # ------------------------------------------------------------------
    # 取图线程
    # ------------------------------------------------------------------
    def _work_thread(self, n_index, win_handle):
        stOutFrame = MV_FRAME_OUT()
        memset(byref(stOutFrame), 0, sizeof(stOutFrame))
        while not self.exit_flag.is_set():
            ret = self.obj_cam.MV_CC_GetImageBuffer(stOutFrame, 1000)
            if ret == 0:
                self.frame_count += 1
                # 拷贝到本地 buffer
                self.buf_lock.acquire()
                if self.buf_save_image_len < stOutFrame.stFrameInfo.nFrameLen:
                    if self.buf_save_image:
                        del self.buf_save_image
                    self.buf_save_image = (c_ubyte * stOutFrame.stFrameInfo.nFrameLen)()
                    self.buf_save_image_len = stOutFrame.stFrameInfo.nFrameLen
                libc.memcpy(byref(self.st_frame_info), byref(stOutFrame.stFrameInfo),
                            sizeof(MV_FRAME_OUT_INFO_EX))
                libc.memcpy(byref(self.buf_save_image), stOutFrame.pBufAddr,
                            stOutFrame.stFrameInfo.nFrameLen)
                self.buf_lock.release()

                # 显示
                stDisplay = MV_DISPLAY_FRAME_INFO()
                memset(byref(stDisplay), 0, sizeof(stDisplay))
                stDisplay.hWnd       = int(win_handle)
                stDisplay.nWidth     = stOutFrame.stFrameInfo.nWidth
                stDisplay.nHeight    = stOutFrame.stFrameInfo.nHeight
                stDisplay.enPixelType= stOutFrame.stFrameInfo.enPixelType
                stDisplay.pData      = stOutFrame.pBufAddr
                stDisplay.nDataLen   = stOutFrame.stFrameInfo.nFrameLen
                self.obj_cam.MV_CC_DisplayOneFrame(stDisplay)

                self.obj_cam.MV_CC_FreeImageBuffer(stOutFrame)
            else:
                # 参考opencamera.py的触发超时处理
                # 检查多种可能的触发超时错误码
                if ret in [0x80040001, 0x80000007, 0x80000008]:  # 各种超时错误码
                    continue  # 触发模式下超时是正常的，继续等待
                # print("get one frame fail, ret[0x%x]" % ret)
                continue

    # ------------------------------------------------------------------
    # 保存 BMP
    # ------------------------------------------------------------------
    def save_bmp(self, save_dir="saved_images"):
        if self.buf_save_image is None:
            return -1
        self.buf_lock.acquire()
        try:
            os.makedirs(save_dir, exist_ok=True)
            timestamp = int(time.time()*1000)
            path = os.path.join(save_dir, f"cam{self.n_connect_num}_{timestamp}.bmp")
            c_path = path.encode('ascii')

            stSave = MV_SAVE_IMAGE_TO_FILE_PARAM_EX()
            memset(byref(stSave), 0, sizeof(stSave))
            stSave.enPixelType = self.st_frame_info.enPixelType
            stSave.nWidth      = self.st_frame_info.nWidth
            stSave.nHeight     = self.st_frame_info.nHeight
            stSave.nDataLen    = self.st_frame_info.nFrameLen
            stSave.pData       = cast(self.buf_save_image, POINTER(c_ubyte))
            stSave.enImageType = MV_Image_Bmp
            stSave.pcImagePath = ctypes.create_string_buffer(c_path)
            stSave.iMethodValue = 1
            ret = self.obj_cam.MV_CC_SaveImageToFileEx(stSave)
            if ret == 0:
                print("Image saved:", path)
            else:
                print("Save bmp fail, ret =", hex(ret))
            return ret
        finally:
            self.buf_lock.release()

    # 工具：错误码转 hex
    def to_hex_str(self, num):
        return hex(num & 0xffffffff)