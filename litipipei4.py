# -*- coding: utf-8 -*-
import sys
import time
import threading
import numpy as np
import cv2
import os
from queue import Queue

from PyQt5.QtWidgets import *
from PyQt5.QtGui import QTextCursor, QPixmap, QImage
from PyQt5.QtCore import QTimer, pyqtSignal, QObject
from CamOperation_class import CameraOperation
from MvCameraControl_class import *
from MvErrorDefine_const import *
from CameraParams_header import *
from PyUIMultipleCameras import Ui_MainWindow
import ctypes


# 将返回的错误码转换为十六进制显示
def ToHexStr(num):
    chaDic = {10: 'a', 11: 'b', 12: 'c', 13: 'd', 14: 'e', 15: 'f'}
    hexStr = ""
    if num < 0:
        num = num + 2 ** 32
    while num >= 16:
        digit = num % 16
        hexStr = chaDic.get(digit, str(digit)) + hexStr
        num //= 16
    hexStr = chaDic.get(num, str(num)) + hexStr
    return hexStr


# Decoding Characters
def decoding_char(c_ubyte_value):
    c_char_p_value = ctypes.cast(c_ubyte_value, ctypes.c_char_p)
    try:
        decode_str = c_char_p_value.value.decode('gbk')  # Chinese characters
    except UnicodeDecodeError:
        decode_str = str(c_char_p_value.value)
    return decode_str


# 立体视觉处理类
class StereoVisionProcessor(QObject):
    # 定义信号用于更新UI
    depth_result_signal = pyqtSignal(np.ndarray, np.ndarray, np.ndarray)  # disparity, depth, depth_color

    def __init__(self):
        super().__init__()
        self.calibration_params = None
        self.rectification_maps = None
        self.Q_matrix = None

        # 立体匹配器
        self.stereo_matcher = self.create_stereo_matcher()

        # 图像队列用于同步
        self.left_image_queue = Queue(maxsize=2)
        self.right_image_queue = Queue(maxsize=2)

        # 处理标志
        self.processing = False
        self.process_thread = None

        # 存储最新的深度图
        self.latest_depth = None
        self.latest_disparity = None
        self.depth_lock = threading.Lock()

        # 初始化标定参数（请替换为你的实际参数）
        self.init_calibration_params()

    def init_calibration_params(self):
        """初始化标定参数 - 请替换为你的实际标定数据"""
        # 左相机内参矩阵 [fx, 0, cx; 0, fy, cy; 0, 0, 1]
        mtx_left = np.array([[2349.599303017811, 0, 1221.0886985822297],
                             [0, 2347.04087849075, 1021.2297950652342],
                             [0, 0, 1]], dtype=np.float32)

        # 右相机内参矩阵
        mtx_right = np.array([[2347.7080632127045, 0, 1219.3168735048296],
                              [0, 2347.528871737054, 1010.4282529230558],
                              [0, 0, 1]], dtype=np.float32)

        # 畸变参数 [k1, k2, p1, p2, k3]
        dist_left = np.array([-0.0677072434895743, 0.16840134514589222, -0.00013311325437381048, -0.0010946605867930416, -0.19743756744235746], dtype=np.float32)
        dist_right = np.array([-0.07691265481784593, 0.22703604995053306, 0.00015959041360151294, -0.0011580170802655745, -0.3538743014783903], dtype=np.float32)

        # 旋转矩阵（右相机相对于左相机）
        R = np.array([[0.9998155478060632, -0.0008436404567199599, -0.01918746038540903],
                      [0.0008172502236645439, 0.9999987094440285, -0.0013831891951225166],
                      [0.019188602537281753, 0.0013672531065493177, 0.9998149469534894]], dtype=np.float32)

        # 平移向量（右相机相对于左相机，单位：mm）
        T = np.array([-100.87040766250446, 0.06079718879422688, -1.3284405860235702], dtype=np.float32)

        # 图像尺寸 (width, height)
        image_size = (2448, 2048)

        self.set_calibration_params(mtx_left, dist_left, mtx_right, dist_right, R, T, image_size)

    def set_calibration_params(self, mtx_left, dist_left, mtx_right, dist_right, R, T, image_size):
        """设置标定参数"""
        self.calibration_params = {
            'camera_matrix_left': mtx_left.astype(np.float64),
            'camera_matrix_right': mtx_right.astype(np.float64),
            'distortion_left': dist_left.astype(np.float64),
            'distortion_right': dist_right.astype(np.float64),
            'rotation_matrix': R.astype(np.float64),
            'translation_vector': T.astype(np.float64),
            'image_size': image_size
        }
        self.compute_rectification_maps()
    
    def compute_rectification_maps(self):
        """计算立体校正映射"""
        if self.calibration_params is None:
            return

        # 立体校正
        R1, R2, P1, P2, Q, validPixROI1, validPixROI2 = cv2.stereoRectify(
            self.calibration_params['camera_matrix_left'],
            self.calibration_params['distortion_left'],
            self.calibration_params['camera_matrix_right'],
            self.calibration_params['distortion_right'],
            self.calibration_params['image_size'],
            self.calibration_params['rotation_matrix'],
            self.calibration_params['translation_vector'],
            alpha=0
        )

        # 确保矩阵类型一致
        R1 = R1.astype(np.float64)
        R2 = R2.astype(np.float64)
        P1 = P1.astype(np.float64)
        P2 = P2.astype(np.float64)
        Q = Q.astype(np.float64)

        # 计算校正映射
        map1_left, map2_left = cv2.initUndistortRectifyMap(
            self.calibration_params['camera_matrix_left'],
            self.calibration_params['distortion_left'],
            R1, P1, self.calibration_params['image_size'], cv2.CV_16SC2)
 
        map1_right, map2_right = cv2.initUndistortRectifyMap(
            self.calibration_params['camera_matrix_right'],
            self.calibration_params['distortion_right'],
            R2, P2, self.calibration_params['image_size'], cv2.CV_16SC2)

        self.rectification_maps = {
            'map1_left': map1_left, 'map2_left': map2_left,
            'map1_right': map1_right, 'map2_right': map2_right
        }
        self.Q_matrix = Q

        print("立体校正映射计算完成")

    def create_stereo_matcher(self):
        """创建立体匹配器"""
        stereo = cv2.StereoSGBM_create(
            minDisparity=0,
            numDisparities=96,  # 必须是16的倍数
            blockSize=5,
            P1=8 * 3 * 5 ** 2,
            P2=32 * 3 * 5 ** 2,
            disp12MaxDiff=1,
            uniquenessRatio=10,
            speckleWindowSize=100,
            speckleRange=32,
            preFilterCap=63,
            mode=cv2.STEREO_SGBM_MODE_SGBM_3WAY
        )
        return stereo

    def update_stereo_params(self, min_disp=0, num_disp=96, block_size=5):
        """更新立体匹配参数"""
        self.stereo_matcher.setMinDisparity(min_disp)
        self.stereo_matcher.setNumDisparities(num_disp)
        self.stereo_matcher.setBlockSize(block_size)

    def rectify_images(self, left_img, right_img):
        """校正图像"""
        if self.rectification_maps is None:
            return left_img, right_img

        left_rectified = cv2.remap(left_img,
                                   self.rectification_maps['map1_left'],
                                   self.rectification_maps['map2_left'],
                                   cv2.INTER_LINEAR)

        right_rectified = cv2.remap(right_img,
                                    self.rectification_maps['map1_right'],
                                    self.rectification_maps['map2_right'],
                                    cv2.INTER_LINEAR)

        return left_rectified, right_rectified

    def compute_disparity(self, left_img, right_img):
        """计算视差图"""
        # 转换为灰度图
        if len(left_img.shape) == 3:
            left_gray = cv2.cvtColor(left_img, cv2.COLOR_BGR2GRAY)
            right_gray = cv2.cvtColor(right_img, cv2.COLOR_BGR2GRAY)
        else:
            left_gray = left_img
            right_gray = right_img

        # 计算视差
        disparity = self.stereo_matcher.compute(left_gray, right_gray).astype(np.float32) / 16.0

        # 视差后处理
        disparity = self.post_process_disparity(disparity)

        return disparity

    def post_process_disparity(self, disparity):
        """视差后处理"""
        # 去除无效视差
        disparity[disparity <= 0] = 0
        disparity[disparity >= 96] = 0

        # 中值滤波去噪
        disparity = cv2.medianBlur(disparity.astype(np.uint8), 5).astype(np.float32)

        return disparity

    def compute_depth(self, disparity):
        """从视差计算深度"""
        if self.Q_matrix is None:
            return None, None

        # 重投影到3D空间
        points_3d = cv2.reprojectImageTo3D(disparity, self.Q_matrix)

        # 提取深度信息（Z坐标）
        depth = points_3d[:, :, 2]

        # 过滤无效深度值
        depth[depth <= 0] = 0
        depth[depth > 10000] = 0  # 限制最大深度为10米

        return depth, points_3d

    def process_stereo_pair(self, left_img, right_img):
        """处理立体图像对"""
        try:
            # 图像校正
            left_rectified, right_rectified = self.rectify_images(left_img, right_img)

            # 计算视差
            disparity = self.compute_disparity(left_rectified, right_rectified)

            # 计算深度
            depth, points_3d = self.compute_depth(disparity)

            if depth is not None:
                # 更新最新的深度图
                with self.depth_lock:
                    self.latest_depth = depth.copy()
                    self.latest_disparity = disparity.copy()

                # 创建深度图的彩色可视化
                depth_norm = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX)
                depth_color = cv2.applyColorMap(depth_norm.astype(np.uint8), cv2.COLORMAP_JET)
                
                # 保存深度图和视差图到文件
                self.save_depth_image(depth_color)

                # 发射信号更新UI
                self.depth_result_signal.emit(disparity, depth, depth_color)

            return {
                'left_rectified': left_rectified,
                'right_rectified': right_rectified,
                'disparity': disparity,
                'depth': depth,
                'points_3d': points_3d
            }
        except Exception as e:
            print(f"立体处理错误: {e}")
            return None
            
    def save_depth_image(self, depth_image):
        save_dir = "saved_depth_images"
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        filename = os.path.join(save_dir, f"depth_{int(time.time()*1000)}.png")
        cv2.imwrite(filename, depth_image)
        print(f"深度图像已保存: {filename}")


            
    def add_image_pair(self, left_img, right_img):
        """添加图像对到处理队列"""
        # 清空队列保持最新图像
        while not self.left_image_queue.empty():
            self.left_image_queue.get()
        while not self.right_image_queue.empty():
            self.right_image_queue.get()

        # 添加新图像
        if left_img is not None and right_img is not None:
            self.left_image_queue.put(left_img.copy())
            self.right_image_queue.put(right_img.copy())

    def get_latest_depth(self):
        """获取最新的深度图"""
        with self.depth_lock:
            if self.latest_depth is not None:
                return self.latest_depth.copy()
        return None

    def start_processing(self):
        """启动处理线程"""
        if not self.processing:
            self.processing = True
            self.process_thread = threading.Thread(target=self.processing_loop)
            self.process_thread.daemon = True
            self.process_thread.start()

    def stop_processing(self):
        """停止处理"""
        self.processing = False
        if self.process_thread:
            self.process_thread.join(timeout=1)

    def processing_loop(self):
        """处理循环"""
        while self.processing:
            try:
                # 从队列获取图像
                if not self.left_image_queue.empty() and not self.right_image_queue.empty():
                    left_img = self.left_image_queue.get()
                    right_img = self.right_image_queue.get()

                    # 处理立体图像对
                    self.process_stereo_pair(left_img, right_img)

                time.sleep(0.033)  # 约30FPS
            except Exception as e:
                print(f"立体处理循环错误: {e}")


# 扩展CameraOperation类以获取图像数据
class ExtendedCameraOperation(CameraOperation):
    def __init__(self, cam_obj, device_list, device_index):
        super().__init__(cam_obj, device_list, device_index)
        self.latest_image = None
        self.image_lock = threading.Lock()
        self.image_callback = None

    def set_image_callback(self, callback):
        """设置图像回调函数"""
        self.image_callback = callback

    def get_latest_image(self):
        """获取最新的图像"""
        with self.image_lock:
            if self.latest_image is not None:
                return self.latest_image.copy()
        return None

    def update_image(self, image):
        """更新最新图像"""
        with self.image_lock:
            self.latest_image = image.copy()
        
        # 调用回调函数
        if self.image_callback:
            self.image_callback(image)

    # 重写图像处理方法以确保图像数据被正确更新
    def work_thread(self, n_index, win_handle, exit_flag):
        """重写工作线程以更新图像数据"""
        try:
            # 调用父类方法
            super().work_thread(n_index, win_handle, exit_flag)

        except Exception as e:
            print(f"图像处理错误: {e}")

    def process_image_data(self):
        """处理图像数据并更新最新图像"""
        try:
            # 如果有图像数据，更新最新图像
            if hasattr(self, 'buf_save_image') and self.buf_save_image is not None:
                frame_info = self.st_frame_info

                # 根据像素格式转换图像
                if frame_info.enPixelType == 0x01080001:  # Mono8
                    # 单通道图像
                    image = np.frombuffer(self.buf_save_image, dtype=np.uint8)
                    image = image.reshape((frame_info.nHeight, frame_info.nWidth))
                    # 转换为BGR格式
                    image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                elif frame_info.enPixelType == 0x02180014:  # RGB8
                    # 彩色图像
                    image = np.frombuffer(self.buf_save_image, dtype=np.uint8)
                    image = image.reshape((frame_info.nHeight, frame_info.nWidth, 3))
                    # 转换RGB到BGR
                    image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
                else:
                    # 默认处理为单通道
                    image = np.frombuffer(self.buf_save_image, dtype=np.uint8)
                    image = image.reshape((frame_info.nHeight, frame_info.nWidth))
                    image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)

                # 更新图像
                self.update_image(image)

        except Exception as e:
            print(f"图像数据处理错误: {e}")


if __name__ == "__main__":

    global deviceList
    deviceList = MV_CC_DEVICE_INFO_LIST()

    global cam_checked_list
    cam_checked_list = []

    global obj_cam_operation
    obj_cam_operation = []

    global win_display_handles
    win_display_handles = []

    global valid_number
    valid_number = 0

    global b_is_open
    b_is_open = False

    global b_is_grab
    b_is_grab = False

    global b_is_trigger
    b_is_trigger = False

    global b_is_software_trigger
    b_is_software_trigger = False

    global b_is_hardware_trigger
    b_is_hardware_trigger = False

    global hardware_trigger_line
    hardware_trigger_line = "Line0"

    global hardware_trigger_activation
    hardware_trigger_activation = "RisingEdge"

    # 立体视觉相关全局变量
    global stereo_processor
    stereo_processor = None

    global stereo_enabled
    stereo_enabled = False

    global stereo_thread
    stereo_thread = None

    global stereo_running
    stereo_running = False

    # 触发后立体匹配相关变量
    global trigger_stereo_match
    trigger_stereo_match = False

    global last_trigger_images
    last_trigger_images = {"left": None, "right": None}

    # ch:初始化SDK | en: initialize SDK
    MvCamera.MV_CC_Initialize()


    # print info in ui
    def print_text(str_info):
        ui.textEdit.append(str_info)  # 使用 append 代替手动操作光标


    # 初始化立体视觉
    def init_stereo_vision():
        global stereo_processor
        try:
            stereo_processor = StereoVisionProcessor()
            # 连接信号
            stereo_processor.depth_result_signal.connect(update_depth_display)
            print_text("立体视觉系统初始化完成")
            return True
        except Exception as e:
            print_text(f"立体视觉初始化失败: {e}")
            return False


    def update_depth_display(disparity, depth, depth_color):
        """更新深度显示"""
        try:
            # 这里可以将深度图显示到额外的窗口或控件中
            # 由于原UI可能没有专门的深度显示控件，这里只打印信息
            if depth is not None:
                valid_depth = depth[depth > 0]
                if len(valid_depth) > 0:
                    min_depth = np.min(valid_depth)
                    max_depth = np.max(valid_depth)
                    mean_depth = np.mean(valid_depth)
                    print_text(f"深度信息 - 最小: {min_depth:.1f}mm, 最大: {max_depth:.1f}mm, 平均: {mean_depth:.1f}mm")
                    
                    # 可选：保存深度图
                    # cv2.imwrite(f"depth_{int(time.time())}.png", depth_color)
        except Exception as e:
            print_text(f"深度显示更新错误: {e}")


    def get_camera_image(camera_index):
        """获取指定相机的图像"""
        global obj_cam_operation
        try:
            if (camera_index < len(obj_cam_operation) and
                    obj_cam_operation[camera_index] != 0 and
                    hasattr(obj_cam_operation[camera_index], 'get_latest_image')):
                return obj_cam_operation[camera_index].get_latest_image()
        except Exception as e:
            print_text(f"获取相机{camera_index}图像失败: {e}")
        return None


    def on_camera_image_update(camera_index, image):
        """相机图像更新回调"""
        global stereo_processor, stereo_enabled
        
        if stereo_enabled and stereo_processor is not None:
            # 如果是左相机（索引0）或右相机（索引1）
            if camera_index == 0:
                # 获取右相机图像
                right_image = get_camera_image(1)
                if right_image is not None:
                    stereo_processor.add_image_pair(image, right_image)
            elif camera_index == 1:
                # 获取左相机图像
                left_image = get_camera_image(0)
                if left_image is not None:
                    stereo_processor.add_image_pair(left_image, image)


    def stereo_image_acquisition_loop():
        """立体图像获取循环"""
        global stereo_running, stereo_processor, obj_cam_operation

        while stereo_running:
            try:
                # 获取左右相机图像（假设相机0是左相机，相机1是右相机）
                left_image = get_camera_image(0)
                right_image = get_camera_image(1)

                if left_image is not None and right_image is not None and stereo_processor is not None:
                    # 添加到立体处理队列
                    stereo_processor.add_image_pair(left_image, right_image)

                time.sleep(0.033)  # 约30FPS

            except Exception as e:
                print_text(f"立体图像获取错误: {e}")
                time.sleep(0.1)


    def start_stereo_vision():
        """启动立体视觉"""
        global stereo_enabled, stereo_processor, stereo_thread, stereo_running

        if not b_is_grab:
            print_text("请先启动相机采集")
            return

        # 检查是否至少选择了两个相机
        selected_count = sum(1 for i in range(4) if i < len(obj_cam_operation) and obj_cam_operation[i] != 0)
        if selected_count < 2:
            print_text("立体视觉需要至少两个相机")
            return

        if stereo_processor is None:
            if not init_stereo_vision():
                return

        # 启动立体处理
        stereo_processor.start_processing()

        # 启动图像获取线程
        if not stereo_running:
            stereo_running = True
            stereo_thread = threading.Thread(target=stereo_image_acquisition_loop)
            stereo_thread.daemon = True
            stereo_thread.start()

        stereo_enabled = True
        print_text("立体视觉已启动")


    def stop_stereo_vision():
        """停止立体视觉"""
        global stereo_enabled, stereo_processor, stereo_running

        stereo_enabled = False
        stereo_running = False

        if stereo_processor:
            stereo_processor.stop_processing()

        print_text("立体视觉已停止")


    # ch:枚举相机 | en:enum devices
    def enum_devices():
        global deviceList
        global valid_number
        deviceList = MV_CC_DEVICE_INFO_LIST()
        n_layer_type = (MV_GIGE_DEVICE | MV_USB_DEVICE
                        | MV_GENTL_GIGE_DEVICE | MV_GENTL_CAMERALINK_DEVICE
                        | MV_GENTL_CXP_DEVICE | MV_GENTL_XOF_DEVICE)
        ret = MvCamera.MV_CC_EnumDevicesEx2(n_layer_type, deviceList, '', SortMethod_SerialNumber)
        if ret != 0:
            str_error = "Enum devices fail! ret = :" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", str_error, QMessageBox.Ok)
            return ret

        if deviceList.nDeviceNum == 0:
            QMessageBox.warning(mainWindow, "Info", "Find no device", QMessageBox.Ok)
            return ret
        print_text("Find %d devices!" % deviceList.nDeviceNum)

        valid_number = 0
        for i in range(0, 4):
            if (i < deviceList.nDeviceNum) is True:
                serial_number = ""
                model_name = ""
                mvcc_dev_info = cast(deviceList.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
                if mvcc_dev_info.nTLayerType == MV_GIGE_DEVICE or mvcc_dev_info.nTLayerType == MV_GENTL_GIGE_DEVICE:
                    print("\ngige device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    nip1 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0xff000000) >> 24)
                    nip2 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x00ff0000) >> 16)
                    nip3 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x0000ff00) >> 8)
                    nip4 = (mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x000000ff)
                    print("current ip: %d.%d.%d.%d " % (nip1, nip2, nip3, nip4))

                    for per in mvcc_dev_info.SpecialInfo.stGigEInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)

                elif mvcc_dev_info.nTLayerType == MV_USB_DEVICE:
                    print("\nu3v device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_CAMERALINK_DEVICE:
                    print("\nCML device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stCMLInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stCMLInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stCMLInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_CXP_DEVICE:
                    print("\nCXP device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stCXPInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stCXPInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stCXPInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)
                elif mvcc_dev_info.nTLayerType == MV_GENTL_XOF_DEVICE:
                    print("\nXoF device: [%d]" % i)
                    user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stXoFInfo.chUserDefinedName)
                    model_name = decoding_char(mvcc_dev_info.SpecialInfo.stXoFInfo.chModelName)
                    print("device user define name: " + user_defined_name)
                    print("device model name: " + model_name)

                    for per in mvcc_dev_info.SpecialInfo.stXoFInfo.chSerialNumber:
                        if per == 0:
                            break
                        serial_number = serial_number + chr(per)
                    print("user serial number: " + serial_number)

                button_by_id = cam_button_group.button(i)
                button_by_id.setText("(" + serial_number + ")" + model_name)
                button_by_id.setEnabled(True)
                valid_number = valid_number + 1
            else:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(False)


    def cam_check_box_clicked():
        global cam_checked_list
        cam_checked_list = []
        for i in range(0, 4):
            button = cam_button_group.button(i)
            if button.isChecked() is True:
                cam_checked_list.append(True)
            else:
                cam_checked_list.append(False)


    def enable_ui_controls():
        global b_is_open
        global b_is_grab
        global b_is_trigger
        global b_is_software_trigger
        global b_is_hardware_trigger

        ui.pushButton_enum.setEnabled(not b_is_open)
        ui.pushButton_open.setEnabled(not b_is_open)
        ui.pushButton_close.setEnabled(b_is_open)
        result1 = False if b_is_grab else b_is_open
        result2 = b_is_open if b_is_grab else False
        ui.pushButton_startGrab.setEnabled(result1)
        ui.pushButton_stopGrab.setEnabled(result2)
        ui.pushButton_saveImg.setEnabled(result2)
        ui.radioButton_continuous.setEnabled(b_is_open)
        ui.radioButton_trigger.setEnabled(b_is_open)
        ui.pushButton_setParams.setEnabled(b_is_open)
        ui.lineEdit_gain.setEnabled(b_is_open)
        ui.lineEdit_frameRate.setEnabled(b_is_open)
        ui.lineEdit_exposureTime.setEnabled(b_is_open)

        # 触发相关控件
        result3 = b_is_open if b_is_trigger else False
        ui.pushButton_triggerOnce.setEnabled(b_is_software_trigger and result3)
        ui.checkBox_software_trigger.setEnabled(b_is_trigger)
        ui.checkBox_hardware_trigger.setEnabled(b_is_trigger)

        # 硬件触发配置控件
        ui.comboBox_trigger_line.setEnabled(b_is_hardware_trigger and b_is_trigger)
        ui.comboBox_trigger_activation.setEnabled(b_is_hardware_trigger and b_is_trigger)

        # 立体匹配按钮
        ui.pushButton_stereo_match.setEnabled(result2)  # 需要在采集状态下才能进行立体匹配


    def open_devices():
        global deviceList
        global obj_cam_operation
        global b_is_open
        global valid_number
        global cam_checked_list
        b_checked = 0
        if b_is_open is True:
            return

        if len(cam_checked_list) <= 0:
            print_text("please select a camera !")
            return
        obj_cam_operation = []
        for i in range(0, 4):
            if cam_checked_list[i] is True:
                b_checked = True
                camObj = MvCamera()
                # 使用扩展的CameraOperation类
                cam_op = ExtendedCameraOperation(camObj, deviceList, i)
                # 设置图像回调
                cam_op.set_image_callback(lambda img, idx=i: on_camera_image_update(idx, img))
                obj_cam_operation.append(cam_op)
                ret = obj_cam_operation[i].open_device()
                if 0 != ret:
                    obj_cam_operation.pop()
                    print_text("open cam %d fail ret[0x%x]" % (i, ret))
                    continue
                else:
                    b_is_open = True
            else:
                obj_cam_operation.append(0)
        if b_checked is False:
            print_text("please select a camera !")
            return
        if b_is_open is False:
            print_text("no camera opened successfully !")
            return
        else:
            ui.radioButton_continuous.setChecked(True)
            enable_ui_controls()

        for i in range(0, 4):
            if (i < valid_number) is True:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(not b_is_open)


    def software_trigger_check_box_clicked():
        global obj_cam_operation
        global b_is_software_trigger
        global b_is_hardware_trigger

        if (ui.checkBox_software_trigger.isChecked()) is True:
            b_is_software_trigger = True
            b_is_hardware_trigger = False
            ui.checkBox_hardware_trigger.setChecked(False)

            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_trigger_source("software")
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger source: software  fail! ret = ' + ToHexStr(ret))
                    else:
                        print_text('camera' + str(i) + ' set to software trigger mode')
        else:
            b_is_software_trigger = False

        enable_ui_controls()

    def hardware_trigger_check_box_clicked():
        global obj_cam_operation
        global b_is_hardware_trigger
        global b_is_software_trigger
        global hardware_trigger_line
        global hardware_trigger_activation

        if (ui.checkBox_hardware_trigger.isChecked()) is True:
            b_is_hardware_trigger = True
            b_is_software_trigger = False
            ui.checkBox_software_trigger.setChecked(False)

            # 获取当前选择的触发线和极性
            hardware_trigger_line = ui.comboBox_trigger_line.currentText()
            hardware_trigger_activation = ui.comboBox_trigger_activation.currentText()

            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    # 设置硬件触发源
                    ret = obj_cam_operation[i].set_trigger_source("hardware", hardware_trigger_line)
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger source: hardware fail! ret = ' + ToHexStr(ret))
                        continue

                    # 设置触发极性
                    ret = obj_cam_operation[i].set_trigger_activation(hardware_trigger_activation)
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger activation fail! ret = ' + ToHexStr(ret))
                        continue

                    print_text('camera' + str(i) + f' set to hardware trigger mode ({hardware_trigger_line}, {hardware_trigger_activation})')
        else:
            b_is_hardware_trigger = False

        enable_ui_controls()


    def radio_button_clicked(button):
        global obj_cam_operation
        global b_is_trigger
        button_id = raio_button_group.id(button)
        if (button_id == 0) is True:
            b_is_trigger = False
            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_trigger_mode("continuous")
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger mode: continuous fail! ret = ' + ToHexStr(ret))
            enable_ui_controls()

        else:
            b_is_trigger = True
            for i in range(0, 4):
                if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                    ret = obj_cam_operation[i].set_trigger_mode("triggermode")
                    if 0 != ret:
                        print_text('camera' + str(i) + ' set trigger on fail! ret = ' + ToHexStr(ret))
            enable_ui_controls()


    def close_devices():
        global b_is_open
        global obj_cam_operation
        global valid_number

        if b_is_open is False:
            return
        if b_is_grab is True:
            stop_grabbing()

        # 停止立体视觉
        stop_stereo_vision()

        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].close_device()
                if 0 != ret:
                    print_text('camera' + str(i) + ' close device fail! ret = ' + ToHexStr(ret))

            if i < valid_number:
                button_by_id = cam_button_group.button(i)
                button_by_id.setEnabled(True)
        b_is_open = False
        enable_ui_controls()


    def start_grabbing():
        global obj_cam_operation
        global win_display_handles
        global b_is_open
        global b_is_grab

        if (not b_is_open) or (b_is_grab is True):
            return

        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].start_grabbing(i, win_display_handles[i])
                if 0 != ret:
                    print_text('camera' + str(i) + ' start grabbing fail! ret = ' + ToHexStr(ret))
                b_is_grab = True
        enable_ui_controls()

        # 自动启动立体视觉（如果有两个或以上相机）
        selected_count = sum(1 for i in range(4) if i < len(obj_cam_operation) and obj_cam_operation[i] != 0)
        if selected_count >= 2:
            print_text("检测到多个相机，可以启动立体视觉")
            start_stereo_vision()


    def stop_grabbing():
        global b_is_grab
        global obj_cam_operation
        global b_is_open

        if (not b_is_open) or (b_is_grab is False):
            return

        # 停止立体视觉
        stop_stereo_vision()

        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].stop_grabbing()
                if 0 != ret:
                    print_text('camera' + str(i) + ' stop grabbing fail!ret = ' + ToHexStr(ret))
                b_is_grab = False
        enable_ui_controls()


    def save_bmp():
        global b_is_grab
        global obj_cam_operation

        if b_is_grab is False:
            print_text("Camera is not grabbing, no image to save.")
            return

        save_dir = "saved_images"
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                # 检查图像缓冲区是否有有效的图像数据
                if obj_cam_operation[i].buf_save_image is None:
                    print_text(f'camera{str(i)} has no image data to save.')
                    continue

                # 确保图像缓冲区中有有效的图像数据
                if obj_cam_operation[i].st_frame_info is None:
                    print_text(f'camera{str(i)} has no frame info to save.')
                    continue

                # 获取图像数据
                image_data = obj_cam_operation[i].buf_save_image
                frame_info = obj_cam_operation[i].st_frame_info

                # 检查图像尺寸是否有效
                if frame_info.nHeight <= 0 or frame_info.nWidth <= 0:
                    print_text(f'camera{str(i)} frame info has invalid dimensions: '
                               f'height={frame_info.nHeight}, width={frame_info.nWidth}')
                    continue

                # 转换图像格式（根据像素类型）
                if frame_info.enPixelType == PixelType_Gvsp_BayerGR8:
                    # Bayer 格式转换为 BGR
                    image_data = np.frombuffer(image_data, dtype=np.uint8)
                    image_data = image_data.reshape((frame_info.nHeight, frame_info.nWidth))
                    image_data = cv2.cvtColor(image_data, cv2.COLOR_BAYER_GR2BGR)
                elif frame_info.enPixelType == PixelType_Gvsp_Mono8:
                    # 单通道图像
                    image_data = np.frombuffer(image_data, dtype=np.uint8)
                    image_data = image_data.reshape((frame_info.nHeight, frame_info.nWidth))
                    image_data = cv2.cvtColor(image_data, cv2.COLOR_GRAY2BGR)
                elif frame_info.enPixelType == PixelType_Gvsp_RGB8_Packed:
                    # 彩色图像
                    image_data = np.frombuffer(image_data, dtype=np.uint8)
                    image_data = image_data.reshape((frame_info.nHeight, frame_info.nWidth, 3))
                else:
                    print_text(f'camera{str(i)} unsupported pixel type: {frame_info.enPixelType}')
                    continue

                # 打印调试信息
                print_text(f"camera{str(i)} image shape: {image_data.shape}, dtype: {image_data.dtype}")

                # 使用 OpenCV 保存图像
                filename = os.path.join(save_dir, f"camera_{i}_frame_{frame_info.nFrameNum}.bmp")
                if cv2.imwrite(filename, image_data):
                    print_text(f'camera{str(i)} save bmp successfully to {filename}')
                else:
                    print_text(f'camera{str(i)} save bmp fail!')


    def is_float(str_value):
        try:
            float(str_value)
            return True
        except ValueError:
            return False


    def set_parameters():
        global obj_cam_operation
        global b_is_open
        if b_is_open is False:
            return

        frame_rate = ui.lineEdit_frameRate.text()
        exposure_time = ui.lineEdit_exposureTime.text()
        gain = ui.lineEdit_gain.text()

        if is_float(frame_rate) is False or is_float(exposure_time) is False or is_float(gain) is False:
            print_text("parameters is valid, please check")
            return

        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].set_exposure_time(exposure_time)
                if ret != 0:
                    print_text('camera' + str(i) + ' Set exposure time failed ret:' + ToHexStr(ret))
                ret = obj_cam_operation[i].set_gain(gain)
                if ret != 0:
                    print_text('camera' + str(i) + ' Set gain failed ret:' + ToHexStr(ret))
                ret = obj_cam_operation[i].set_frame_rate(frame_rate)
                if ret != 0:
                    print_text('camera' + str(i) + ' set acquisition frame rate failed ret:' + ToHexStr(ret))


    def software_trigger_once():
        global last_trigger_images

        # 清空上次触发的图像
        last_trigger_images = {"left": None, "right": None}

        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                ret = obj_cam_operation[i].trigger_once()
                if ret != 0:
                    print_text('camera' + str(i) + 'TriggerSoftware failed ret:' + ToHexStr(ret))
                else:
                    print_text('camera' + str(i) + ' software trigger executed')

        # 等待一段时间让图像采集完成，然后获取触发后的图像
        QTimer.singleShot(500, capture_trigger_images)  # 500ms后获取图像

    def capture_trigger_images():
        """捕获触发后的图像"""
        global last_trigger_images, obj_cam_operation

        try:
            # 获取左相机图像（假设相机0是左相机）
            if len(obj_cam_operation) > 0 and obj_cam_operation[0] != 0:
                left_image = get_camera_image(0)
                if left_image is not None:
                    last_trigger_images["left"] = left_image.copy()
                    print_text("Left camera image captured")

            # 获取右相机图像（假设相机1是右相机）
            if len(obj_cam_operation) > 1 and obj_cam_operation[1] != 0:
                right_image = get_camera_image(1)
                if right_image is not None:
                    last_trigger_images["right"] = right_image.copy()
                    print_text("Right camera image captured")

            # 检查是否成功获取了双目图像
            if last_trigger_images["left"] is not None and last_trigger_images["right"] is not None:
                print_text("Stereo image pair captured successfully")
                # 如果启用了自动立体匹配，则自动进行立体匹配
                if trigger_stereo_match:
                    perform_trigger_stereo_match()
            else:
                print_text("Failed to capture stereo image pair")

        except Exception as e:
            print_text(f"Error capturing trigger images: {e}")

    def perform_trigger_stereo_match():
        """对触发后的图像进行立体匹配"""
        global last_trigger_images, stereo_processor

        try:
            if (last_trigger_images["left"] is None or
                last_trigger_images["right"] is None):
                print_text("No stereo image pair available for matching")
                return

            if stereo_processor is None:
                if not init_stereo_vision():
                    print_text("Failed to initialize stereo vision processor")
                    return

            print_text("Starting stereo matching on triggered images...")

            # 进行立体匹配
            result = stereo_processor.process_stereo_pair(
                last_trigger_images["left"],
                last_trigger_images["right"]
            )

            if result is not None:
                print_text("Stereo matching completed successfully")

                # 保存立体匹配结果
                save_stereo_results(result)

                # 显示深度信息
                if result['depth'] is not None:
                    depth = result['depth']
                    valid_depth = depth[depth > 0]
                    if len(valid_depth) > 0:
                        min_depth = np.min(valid_depth)
                        max_depth = np.max(valid_depth)
                        mean_depth = np.mean(valid_depth)
                        print_text(f"Depth range: {min_depth:.1f}mm - {max_depth:.1f}mm, Mean: {mean_depth:.1f}mm")
            else:
                print_text("Stereo matching failed")

        except Exception as e:
            print_text(f"Error in stereo matching: {e}")

    def save_stereo_results(result):
        """保存立体匹配结果"""
        try:
            save_dir = "stereo_results"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = int(time.time() * 1000)

            # 保存校正后的图像
            if result['left_rectified'] is not None:
                cv2.imwrite(os.path.join(save_dir, f"left_rectified_{timestamp}.png"),
                           result['left_rectified'])

            if result['right_rectified'] is not None:
                cv2.imwrite(os.path.join(save_dir, f"right_rectified_{timestamp}.png"),
                           result['right_rectified'])

            # 保存视差图
            if result['disparity'] is not None:
                disparity_norm = cv2.normalize(result['disparity'], None, 0, 255, cv2.NORM_MINMAX)
                cv2.imwrite(os.path.join(save_dir, f"disparity_{timestamp}.png"),
                           disparity_norm.astype(np.uint8))

            # 保存深度图
            if result['depth'] is not None:
                depth_norm = cv2.normalize(result['depth'], None, 0, 255, cv2.NORM_MINMAX)
                depth_color = cv2.applyColorMap(depth_norm.astype(np.uint8), cv2.COLORMAP_JET)
                cv2.imwrite(os.path.join(save_dir, f"depth_{timestamp}.png"), depth_color)

            print_text(f"Stereo results saved to {save_dir}")

        except Exception as e:
            print_text(f"Error saving stereo results: {e}")

    def manual_stereo_match():
        """手动触发立体匹配"""
        global trigger_stereo_match

        if (last_trigger_images["left"] is None or
            last_trigger_images["right"] is None):
            print_text("No captured images available. Please trigger cameras first.")
            return

        print_text("Manual stereo matching triggered...")
        perform_trigger_stereo_match()

    def update_hardware_trigger_config():
        """更新硬件触发配置"""
        global hardware_trigger_line, hardware_trigger_activation
        global obj_cam_operation, b_is_hardware_trigger

        if not b_is_hardware_trigger:
            return

        # 获取新的配置
        new_line = ui.comboBox_trigger_line.currentText()
        new_activation = ui.comboBox_trigger_activation.currentText()

        # 如果配置没有变化，直接返回
        if (new_line == hardware_trigger_line and
            new_activation == hardware_trigger_activation):
            return

        hardware_trigger_line = new_line
        hardware_trigger_activation = new_activation

        # 应用新配置到所有相机
        for i in range(0, 4):
            if i < len(obj_cam_operation) and obj_cam_operation[i] != 0:
                # 设置硬件触发源
                ret = obj_cam_operation[i].set_trigger_source("hardware", hardware_trigger_line)
                if 0 != ret:
                    print_text('camera' + str(i) + ' update trigger line fail! ret = ' + ToHexStr(ret))
                    continue

                # 设置触发极性
                ret = obj_cam_operation[i].set_trigger_activation(hardware_trigger_activation)
                if 0 != ret:
                    print_text('camera' + str(i) + ' update trigger activation fail! ret = ' + ToHexStr(ret))
                    continue

                print_text('camera' + str(i) + f' updated to ({hardware_trigger_line}, {hardware_trigger_activation})')

    def enable_auto_stereo_match():
        """启用自动立体匹配"""
        global trigger_stereo_match
        trigger_stereo_match = True
        print_text("Auto stereo matching enabled")

    def disable_auto_stereo_match():
        """禁用自动立体匹配"""
        global trigger_stereo_match
        trigger_stereo_match = False
        print_text("Auto stereo matching disabled")

    def get_depth_at_point(x, y):
        """获取指定点的深度值"""
        global stereo_processor
        if stereo_processor:
            depth_map = stereo_processor.get_latest_depth()
            if depth_map is not None and 0 <= x < depth_map.shape[1] and 0 <= y < depth_map.shape[0]:
                depth_value = depth_map[y, x]
                if depth_value > 0:
                    print_text(f"点({x}, {y})的深度: {depth_value:.1f}mm")
                    return depth_value
        print_text(f"无法获取点({x}, {y})的深度信息")
        return None
        
    # 测试函数（已移除重复的主程序入口）
    def test_stereo_processor():
        """测试立体视觉处理器"""
        img = np.random.randint(0, 255, (480, 640), dtype=np.uint8)
        processor = StereoVisionProcessor()
        processor.save_depth_image(img)


    # ch: 初始化app, 绑定控件与函数 | en: Init app, bind ui and api
    app = QApplication(sys.argv)
    mainWindow = QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(mainWindow)
    ui.pushButton_enum.clicked.connect(enum_devices)
    ui.pushButton_open.clicked.connect(open_devices)
    ui.pushButton_close.clicked.connect(close_devices)
    ui.pushButton_startGrab.clicked.connect(start_grabbing)
    ui.pushButton_stopGrab.clicked.connect(stop_grabbing)
    ui.pushButton_saveImg.clicked.connect(save_bmp)
    ui.pushButton_setParams.clicked.connect(set_parameters)
    ui.checkBox_software_trigger.clicked.connect(software_trigger_check_box_clicked)
    ui.checkBox_hardware_trigger.clicked.connect(hardware_trigger_check_box_clicked)
    ui.pushButton_triggerOnce.clicked.connect(software_trigger_once)
    ui.pushButton_stereo_match.clicked.connect(manual_stereo_match)

    # 硬件触发配置变化事件
    ui.comboBox_trigger_line.currentTextChanged.connect(update_hardware_trigger_config)
    ui.comboBox_trigger_activation.currentTextChanged.connect(update_hardware_trigger_config)

    # 添加立体视觉控制按钮（如果UI中有的话）
    # ui.pushButton_startStereo.clicked.connect(start_stereo_vision)
    # ui.pushButton_stopStereo.clicked.connect(stop_stereo_vision)

    cam_button_group = QButtonGroup(mainWindow)
    cam_button_group.addButton(ui.checkBox_1, 0)
    cam_button_group.addButton(ui.checkBox_2, 1)
    cam_button_group.addButton(ui.checkBox_3, 2)
    cam_button_group.addButton(ui.checkBox_4, 3)

    cam_button_group.setExclusive(False)
    cam_button_group.buttonClicked.connect(cam_check_box_clicked)

    raio_button_group = QButtonGroup(mainWindow)
    raio_button_group.addButton(ui.radioButton_continuous, 0)
    raio_button_group.addButton(ui.radioButton_trigger, 1)
    raio_button_group.buttonClicked.connect(radio_button_clicked)

    win_display_handles.append(ui.widget_display1.winId())
    win_display_handles.append(ui.widget_display2.winId())
    win_display_handles.append(ui.widget_display3.winId())
    win_display_handles.append(ui.widget_display4.winId())

    mainWindow.show()
    enum_devices()
    enable_ui_controls()

    # 初始化立体视觉
    init_stereo_vision()

    app.exec_()

    close_devices()

    # ch:反初始化SDK | en: finalize SDK
    MvCamera.MV_CC_Finalize()

    sys.exit()

