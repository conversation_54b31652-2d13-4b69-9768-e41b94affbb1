# -*- coding: utf-8 -*-
import sys
import time
import os

import numpy as np
import cv2
from ctypes import *
import termios
from datetime import datetime
import threading

sys.path.append("/opt/MVS/Samples/64/Python/MvImport")  # 导入相应SDK的库，实际安装位置绝对路径
from MvCameraControl_class import *

# 以下需要配置
# 抓图保存路径，提供给app
grab_dir = "/opt/MVS/Samples/aarch64/Python_2hk/MultipleCameras1/picture/"
DEVICE_USER_ID_FRONT = "user_id_002"
DEVICE_USER_ID_BACK = "user_id_001"

# 其他全局默认值，不用配置
CAM_LIST = []
DEVICE_NUM = 0
GRAB_RUN = True
SERVICE_ID = "200002"
USER_DATA_PY = []


# DEVICE_IDX = None

# 打印设备详情
def printDeviceInfo(deviceList):
    for i in range(0, deviceList.nDeviceNum):
        mvcc_dev_info = cast(deviceList.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
        print("mvcc_dev_info", mvcc_dev_info)
        if mvcc_dev_info.nTLayerType == MV_GIGE_DEVICE:
            print("\ngige device: [%d]" % i)
            strModeName = ""
            for per in mvcc_dev_info.SpecialInfo.stGigEInfo.chModelName:
                strModeName = strModeName + chr(per)
            print("device model name: %s" % strModeName)

            nip1 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0xff000000) >> 24)
            nip2 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x00ff0000) >> 16)
            nip3 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x0000ff00) >> 8)
            nip4 = (mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x000000ff)
            print("current ip: %d.%d.%d.%d\n" % (nip1, nip2, nip3, nip4))
        elif mvcc_dev_info.nTLayerType == MV_USB_DEVICE:
            print("\nu3v device: [%d]" % i)
            strModeName = ""
            for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chModelName:
                if per == 0:
                    break
                strModeName = strModeName + chr(per)
            print("device model name: %s" % strModeName)

            strSerialNumber = ""
            for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                if per == 0:
                    break
                strSerialNumber = strSerialNumber + chr(per)
            print("user serial number: %s" % strSerialNumber)


# 判读图像格式是彩色还是黑白
def IsImageColor(enType):
    dates = {
        PixelType_Gvsp_RGB8_Packed: 'color',
        PixelType_Gvsp_BGR8_Packed: 'color',
        PixelType_Gvsp_YUV422_Packed: 'color',
        PixelType_Gvsp_YUV422_YUYV_Packed: 'color',
        PixelType_Gvsp_BayerGR8: 'color',
        PixelType_Gvsp_BayerRG8: 'color',
        PixelType_Gvsp_BayerGB8: 'color',
        PixelType_Gvsp_BayerBG8: 'color',
        PixelType_Gvsp_BayerGB10: 'color',
        PixelType_Gvsp_BayerGB10_Packed: 'color',
        PixelType_Gvsp_BayerBG10: 'color',
        PixelType_Gvsp_BayerBG10_Packed: 'color',
        PixelType_Gvsp_BayerRG10: 'color',
        PixelType_Gvsp_BayerRG10_Packed: 'color',
        PixelType_Gvsp_BayerGR10: 'color',
        PixelType_Gvsp_BayerGR10_Packed: 'color',
        PixelType_Gvsp_BayerGB12: 'color',
        PixelType_Gvsp_BayerGB12_Packed: 'color',
        PixelType_Gvsp_BayerBG12: 'color',
        PixelType_Gvsp_BayerBG12_Packed: 'color',
        PixelType_Gvsp_BayerRG12: 'color',
        PixelType_Gvsp_BayerRG12_Packed: 'color',
        PixelType_Gvsp_BayerGR12: 'color',
        PixelType_Gvsp_BayerGR12_Packed: 'color',
        PixelType_Gvsp_Mono8: 'mono',
        PixelType_Gvsp_Mono10: 'mono',
        PixelType_Gvsp_Mono10_Packed: 'mono',
        PixelType_Gvsp_Mono12: 'mono',
        PixelType_Gvsp_Mono12_Packed: 'mono'}
    return dates.get(enType, '未知')


# 回调取图采集
def image_callback_main(pData, pFrameInfo, device_idx):
    global USER_DATA_PY
    stFrameInfo = cast(pFrameInfo, POINTER(MV_FRAME_OUT_INFO_EX)).contents

    # print("pUser",pUser,"cast",cast(pUser, POINTER(py_object)))
    # user_data_py = cast(pUser, POINTER(py_object)).contents.value
    # print("user_data_py",user_data_py)
    # cam = user_data_py["cam"]
    # device_idx = user_data_py["device_idx"]

    # print("pUser",pUser)
    # DEVICE_IDX = cast(pUser, POINTER(py_object)).contents.value
    # print("DEVICE_IDX",DEVICE_IDX)
    # print("USER_DATA_PY",USER_DATA_PY,"DEVICE_IDX",DEVICE_IDX)
    # cam = USER_DATA_PY[DEVICE_IDX]["cam"]

    DEVICE_IDX = device_idx
    cam = USER_DATA_PY[DEVICE_IDX]["cam"]

    if stFrameInfo:
        print("get one frame: Width[%d], Height[%d], nFrameNum[%d], enPixelType[%s]" % (stFrameInfo.nWidth,
                                                                                        stFrameInfo.nHeight,
                                                                                        stFrameInfo.nFrameNum,
                                                                                        stFrameInfo.enPixelType))
        time_start = time.time()
        # 图片名格式 image-200001-4-2022-01-18-10-57-03-1642474623702.jpg
        img_name = "image-" + SERVICE_ID + "-" + str(DEVICE_IDX + 1) + "-" + datetime.now().strftime(
            '%Y-%m-%d-%H-%M-%S-%f')
        img_name_tmp = img_name + ".jpeg"
        img_name_jpg = img_name + ".jpg"
        img_path_tmp = os.path.join(grab_dir, img_name_tmp)
        img_path_jpg = os.path.join(grab_dir, img_name_jpg)
        stConvertParam = MV_CC_PIXEL_CONVERT_PARAM()
        memset(byref(stConvertParam), 0, sizeof(stConvertParam))
        if IsImageColor(stFrameInfo.enPixelType) == 'mono':
            print("mono!")
            stConvertParam.enDstPixelType = PixelType_Gvsp_Mono8
            nConvertSize = stFrameInfo.nWidth * stFrameInfo.nHeight
        elif IsImageColor(stFrameInfo.enPixelType) == 'color':
            print("color!")
            stConvertParam.enDstPixelType = PixelType_Gvsp_BGR8_Packed  # opecv要用BGR，不能使用RGB
            nConvertSize = stFrameInfo.nWidth * stFrameInfo.nHeight * 3
        else:
            print("not support!!!")

        img_buffer = (c_ubyte * stFrameInfo.nFrameLen)()

        stConvertParam.nWidth = stFrameInfo.nWidth
        stConvertParam.nHeight = stFrameInfo.nHeight
        stConvertParam.pSrcData = cast(pData, POINTER(c_ubyte))
        stConvertParam.nSrcDataLen = stFrameInfo.nFrameLen
        stConvertParam.enSrcPixelType = stFrameInfo.enPixelType
        stConvertParam.pDstBuffer = (c_ubyte * nConvertSize)()
        stConvertParam.nDstBufferSize = nConvertSize
        print('time cos 1:', time.time() - time_start, 's')
        ret = cam.MV_CC_ConvertPixelType(stConvertParam)
        print('time cos 2:', time.time() - time_start, 's')
        if ret != 0:
            print("convert pixel fail! ret[0x%x]" % ret)
            del stConvertParam.pSrcData
            sys.exit()
        else:
            # print("convert ok!!")
            # 转OpenCV
            # 黑白处理
            if IsImageColor(stFrameInfo.enPixelType) == 'mono':
                img_buffer = (c_ubyte * stConvertParam.nDstLen)()
                memmove(byref(img_buffer), stConvertParam.pDstBuffer, stConvertParam.nDstLen)
                img_buffer = np.frombuffer(img_buffer, count=int(stConvertParam.nDstLen), dtype=np.uint8)
                img_buffer = img_buffer.reshape((stFrameInfo.nHeight, stFrameInfo.nWidth))
                # print("mono ok!!")
            # 彩色处理
            if IsImageColor(stFrameInfo.enPixelType) == 'color':
                img_buffer = (c_ubyte * stConvertParam.nDstLen)()
                memmove(byref(img_buffer), stConvertParam.pDstBuffer, stConvertParam.nDstLen)
                img_buffer = np.frombuffer(img_buffer, count=int(stConvertParam.nDstBufferSize), dtype=np.uint8)
                img_buffer = img_buffer.reshape(stFrameInfo.nHeight, stFrameInfo.nWidth, 3)
                # print("color ok!!")
            print('time cos 3:', time.time() - time_start, 's')

            height, width = img_buffer.shape[0:2]
            img_buffer = cv2.resize(img_buffer, (int(width / 2), int(height / 2)), interpolation=cv2.INTER_AREA)
            print("img_path", img_path_tmp)
            cv2.imwrite(img_path_tmp, img_buffer)
            os.rename(img_path_tmp, img_path_jpg)
            # cv2.imshow('img', img_buffer)
            # cv2.waitKey(10)

            # 下面是模拟摄像机2
            # img_name = "image-"+SERVICE_ID+"-"+str(DEVICE_IDX+2)+"-"+datetime.now().strftime('%Y-%m-%d-%H-%M-%S-%f')
            # img_name_tmp = img_name + ".jpeg"
            # img_name_jpg = img_name + ".jpg"
            # img_path_tmp = os.path.join(grab_dir, img_name_tmp)
            # img_path_jpg = os.path.join(grab_dir, img_name_jpg)
            # cv2.imwrite(img_path_tmp, img_buffer)
            # os.rename(img_path_tmp, img_path_jpg)
        print("")


g_winfun_ctype = CFUNCTYPE
g_st_frame_info = POINTER(MV_FRAME_OUT_INFO_EX)
g_p_data = POINTER(c_ubyte)
FrameInfoCallBack = g_winfun_ctype(None, g_p_data, g_st_frame_info, c_void_p)


def image_callback_0(pData, pFrameInfo, pUser):
    image_callback_main(pData, pFrameInfo, 0)


def image_callback_1(pData, pFrameInfo, pUser):
    image_callback_main(pData, pFrameInfo, 1)


# 因为回调传值会变化，暂时只能用2个回调区别那个摄像头
CALL_BACK_FUN_0 = FrameInfoCallBack(image_callback_0)
CALL_BACK_FUN_1 = FrameInfoCallBack(image_callback_1)


# def press_any_key_exit():
#     fd = sys.stdin.fileno()
#     old_ttyinfo = termios.tcgetattr(fd)
#     new_ttyinfo = old_ttyinfo[:]
#     new_ttyinfo[3] &= ~termios.ICANON
#     new_ttyinfo[3] &= ~termios.ECHO
#     # sys.stdout.write(msg)
#     # sys.stdout.flush()
#     termios.tcsetattr(fd, termios.TCSANOW, new_ttyinfo)
#     try:
#         os.read(fd, 7)
#     except:
#         pass
#     finally:
#         termios.tcsetattr(fd, termios.TCSANOW, old_ttyinfo)

def press_any_key_exit():
    try:
        fd = sys.stdin.fileno()
        old_ttyinfo = termios.tcgetattr(fd)
        new_ttyinfo = old_ttyinfo[:]
        new_ttyinfo[3] &= ~termios.ICANON
        new_ttyinfo[3] &= ~termios.ECHO
        termios.tcsetattr(fd, termios.TCSANOW, new_ttyinfo)
        try:
            os.read(fd, 7)
        except:
            pass
        finally:
            termios.tcsetattr(fd, termios.TCSANOW, old_ttyinfo)
    except termios.error as e:
        print(f"termios error: {e}. Falling back to input().")
        input("Press Enter to exit...")


def add_trigger_event_thread(cam=0, idx=None):
    print("add_trigger_event_thread")
    while True:
        if GRAB_RUN == False:
            break
        ret = cam.MV_CC_SetCommandValue("TriggerSoftware");
        if ret != 0:
            print("TriggerSoftware fail! ret[0x%x]" % ret)
            break
        time.sleep(2)


def start():
    global GRAB_RUN
    global CAM_LIST
    global DEVICE_NUM
    global USER_DATA_PY

    deviceList = MV_CC_DEVICE_INFO_LIST()
    tlayerType = MV_GIGE_DEVICE | MV_USB_DEVICE

    # 1 枚举设备 | en:Enum device
    ret = MvCamera.MV_CC_EnumDevices(tlayerType, deviceList)
    if ret != 0:
        print("enum devices fail! ret[0x%x]" % ret)
        GRAB_RUN = False
        return
    if deviceList.nDeviceNum == 0:
        print("find no device!")
        GRAB_RUN = False
        return
    print("Find %d devices!" % deviceList.nDeviceNum)
    # 打印设备详情
    printDeviceInfo(deviceList)

    # 2 打开
    # 2.1 创建相机实例 | en:Creat Camera Object
    DEVICE_NUM = deviceList.nDeviceNum
    for i in range(0, DEVICE_NUM):
        CAM_LIST.append(MvCamera())
        # ch:选择设备并创建句柄| en:Select device and create handle
        stDeviceList = cast(deviceList.pDeviceInfo[int(i)], POINTER(MV_CC_DEVICE_INFO)).contents
        ret = CAM_LIST[i].MV_CC_CreateHandle(stDeviceList)
        if ret != 0:
            print("create handle fail! ret[0x%x]" % ret)
            CAM_LIST.pop()  # 移除未成功创建的设备
            continue

        # 2.2 打开设备 | en:Open device
        ret = CAM_LIST[i].MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)
        if ret != 0:
            print("open device fail! ret[0x%x]" % ret)
            CAM_LIST.pop()  # 移除未成功打开的设备
            continue

        ret = CAM_LIST[i].MV_CC_SetEnumValue("ExposureAuto", MV_EXPOSURE_AUTO_MODE_CONTINUOUS)
        if ret != 0:
            print("set ExposureAuto fail! ret[0x%x]" % ret)
            CAM_LIST.pop()  # 移除未成功设置的设备
            continue

        ret = CAM_LIST[i].MV_CC_SetEnumValue("GainAuto", MV_GAIN_MODE_CONTINUOUS)
        if ret != 0:
            print("set GainAuto fail! ret[0x%x]" % ret)
            CAM_LIST.pop()  # 移除未成功设置的设备
            continue

        ret = CAM_LIST[i].MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_ON)
        if ret != 0:
            print('Enable trigger failed! [{0:#X}]'.format(ret))
            CAM_LIST.pop()  # 移除未成功设置的设备
            continue

        ret = CAM_LIST[i].MV_CC_SetEnumValue('TriggerSource', MV_TRIGGER_SOURCE_SOFTWARE)
        if ret != 0:
            print('Set trigger source failed! [{0:#X}]'.format(ret))
            CAM_LIST.pop()  # 移除未成功设置的设备
            continue

        stStringValue = MVCC_STRINGVALUE()
        memset(byref(stStringValue), 0, sizeof(MVCC_STRINGVALUE))
        ret = CAM_LIST[i].MV_CC_GetStringValue("DeviceUserID", stStringValue)
        if ret != 0:
            print("获取 string 型数据 %s 失败 ! 报错码 ret[0x%x]" % ("DeviceUserID", ret))
            CAM_LIST.pop()  # 移除未成功获取设备ID的设备
            continue
        device_user_id = bytes.decode(stStringValue.chCurValue)
        print("device_user_id", device_user_id)

        USER_DATA_PY.append({
            "cam": CAM_LIST[i],
            "device_idx": i
        })

        # 默认回调函数
        call_back_fun = None
        user_data = cast(pointer(py_object(i)), c_void_p)
        if device_user_id == DEVICE_USER_ID_FRONT:
            call_back_fun = CALL_BACK_FUN_0
        elif device_user_id == DEVICE_USER_ID_BACK:
            call_back_fun = CALL_BACK_FUN_1
        else:
            print(f"Warning: Unknown device_user_id {device_user_id}. Using default callback.")
            call_back_fun = CALL_BACK_FUN_0  # 默认使用第一个回调函数

        if call_back_fun is None:
            print("Error: No callback function assigned.")
            CAM_LIST.pop()  # 移除未成功设置回调的设备
            continue

        ret = CAM_LIST[i].MV_CC_RegisterImageCallBackEx(call_back_fun, user_data)
        if ret != 0:
            print('Register callback failed! [{0:#X}]'.format(ret))
            CAM_LIST.pop()  # 移除未成功注册回调的设备
            continue

        hThreadHandle = threading.Thread(target=add_trigger_event_thread, args=(CAM_LIST[i], i))
        hThreadHandle.start()

        ret = CAM_LIST[i].MV_CC_StartGrabbing()
        if ret != 0:
            print('Start grabbing failed! [{0:#X}]'.format(ret))
            CAM_LIST.pop()  # 移除未成功开始抓取的设备
            continue

    DEVICE_NUM = len(CAM_LIST)  # 更新设备数量

        # ret = CAM_LIST[i].MV_CC_SetEnumValue('TriggerActivation', 3)
        # if ret != 0:
        #     print('Set trigger source failed! [{0:#X}]'.format(ret))
        #     GRAB_RUN = False
        #     return False

        # stStringValue = MVCC_STRINGVALUE()
        # memset(byref(stStringValue), 0, sizeof(MVCC_STRINGVALUE))
        # ret = CAM_LIST[i].MV_CC_GetStringValue("DeviceUserID", stStringValue)
        # if ret != 0:
        #     print("获取 string 型数据 %s 失败 ! 报错码 ret[0x%x]" % ("DeviceUserID", ret))
        #     GRAB_RUN = False
        #     return False
        # device_user_id = bytes.decode(stStringValue.chCurValue)
        # print("device_user_id", device_user_id)
        #
        # USER_DATA_PY.append({
        #     "cam": CAM_LIST[i],
        #     "device_idx": i
        # })
        # # user_data = cast(pointer(py_object(i)), c_void_p)
        # # user_data = cast(pointer(c_int(i)), c_void_p)
        # # print("pass callback arg,i:",i)
        # # user_data = pointer(c_int(i))
        #
        # # device_idx = (c_int)()
        # # device_idx = i
        # # print("pass callback arg,device_idx:",device_idx)
        # # user_data = cast(pointer(c_int(device_idx)), c_void_p)
        # user_data = cast(pointer(py_object(i)), c_void_p)
        # if device_user_id == DEVICE_USER_ID_FRONT:
        #     call_back_fun = CALL_BACK_FUN_0
        # elif device_user_id == DEVICE_USER_ID_BACK:
        #     call_back_fun = CALL_BACK_FUN_1
        # ret = CAM_LIST[i].MV_CC_RegisterImageCallBackEx(call_back_fun, user_data)
        # if ret != 0:
        #     print('Register callback failed! [{0:#X}]'.format(ret))
        #     GRAB_RUN = False
        #     return False
        #
        # hThreadHandle = threading.Thread(target=add_trigger_event_thread, args=(CAM_LIST[i], i))
        # hThreadHandle.start()
        #
        # # try:
        # # except:
        # #     print ("error: unable to start thread")
        #
        # ret = CAM_LIST[i].MV_CC_StartGrabbing()
        # if ret != 0:
        #     print('Start grabbing failed! [{0:#X}]'.format(ret))
        #     GRAB_RUN = False
        #     return False


def stop():
    global GRAB_RUN
    global CAM_LIST
    global DEVICE_NUM
    for i in range(0, DEVICE_NUM):
        if i >= len(CAM_LIST):
            print(f"Warning: CAM_LIST index {i} out of range. Skipping this device.")
            continue
        # 5 关闭
        # 5.1 停止取流 | en:Stop grab image
        print("stop grabbing device index[%d]" % i)
        ret = CAM_LIST[i].MV_CC_StopGrabbing()
        if ret != 0:
            print("stop grabbing fail! ret[0x%x]" % ret)
            continue

        # 5.2 关闭设备 | Close device
        ret = CAM_LIST[i].MV_CC_CloseDevice()
        if ret != 0:
            print("close device fail! ret[0x%x]" % ret)
            continue

        # 6 销毁句柄 | Destroy handle
        ret = CAM_LIST[i].MV_CC_DestroyHandle()
        if ret != 0:
            print("destroy handle fail! ret[0x%x]" % ret)
            continue
    GRAB_RUN = False


start()
print("press a key to stop grabbing.")
press_any_key_exit()
stop()
