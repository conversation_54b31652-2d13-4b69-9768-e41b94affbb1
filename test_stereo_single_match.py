#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试立体匹配单次执行的脚本
验证点击立体匹配按钮只执行一次匹配
"""

import sys
import time
import threading
from unittest.mock import Mock, MagicMock
import numpy as np

def test_single_stereo_match():
    """测试单次立体匹配功能"""
    print("=== 测试单次立体匹配功能 ===")
    
    # 模拟立体视觉处理器
    class MockStereoVisionProcessor:
        def __init__(self):
            self.calibration_available = True
            self.stereo_available = True
            self.processing = False
            self.process_count = 0
            
        def process_stereo_pair(self, left_img, right_img):
            """模拟立体处理"""
            self.process_count += 1
            print(f"  执行立体匹配 #{self.process_count}")
            
            # 模拟处理时间
            time.sleep(0.1)
            
            # 返回模拟结果
            return {
                'disparity': np.random.rand(100, 100),
                'depth': np.random.rand(100, 100) * 1000 + 1000,  # 1-2米深度
                'depth_color': np.random.randint(0, 255, (100, 100, 3)),
                'points_3d': np.random.rand(100, 100, 3),
                'left_rectified': left_img,
                'right_rectified': right_img
            }
        
        def start_processing(self):
            """启动持续处理"""
            self.processing = True
            print("  启动持续处理模式")
        
        def stop_processing(self):
            """停止持续处理"""
            self.processing = False
            print("  停止持续处理模式")
    
    # 模拟图像
    left_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    right_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    
    # 模拟单次立体匹配函数
    def mock_perform_single_stereo_match(left_img, right_img):
        """模拟单次立体匹配"""
        stereo_processor = MockStereoVisionProcessor()
        
        print("开始单次立体匹配处理...")
        result = stereo_processor.process_stereo_pair(left_img, right_img)
        
        if result is not None:
            print("单次立体匹配完成")
            depth = result['depth']
            valid_depth_count = np.sum(depth > 0)
            total_pixels = depth.shape[0] * depth.shape[1]
            coverage = (valid_depth_count / total_pixels) * 100
            
            print(f"深度图统计:")
            print(f"  - 有效像素: {valid_depth_count:,} / {total_pixels:,} ({coverage:.1f}%)")
            
            return True
        else:
            print("立体匹配处理失败")
            return False
    
    # 测试1: 单次执行
    print("\n1. 测试单次立体匹配执行")
    success = mock_perform_single_stereo_match(left_image, right_image)
    assert success, "单次立体匹配应该成功"
    
    # 测试2: 多次点击，每次都是独立执行
    print("\n2. 测试多次点击立体匹配按钮")
    for i in range(3):
        print(f"  第 {i+1} 次点击:")
        success = mock_perform_single_stereo_match(left_image, right_image)
        assert success, f"第 {i+1} 次立体匹配应该成功"
    
    print("✓ 单次立体匹配功能测试通过")

def test_continuous_vs_single_mode():
    """测试持续模式与单次模式的区别"""
    print("\n=== 测试持续模式与单次模式的区别 ===")
    
    class MockStereoProcessor:
        def __init__(self):
            self.processing = False
            self.process_count = 0
            
        def start_processing(self):
            self.processing = True
            
        def stop_processing(self):
            self.processing = False
            
        def process_stereo_pair(self, left_img, right_img):
            self.process_count += 1
            return {"result": "success"}
    
    processor = MockStereoProcessor()
    
    # 测试持续模式
    print("\n1. 测试持续模式")
    processor.start_processing()
    assert processor.processing == True, "持续模式应该启动"
    
    # 模拟持续处理
    for i in range(5):
        processor.process_stereo_pair(None, None)
    
    print(f"  持续模式处理了 {processor.process_count} 次")
    processor.stop_processing()
    assert processor.processing == False, "持续模式应该停止"
    
    # 测试单次模式
    print("\n2. 测试单次模式")
    initial_count = processor.process_count
    
    # 单次处理（不启动持续模式）
    processor.process_stereo_pair(None, None)
    
    single_count = processor.process_count - initial_count
    print(f"  单次模式处理了 {single_count} 次")
    assert single_count == 1, "单次模式应该只处理一次"
    assert processor.processing == False, "单次模式不应该启动持续处理"
    
    print("✓ 持续模式与单次模式区别测试通过")

def test_manual_stereo_match_logic():
    """测试手动立体匹配的逻辑"""
    print("\n=== 测试手动立体匹配逻辑 ===")
    
    # 模拟全局变量
    last_trigger_images = {"left": None, "right": None}
    
    def mock_get_camera_image(camera_id):
        """模拟获取相机图像"""
        return np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    
    def mock_manual_stereo_match():
        """模拟手动立体匹配函数"""
        left_img = None
        right_img = None
        
        # 检查是否有触发图像
        if (last_trigger_images["left"] is not None and 
            last_trigger_images["right"] is not None):
            left_img = last_trigger_images["left"]
            right_img = last_trigger_images["right"]
            print("使用触发图像进行立体匹配...")
            return "trigger_images"
        else:
            # 获取当前实时图像
            left_img = mock_get_camera_image(0)
            right_img = mock_get_camera_image(1)
            
            if left_img is None or right_img is None:
                print("无法获取有效的立体图像对")
                return "no_images"
            
            print("使用当前实时图像进行立体匹配...")
            return "live_images"
    
    # 测试1: 无触发图像时使用实时图像
    print("\n1. 测试无触发图像时的行为")
    result = mock_manual_stereo_match()
    assert result == "live_images", "应该使用实时图像"
    
    # 测试2: 有触发图像时优先使用触发图像
    print("\n2. 测试有触发图像时的行为")
    last_trigger_images["left"] = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    last_trigger_images["right"] = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    
    result = mock_manual_stereo_match()
    assert result == "trigger_images", "应该优先使用触发图像"
    
    print("✓ 手动立体匹配逻辑测试通过")

def test_stereo_status_tracking():
    """测试立体匹配状态跟踪"""
    print("\n=== 测试立体匹配状态跟踪 ===")
    
    # 模拟状态变量
    stereo_enabled = False
    stereo_processor = None
    
    class MockProcessor:
        def __init__(self):
            self.calibration_available = True
            self.stereo_available = True
    
    def mock_get_stereo_status():
        """模拟获取立体匹配状态"""
        status_info = []
        status_info.append("=== 立体匹配状态 ===")
        
        if stereo_processor is None:
            status_info.append("立体视觉处理器: 未初始化")
        else:
            status_info.append("立体视觉处理器: 已初始化")
            status_info.append(f"标定状态: {'已加载' if stereo_processor.calibration_available else '未加载'}")
            status_info.append(f"立体匹配器: {'可用' if stereo_processor.stereo_available else '不可用'}")
        
        status_info.append(f"持续匹配状态: {'运行中' if stereo_enabled else '已停止'}")
        
        return "\n".join(status_info)
    
    # 测试1: 未初始化状态
    print("\n1. 测试未初始化状态")
    status = mock_get_stereo_status()
    assert "未初始化" in status, "应该显示未初始化状态"
    assert "已停止" in status, "应该显示已停止状态"
    
    # 测试2: 已初始化状态
    print("\n2. 测试已初始化状态")
    stereo_processor = MockProcessor()
    status = mock_get_stereo_status()
    assert "已初始化" in status, "应该显示已初始化状态"
    assert "已加载" in status, "应该显示标定已加载"
    assert "可用" in status, "应该显示立体匹配器可用"
    
    # 测试3: 运行状态
    print("\n3. 测试运行状态")
    stereo_enabled = True
    status = mock_get_stereo_status()
    assert "运行中" in status, "应该显示运行中状态"
    
    print("✓ 立体匹配状态跟踪测试通过")

def main():
    """主测试函数"""
    print("=== 立体匹配单次执行验证测试 ===\n")
    
    try:
        # 测试1: 单次立体匹配功能
        test_single_stereo_match()
        
        # 测试2: 持续模式与单次模式的区别
        test_continuous_vs_single_mode()
        
        # 测试3: 手动立体匹配逻辑
        test_manual_stereo_match_logic()
        
        # 测试4: 状态跟踪
        test_stereo_status_tracking()
        
        print("\n" + "="*60)
        print("✓ 所有测试通过！")
        print("✓ 立体匹配单次执行功能验证成功")
        print("✓ 点击立体匹配按钮现在只执行一次匹配")
        print("✓ 用户可以选择单次匹配或持续匹配模式")
        print("="*60)
        
        return True
        
    except AssertionError as e:
        print(f"\n❌ 测试失败: {e}")
        return False
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
